<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Exception;

class EncryptionService
{
    protected string $cipher = 'aes-256-cbc';
    protected int $keyLength = 32; // 256 bits
    protected int $ivLength = 16;  // 128 bits
    protected int $saltLength = 16; // 128 bits
    protected int $iterations = 10000; // PBKDF2 iterations

    /**
     * Encrypt a file with password-based encryption.
     */
    public function encryptFile(string $inputPath, string $password, ?string $outputPath = null): string
    {
        try {
            if (!file_exists($inputPath)) {
                throw new Exception("Input file does not exist: {$inputPath}");
            }

            $outputPath = $outputPath ?: $inputPath . '.enc';
            
            // Generate salt and derive key
            $salt = random_bytes($this->saltLength);
            $key = $this->deriveKey($password, $salt);
            $iv = random_bytes($this->ivLength);

            // Read input file
            $plaintext = file_get_contents($inputPath);
            if ($plaintext === false) {
                throw new Exception("Failed to read input file: {$inputPath}");
            }

            // Encrypt the data
            $ciphertext = openssl_encrypt($plaintext, $this->cipher, $key, OPENSSL_RAW_DATA, $iv);
            if ($ciphertext === false) {
                throw new Exception("Encryption failed: " . openssl_error_string());
            }

            // Create encrypted file with salt + iv + ciphertext
            $encryptedData = $salt . $iv . $ciphertext;
            
            if (file_put_contents($outputPath, $encryptedData) === false) {
                throw new Exception("Failed to write encrypted file: {$outputPath}");
            }

            // Clear sensitive data from memory
            sodium_memzero($plaintext);
            sodium_memzero($key);

            Log::info("File encrypted successfully: {$outputPath}");
            return $outputPath;

        } catch (Exception $e) {
            Log::error("File encryption failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Decrypt a file with password-based encryption.
     */
    public function decryptFile(string $inputPath, string $password, ?string $outputPath = null): string
    {
        try {
            if (!file_exists($inputPath)) {
                throw new Exception("Input file does not exist: {$inputPath}");
            }

            $outputPath = $outputPath ?: str_replace('.enc', '', $inputPath);
            
            // Read encrypted file
            $encryptedData = file_get_contents($inputPath);
            if ($encryptedData === false) {
                throw new Exception("Failed to read encrypted file: {$inputPath}");
            }

            // Extract salt, IV, and ciphertext
            $salt = substr($encryptedData, 0, $this->saltLength);
            $iv = substr($encryptedData, $this->saltLength, $this->ivLength);
            $ciphertext = substr($encryptedData, $this->saltLength + $this->ivLength);

            // Derive key from password and salt
            $key = $this->deriveKey($password, $salt);

            // Decrypt the data
            $plaintext = openssl_decrypt($ciphertext, $this->cipher, $key, OPENSSL_RAW_DATA, $iv);
            if ($plaintext === false) {
                throw new Exception("Decryption failed: Invalid password or corrupted data");
            }

            // Write decrypted file
            if (file_put_contents($outputPath, $plaintext) === false) {
                throw new Exception("Failed to write decrypted file: {$outputPath}");
            }

            // Clear sensitive data from memory
            sodium_memzero($plaintext);
            sodium_memzero($key);

            Log::info("File decrypted successfully: {$outputPath}");
            return $outputPath;

        } catch (Exception $e) {
            Log::error("File decryption failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Encrypt file using streaming for large files.
     */
    public function encryptFileStreaming(string $inputPath, string $password, ?string $outputPath = null, ?callable $progressCallback = null): string
    {
        try {
            if (!file_exists($inputPath)) {
                throw new Exception("Input file does not exist: {$inputPath}");
            }

            $outputPath = $outputPath ?: $inputPath . '.enc';
            $inputSize = filesize($inputPath);
            $processedBytes = 0;
            $chunkSize = 8192; // 8KB chunks

            // Generate salt and derive key
            $salt = random_bytes($this->saltLength);
            $key = $this->deriveKey($password, $salt);
            $iv = random_bytes($this->ivLength);

            $inputHandle = fopen($inputPath, 'rb');
            $outputHandle = fopen($outputPath, 'wb');

            if (!$inputHandle || !$outputHandle) {
                throw new Exception("Failed to open files for streaming encryption");
            }

            try {
                // Write salt and IV to output file
                fwrite($outputHandle, $salt . $iv);

                // Initialize encryption context
                $encryptionContext = openssl_cipher_iv_length($this->cipher);
                
                while (!feof($inputHandle)) {
                    $chunk = fread($inputHandle, $chunkSize);
                    if ($chunk !== false && strlen($chunk) > 0) {
                        $encryptedChunk = openssl_encrypt($chunk, $this->cipher, $key, OPENSSL_RAW_DATA, $iv);
                        fwrite($outputHandle, $encryptedChunk);
                        
                        $processedBytes += strlen($chunk);
                        if ($progressCallback && $inputSize > 0) {
                            $progress = ($processedBytes / $inputSize) * 100;
                            $progressCallback(min(100, round($progress, 2)));
                        }
                    }
                }

            } finally {
                fclose($inputHandle);
                fclose($outputHandle);
                sodium_memzero($key);
            }

            Log::info("File encrypted with streaming: {$outputPath}");
            return $outputPath;

        } catch (Exception $e) {
            Log::error("Streaming encryption failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Derive encryption key from password using PBKDF2.
     */
    protected function deriveKey(string $password, string $salt): string
    {
        $key = hash_pbkdf2('sha256', $password, $salt, $this->iterations, $this->keyLength, true);
        
        if (strlen($key) !== $this->keyLength) {
            throw new Exception("Key derivation failed: Invalid key length");
        }

        return $key;
    }

    /**
     * Verify if a file is encrypted by this service.
     */
    public function isEncrypted(string $filePath): bool
    {
        try {
            if (!file_exists($filePath)) {
                return false;
            }

            $fileSize = filesize($filePath);
            
            // File must be at least salt + iv length
            if ($fileSize < ($this->saltLength + $this->ivLength)) {
                return false;
            }

            // Additional checks could be added here (magic bytes, etc.)
            return true;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get encryption metadata for a file.
     */
    public function getEncryptionMetadata(string $filePath): array
    {
        try {
            if (!$this->isEncrypted($filePath)) {
                throw new Exception("File is not encrypted: {$filePath}");
            }

            return [
                'cipher' => $this->cipher,
                'key_length' => $this->keyLength,
                'iv_length' => $this->ivLength,
                'salt_length' => $this->saltLength,
                'iterations' => $this->iterations,
                'file_size' => filesize($filePath),
                'encrypted_at' => filemtime($filePath)
            ];

        } catch (Exception $e) {
            Log::error("Failed to get encryption metadata: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Securely delete a file by overwriting it.
     */
    public function secureDelete(string $filePath): bool
    {
        try {
            if (!file_exists($filePath)) {
                return true;
            }

            $fileSize = filesize($filePath);
            $handle = fopen($filePath, 'r+b');
            
            if (!$handle) {
                return false;
            }

            // Overwrite with random data
            fseek($handle, 0);
            $randomData = random_bytes(min($fileSize, 1024 * 1024)); // Max 1MB at a time
            
            for ($i = 0; $i < $fileSize; $i += strlen($randomData)) {
                fwrite($handle, $randomData);
            }

            fclose($handle);
            
            // Finally delete the file
            return unlink($filePath);

        } catch (Exception $e) {
            Log::error("Secure delete failed: " . $e->getMessage());
            return false;
        }
    }
}
