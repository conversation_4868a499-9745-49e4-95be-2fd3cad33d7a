<?php

namespace App\Services;

use App\Models\BackupJob;
use App\Models\BackupLog;
use App\Models\BackupServer;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Exception;

class RetentionService
{
    protected ConnectionService $connectionService;

    public function __construct(ConnectionService $connectionService)
    {
        $this->connectionService = $connectionService;
    }

    /**
     * Apply retention policy to a backup job.
     */
    public function applyRetentionPolicy(BackupJob $job): array
    {
        try {
            if (!$job->retention_policy_days) {
                Log::info("No retention policy set for job: {$job->name}");
                return ['deleted' => 0, 'kept' => 0];
            }

            $backupsToDelete = $this->getBackupsToDelete($job);
            $deletedCount = $this->deleteOldBackups($backupsToDelete);
            $keptCount = $job->backupLogs()->where('status', 'completed')->count() - $deletedCount;

            Log::info("Retention policy applied for job {$job->name}: {$deletedCount} deleted, {$keptCount} kept");

            return [
                'deleted' => $deletedCount,
                'kept' => $keptCount,
            ];

        } catch (Exception $e) {
            Log::error("Failed to apply retention policy for job {$job->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get backups that should be deleted based on retention policy.
     */
    public function getBackupsToDelete(BackupJob $job): Collection
    {
        if (!$job->retention_policy_days) {
            return collect();
        }

        $cutoffDate = Carbon::now()->subDays($job->retention_policy_days);

        return $job->backupLogs()
            ->where('status', 'completed')
            ->where('completed_at', '<', $cutoffDate)
            ->orderBy('completed_at', 'asc')
            ->get();
    }

    /**
     * Delete old backup files and update database records.
     */
    public function deleteOldBackups(Collection $backups): int
    {
        $deletedCount = 0;

        foreach ($backups as $backup) {
            try {
                // Delete the physical backup file
                if ($this->deleteBackupFile($backup)) {
                    // Mark the backup log as deleted
                    $backup->update([
                        'status' => 'deleted',
                        'metadata' => array_merge($backup->metadata ?? [], [
                            'deleted_at' => Carbon::now()->toISOString(),
                            'deleted_by' => 'retention_policy',
                        ]),
                    ]);

                    $deletedCount++;
                    Log::info("Deleted old backup: {$backup->backup_path}");
                } else {
                    Log::warning("Failed to delete backup file: {$backup->backup_path}");
                }

            } catch (Exception $e) {
                Log::error("Error deleting backup {$backup->id}: " . $e->getMessage());
            }
        }

        return $deletedCount;
    }

    /**
     * Calculate storage usage for a backup server.
     */
    public function calculateStorageUsage(BackupServer $server): array
    {
        try {
            $totalSize = 0;
            $fileCount = 0;
            $backupsByJob = [];

            // Get all completed backups for this server
            $backups = BackupLog::whereHas('backupJob', function ($query) use ($server) {
                $query->where('backup_server_id', $server->id);
            })
            ->where('status', 'completed')
            ->get();

            foreach ($backups as $backup) {
                $jobName = $backup->backupJob->name;
                
                if (!isset($backupsByJob[$jobName])) {
                    $backupsByJob[$jobName] = [
                        'count' => 0,
                        'total_size' => 0,
                        'latest_backup' => null,
                    ];
                }

                $backupsByJob[$jobName]['count']++;
                $backupsByJob[$jobName]['total_size'] += $backup->backup_size_bytes ?? 0;
                
                if (!$backupsByJob[$jobName]['latest_backup'] || 
                    $backup->completed_at > $backupsByJob[$jobName]['latest_backup']) {
                    $backupsByJob[$jobName]['latest_backup'] = $backup->completed_at;
                }

                $totalSize += $backup->backup_size_bytes ?? 0;
                $fileCount++;
            }

            return [
                'total_size_bytes' => $totalSize,
                'total_size_formatted' => $this->formatBytes($totalSize),
                'file_count' => $fileCount,
                'backups_by_job' => $backupsByJob,
                'server_name' => $server->name,
                'calculated_at' => Carbon::now()->toISOString(),
            ];

        } catch (Exception $e) {
            Log::error("Failed to calculate storage usage for server {$server->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get retention policy recommendations.
     */
    public function getRetentionRecommendations(BackupJob $job): array
    {
        $logs = $job->backupLogs()->where('status', 'completed')->get();
        
        if ($logs->isEmpty()) {
            return [
                'recommendation' => 'No completed backups found',
                'suggested_retention_days' => 30,
            ];
        }

        $averageSize = $logs->avg('backup_size_bytes') ?? 0;
        $totalSize = $logs->sum('backup_size_bytes') ?? 0;
        $oldestBackup = $logs->min('completed_at');
        $newestBackup = $logs->max('completed_at');
        
        $daysBetween = Carbon::parse($oldestBackup)->diffInDays(Carbon::parse($newestBackup));
        
        // Simple recommendation logic
        if ($averageSize > 1024 * 1024 * 1024) { // > 1GB
            $suggestedDays = 14; // Keep 2 weeks for large backups
        } elseif ($averageSize > 100 * 1024 * 1024) { // > 100MB
            $suggestedDays = 30; // Keep 1 month for medium backups
        } else {
            $suggestedDays = 90; // Keep 3 months for small backups
        }

        return [
            'current_retention_days' => $job->retention_policy_days,
            'suggested_retention_days' => $suggestedDays,
            'total_backups' => $logs->count(),
            'average_size_bytes' => $averageSize,
            'average_size_formatted' => $this->formatBytes($averageSize),
            'total_size_bytes' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'oldest_backup' => $oldestBackup,
            'newest_backup' => $newestBackup,
            'days_of_history' => $daysBetween,
        ];
    }

    /**
     * Delete a backup file from the backup server.
     */
    protected function deleteBackupFile(BackupLog $backup): bool
    {
        try {
            if (!$backup->backup_path) {
                Log::warning("No backup path specified for backup log {$backup->id}");
                return false;
            }

            $backupServer = $backup->backupJob->backupServer;
            $filesystem = $this->connectionService->connectToBackupServer($backupServer);
            
            // Extract relative path from full backup path
            $relativePath = str_replace($backupServer->base_directory, '', $backup->backup_path);
            $relativePath = ltrim($relativePath, '/');

            if ($filesystem->fileExists($relativePath)) {
                $filesystem->delete($relativePath);
                return true;
            } else {
                Log::warning("Backup file not found: {$backup->backup_path}");
                return false;
            }

        } catch (Exception $e) {
            Log::error("Failed to delete backup file {$backup->backup_path}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Format bytes into human readable format.
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
