import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, DollarSign, Plus } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface Expense {
    id: number;
    name: string;
    expense_date: string;
    value: string;
    description: string | null;
}

interface MonthCalendarProps {
    expenses: Expense[];
    currentMonth: number;
    currentYear: number;
    onMonthChange: (month: number, year: number) => void;
}

const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
];

const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function MonthCalendar({ 
    expenses, 
    currentMonth, 
    currentYear, 
    onMonthChange 
}: MonthCalendarProps) {
    const formatCurrency = (value: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(parseFloat(value));
    };

    // Group expenses by date
    const expensesByDate = expenses.reduce((acc, expense) => {
        const date = expense.expense_date;
        if (!acc[date]) {
            acc[date] = [];
        }
        acc[date].push(expense);
        return acc;
    }, {} as Record<string, Expense[]>);

    // Get calendar days for the current month
    const getCalendarDays = () => {
        const firstDay = new Date(currentYear, currentMonth - 1, 1);
        const lastDay = new Date(currentYear, currentMonth, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());
        
        const days = [];
        const currentDate = new Date(startDate);
        
        // Generate 42 days (6 weeks) for consistent calendar layout
        for (let i = 0; i < 42; i++) {
            days.push(new Date(currentDate));
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        return days;
    };

    const handlePrevMonth = () => {
        if (currentMonth === 1) {
            onMonthChange(12, currentYear - 1);
        } else {
            onMonthChange(currentMonth - 1, currentYear);
        }
    };

    const handleNextMonth = () => {
        if (currentMonth === 12) {
            onMonthChange(1, currentYear + 1);
        } else {
            onMonthChange(currentMonth + 1, currentYear);
        }
    };

    const isToday = (date: Date) => {
        const today = new Date();
        return date.toDateString() === today.toDateString();
    };

    const isCurrentMonth = (date: Date) => {
        return date.getMonth() === currentMonth - 1 && date.getFullYear() === currentYear;
    };

    const getDayExpenses = (date: Date) => {
        const dateString = date.toISOString().split('T')[0];
        return expensesByDate[dateString] || [];
    };

    const getDayTotal = (date: Date) => {
        const dayExpenses = getDayExpenses(date);
        return dayExpenses.reduce((total, expense) => total + parseFloat(expense.value), 0);
    };

    const calendarDays = getCalendarDays();

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                        <DollarSign className="w-5 h-5 mr-2" />
                        {monthNames[currentMonth - 1]} {currentYear}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" onClick={handlePrevMonth}>
                            <ChevronLeft className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={handleNextMonth}>
                            <ChevronRight className="w-4 h-4" />
                        </Button>
                        <Button size="sm" asChild>
                            <Link href="/expenses/create">
                                <Plus className="w-4 h-4 mr-1" />
                                Add
                            </Link>
                        </Button>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1">
                    {/* Day headers */}
                    {dayNames.map((day) => (
                        <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground border-b">
                            {day}
                        </div>
                    ))}
                    
                    {/* Calendar days */}
                    {calendarDays.map((date, index) => {
                        const dayExpenses = getDayExpenses(date);
                        const dayTotal = getDayTotal(date);
                        const isCurrentMonthDay = isCurrentMonth(date);
                        const isTodayDate = isToday(date);
                        
                        return (
                            <div
                                key={index}
                                className={`min-h-[120px] p-2 border border-border/50 ${
                                    isCurrentMonthDay 
                                        ? 'bg-background' 
                                        : 'bg-muted/30'
                                } ${
                                    isTodayDate 
                                        ? 'ring-2 ring-primary ring-inset' 
                                        : ''
                                } hover:bg-muted/50 transition-colors`}
                            >
                                <div className="flex items-center justify-between mb-1">
                                    <span className={`text-sm ${
                                        isCurrentMonthDay 
                                            ? isTodayDate 
                                                ? 'font-bold text-primary' 
                                                : 'text-foreground'
                                            : 'text-muted-foreground'
                                    }`}>
                                        {date.getDate()}
                                    </span>
                                    {dayTotal > 0 && (
                                        <Badge variant="secondary" className="text-xs px-1 py-0">
                                            {formatCurrency(dayTotal.toString())}
                                        </Badge>
                                    )}
                                </div>
                                
                                {/* Expenses for this day */}
                                <div className="space-y-1 overflow-hidden">
                                    {dayExpenses.slice(0, 3).map((expense) => (
                                        <Link
                                            key={expense.id}
                                            href={`/expenses/${expense.id}`}
                                            className="block"
                                        >
                                            <div className="text-xs p-1.5 bg-primary/10 text-primary rounded-md truncate hover:bg-primary/20 transition-all duration-200 hover:scale-[1.02] border border-transparent hover:border-primary/20">
                                                <div className="font-medium truncate">{expense.name}</div>
                                                <div className="text-xs opacity-75 font-mono">
                                                    {formatCurrency(expense.value)}
                                                </div>
                                            </div>
                                        </Link>
                                    ))}

                                    {dayExpenses.length > 3 && (
                                        <div className="text-xs text-muted-foreground text-center py-1 bg-muted/50 rounded-md">
                                            +{dayExpenses.length - 3} more
                                        </div>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                </div>
                
                {/* Legend */}
                <div className="flex items-center justify-center mt-4 space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                        <div className="w-3 h-3 bg-primary/10 rounded mr-2"></div>
                        Expense
                    </div>
                    <div className="flex items-center">
                        <div className="w-3 h-3 border-2 border-primary rounded mr-2"></div>
                        Today
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
