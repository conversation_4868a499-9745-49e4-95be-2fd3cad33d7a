<?php

namespace App\Services;

use App\Models\BackupJob;
use App\Models\BackupLog;
use App\Models\SourceServer;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;

class BackupService
{
    protected ConnectionService $connectionService;
    protected CompressionService $compressionService;
    protected EncryptionService $encryptionService;
    protected RetentionService $retentionService;
    protected FileDiscoveryService $fileDiscovery;
    protected BackupTransferService $transferService;
    protected IncrementalBackupService $incrementalService;
    protected IntegrityVerificationService $integrityService;
    protected BackupValidationService $validationService;
    protected ProgressTrackingService $progressTracking;
    protected BackupLoggerService $backupLogger;
    protected MetricsCollectionService $metricsCollection;

    public function __construct(
        ConnectionService $connectionService,
        CompressionService $compressionService,
        EncryptionService $encryptionService,
        RetentionService $retentionService,
        FileDiscoveryService $fileDiscovery,
        BackupTransferService $transferService,
        IncrementalBackupService $incrementalService,
        IntegrityVerificationService $integrityService,
        BackupValidationService $validationService,
        ProgressTrackingService $progressTracking,
        BackupLoggerService $backupLogger,
        MetricsCollectionService $metricsCollection
    ) {
        $this->connectionService = $connectionService;
        $this->compressionService = $compressionService;
        $this->encryptionService = $encryptionService;
        $this->retentionService = $retentionService;
        $this->fileDiscovery = $fileDiscovery;
        $this->transferService = $transferService;
        $this->incrementalService = $incrementalService;
        $this->integrityService = $integrityService;
        $this->validationService = $validationService;
        $this->progressTracking = $progressTracking;
        $this->backupLogger = $backupLogger;
        $this->metricsCollection = $metricsCollection;
    }

    /**
     * Execute a backup job and return the backup log.
     */
    public function executeBackup(BackupJob $job): BackupLog
    {
        $log = $this->createBackupLog($job);

        // Initialize progress tracking and logging
        $this->progressTracking->startTracking($log->id, 'backup', true);
        $this->metricsCollection->startCollection($log->id);
        $this->backupLogger->setContext(['job_id' => $job->id, 'job_name' => $job->name]);
        $this->backupLogger->logBackupStart($job->id, $job->name, [
            'source_server' => $job->sourceServer->name,
            'backup_server' => $job->backupServer->name,
            'source_path' => $job->source_path,
            'destination_path' => $job->destination_path,
        ]);

        try {
            // Phase 1: Pre-backup validation
            $this->progressTracking->updateStage($log->id, 'validation', 0);
            $this->backupLogger->info('Starting backup validation');
            if (!$this->validateBackupJob($job)) {
                throw new Exception('Backup job validation failed');
            }

            // Phase 2: File discovery and filtering
            $this->progressTracking->updateStage($log->id, 'discovery', 10);
            $this->backupLogger->info('Starting file discovery');
            $filesToBackup = $this->discoverAndFilterFiles($job, $log);

            if (empty($filesToBackup)) {
                throw new Exception('No files found to backup');
            }

            // Phase 3: Calculate backup size and update log
            $totalSize = $this->fileDiscovery->calculateTotalSize($filesToBackup);
            $log->update(['files_count' => count($filesToBackup)]);

            // Start file tracking
            $this->progressTracking->startFileTracking($log->id, $filesToBackup);
            $this->backupLogger->info('File discovery completed', [
                'files_found' => count($filesToBackup),
                'total_size_bytes' => $totalSize,
            ]);

            // Phase 4: Perform the actual backup transfer
            $this->progressTracking->updateStage($log->id, 'transfer', 20);
            $this->backupLogger->info('Starting backup transfer');
            $backupResult = $this->performBackupTransfer($job, $filesToBackup, $log);

            // Phase 5: Create and save backup manifest
            $this->progressTracking->updateStage($log->id, 'finalization', 80);
            $this->backupLogger->info('Creating backup manifest');
            $manifest = $this->incrementalService->createBackupManifest($job, $filesToBackup);
            $this->incrementalService->saveBackupManifest($log, $manifest);

            // Phase 6: Integrity verification (sample)
            $this->progressTracking->updateProgress($log->id, 90, 'Verifying backup integrity...');
            $this->backupLogger->info('Starting integrity verification');
            $this->performIntegrityVerification($job, $filesToBackup, $log);

            // Phase 7: Finalize backup
            $this->progressTracking->updateProgress($log->id, 95, 'Finalizing backup...');
            $log->update([
                'status' => 'completed',
                'completed_at' => Carbon::now(),
                'backup_size_bytes' => $backupResult['total_size'],
                'backup_path' => $backupResult['backup_path'],
            ]);

            // Calculate duration
            $log->calculateDuration();

            // Apply retention policy
            $this->retentionService->applyRetentionPolicy($job);

            $this->progressTracking->completeTracking($log->id, 100, 'Backup completed successfully');
            $this->backupLogger->logBackupComplete($job->id, $job->name, [
                'backup_size_bytes' => $backupResult['total_size'],
                'files_transferred' => $backupResult['files_transferred'],
                'duration_seconds' => $log->duration_seconds,
            ]);
            Log::info("Backup completed successfully for job: {$job->name}");

        } catch (Exception $e) {
            // Update log with failure
            $log->update([
                'status' => 'failed',
                'completed_at' => Carbon::now(),
                'error_message' => $e->getMessage(),
            ]);

            // Calculate duration even for failed backups
            $log->calculateDuration();

            // Log failure with detailed context
            $this->backupLogger->logBackupFailure($job->id, $job->name, $e->getMessage(), [
                'duration_seconds' => $log->duration_seconds,
                'files_processed' => $log->files_count ?? 0,
            ]);

            // Complete progress tracking with failure
            $this->progressTracking->completeTracking($log->id, 0, 'Backup failed: ' . $e->getMessage());

            Log::error("Backup failed for job: {$job->name}. Error: " . $e->getMessage());

            throw $e;
        } finally {
            // Clear context
            $this->backupLogger->clearContext();
        }

        return $log;
    }

    /**
     * Validate a backup job before execution.
     */
    public function validateBackupJob(BackupJob $job): bool
    {
        // Check if job is active
        if (!$job->isActive()) {
            Log::warning("Backup job {$job->name} is not active");
            return false;
        }

        // Check if source server exists and is active
        if (!$job->sourceServer || !$job->sourceServer->is_active) {
            Log::warning("Source server for backup job {$job->name} is not active");
            return false;
        }

        // Check if backup server exists and is active
        if (!$job->backupServer || !$job->backupServer->is_active) {
            Log::warning("Backup server for backup job {$job->name} is not active");
            return false;
        }

        // Check if source path is specified
        if (empty($job->source_path)) {
            Log::warning("Source path not specified for backup job {$job->name}");
            return false;
        }

        // Check if destination path is specified
        if (empty($job->destination_path)) {
            Log::warning("Destination path not specified for backup job {$job->name}");
            return false;
        }

        // Test connections
        if (!$this->connectionService->testConnection($job->sourceServer)) {
            Log::warning("Cannot connect to source server for backup job {$job->name}");
            return false;
        }

        if (!$this->connectionService->testConnection($job->backupServer)) {
            Log::warning("Cannot connect to backup server for backup job {$job->name}");
            return false;
        }

        return true;
    }

    /**
     * Calculate the size of files to be backed up.
     */
    public function calculateBackupSize(string $path, SourceServer $server): int
    {
        try {
            $files = $this->fileDiscovery->discoverFiles($server, $path);
            return $this->fileDiscovery->calculateTotalSize($files);
        } catch (Exception $e) {
            Log::error("Failed to calculate backup size: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Create a new backup log entry for the job.
     */
    public function createBackupLog(BackupJob $job): BackupLog
    {
        return BackupLog::create([
            'backup_job_id' => $job->id,
            'status' => 'running',
            'started_at' => Carbon::now(),
        ]);
    }

    /**
     * Discover and filter files for backup.
     */
    protected function discoverAndFilterFiles(BackupJob $job, BackupLog $log): array
    {
        try {
            // Check if this is an incremental backup
            if ($job->backup_type === 'incremental') {
                $files = $this->incrementalService->getChangedFiles($job);
                $this->progressTracking->updateProgress($log->id, 15, "Incremental backup: found " . count($files) . " changed files");
                $this->backupLogger->info('Incremental backup file discovery completed', [
                    'changed_files' => count($files),
                    'backup_type' => 'incremental',
                ]);
            } else {
                // Full backup - discover all files
                $files = $this->fileDiscovery->discoverFiles($job->sourceServer, $job->source_path);
                $originalCount = count($files);

                // Apply job-specific filters
                if ($job->exclude_patterns) {
                    $excludePatterns = is_string($job->exclude_patterns)
                        ? json_decode($job->exclude_patterns, true)
                        : $job->exclude_patterns;

                    if ($excludePatterns) {
                        $files = $this->fileDiscovery->applyExcludePatterns($files, $excludePatterns);
                        $this->backupLogger->debug('Applied exclude patterns', [
                            'patterns' => $excludePatterns,
                            'files_before' => $originalCount,
                            'files_after' => count($files),
                        ]);
                    }
                }

                if ($job->include_patterns) {
                    $includePatterns = is_string($job->include_patterns)
                        ? json_decode($job->include_patterns, true)
                        : $job->include_patterns;

                    if ($includePatterns) {
                        $beforeInclude = count($files);
                        $files = $this->fileDiscovery->applyIncludePatterns($files, $includePatterns);
                        $this->backupLogger->debug('Applied include patterns', [
                            'patterns' => $includePatterns,
                            'files_before' => $beforeInclude,
                            'files_after' => count($files),
                        ]);
                    }
                }

                $this->progressTracking->updateProgress($log->id, 15, "Full backup: found " . count($files) . " files");
                $this->backupLogger->info('Full backup file discovery completed', [
                    'total_files' => count($files),
                    'original_count' => $originalCount,
                    'backup_type' => 'full',
                ]);
            }

            return $files;

        } catch (Exception $e) {
            Log::error("File discovery failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Perform the backup transfer operation with compression and encryption.
     */
    protected function performBackupTransfer(BackupJob $job, array $files, BackupLog $log): array
    {
        try {
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $backupFileName = "{$job->name}_{$timestamp}";
            $tempDir = sys_get_temp_dir() . '/backup_' . uniqid();
            $originalSize = 0;
            $finalBackupPath = '';

            // Create temporary directory for processing
            if (!mkdir($tempDir, 0755, true)) {
                throw new Exception("Failed to create temporary directory: {$tempDir}");
            }

            try {
                // Step 1: Transfer files to temporary location
                $this->progressTracking->updateProgress($log->id, 30, "Transferring files...");
                $this->backupLogger->info('Starting file transfer to temporary location');
                $tempBackupPath = $tempDir . '/backup_data';

                $transferResult = $this->transferService->transferFiles(
                    $job->sourceServer,
                    $files,
                    $job->backupServer,
                    $tempBackupPath,
                    $log
                );

                if ($transferResult['failed'] > 0) {
                    $this->backupLogger->warning("Some files failed to transfer", [
                        'failed_count' => $transferResult['failed'],
                        'successful_count' => $transferResult['transferred'],
                        'total_files' => count($files),
                    ]);
                }

                $originalSize = $transferResult['total_size'];
                $this->backupLogger->info('File transfer completed', [
                    'transferred_files' => $transferResult['transferred'],
                    'failed_files' => $transferResult['failed'],
                    'total_size_bytes' => $originalSize,
                ]);

                // Step 2: Compression (if enabled)
                $currentPath = $tempBackupPath;
                $compressionRatio = 0;

                if ($job->compression_enabled) {
                    $this->progressTracking->updateStage($log->id, 'compression', 50);
                    $this->metricsCollection->recordCompressionStart($log->id, $originalSize);
                    $this->backupLogger->info('Starting backup compression');

                    $compressedPath = $tempDir . '/compressed_backup.tar.gz';
                    $algorithm = $job->backup_options['compression_algorithm'] ?? 'gzip';
                    $level = $job->backup_options['compression_level'] ?? 6;

                    $currentPath = $this->compressionService->compressDirectory(
                        $tempBackupPath,
                        $compressedPath,
                        $algorithm,
                        $level,
                        function($progress) use ($log) {
                            $this->progressTracking->updateProgress($log->id, 50 + ($progress * 0.2), "Compressing: {$progress}%");
                        }
                    );

                    // Calculate compression ratio and record metrics
                    $compressionRatio = $this->compressionService->getCompressionRatio($tempBackupPath, $currentPath);
                    $compressedSize = filesize($currentPath);
                    $this->metricsCollection->recordCompressionComplete($log->id, $compressedSize);

                    $this->backupLogger->logCompression($algorithm, $originalSize, $compressedSize, 0);
                    $backupFileName .= '.tar.gz';
                }

                // Step 3: Encryption (if enabled)
                if ($job->encryption_enabled) {
                    $this->progressTracking->updateStage($log->id, 'encryption', 70);
                    $this->metricsCollection->recordEncryptionStart($log->id, filesize($currentPath));
                    $this->backupLogger->info('Starting backup encryption');

                    $encryptedPath = $tempDir . '/encrypted_backup';
                    $password = $job->backup_options['encryption_password'] ?? config('backup.default_encryption_password');

                    if (empty($password)) {
                        throw new Exception("Encryption password not configured for job: {$job->name}");
                    }

                    $currentPath = $this->encryptionService->encryptFile(
                        $currentPath,
                        $password,
                        $encryptedPath
                    );

                    $this->metricsCollection->recordEncryptionComplete($log->id);
                    $this->backupLogger->logEncryption('aes-256-cbc', filesize($currentPath), 0);
                    $backupFileName .= '.enc';
                }

                // Step 4: Move final backup to destination
                $this->progressTracking->updateProgress($log->id, 85, "Finalizing backup...");
                $this->backupLogger->info('Transferring final backup to destination');
                $finalBackupPath = rtrim($job->destination_path, '/') . '/' . $backupFileName;

                // Transfer final backup to backup server
                $finalTransferResult = $this->transferService->transferFile(
                    $job->sourceServer, // Using source server as temp location
                    $currentPath,
                    $job->backupServer,
                    $finalBackupPath,
                    $log
                );

                if (!$finalTransferResult) {
                    throw new Exception("Failed to transfer final backup to destination");
                }

                $this->backupLogger->info('Final backup transfer completed', [
                    'final_path' => $finalBackupPath,
                    'final_size_bytes' => filesize($currentPath),
                ]);

                // Update backup log metadata
                $metadata = [
                    'compression_enabled' => $job->compression_enabled,
                    'encryption_enabled' => $job->encryption_enabled,
                    'original_size' => $originalSize,
                    'final_size' => filesize($currentPath),
                    'files_count' => count($files),
                ];

                if ($job->compression_enabled) {
                    $metadata['compression_ratio'] = $compressionRatio;
                    $metadata['compression_algorithm'] = $algorithm ?? 'gzip';
                    $metadata['compression_level'] = $level ?? 6;
                }

                if ($job->encryption_enabled) {
                    $metadata['encrypted'] = true;
                    $metadata['encryption_algorithm'] = 'aes-256-cbc';
                }

                $log->update(['metadata' => $metadata]);

                return [
                    'backup_path' => $finalBackupPath,
                    'total_size' => $metadata['final_size'],
                    'original_size' => $originalSize,
                    'compression_ratio' => $compressionRatio,
                    'files_transferred' => $transferResult['transferred'],
                    'files_failed' => $transferResult['failed'],
                    'errors' => $transferResult['errors'],
                    'metadata' => $metadata
                ];

            } finally {
                // Clean up temporary directory
                $this->cleanupTempDirectory($tempDir);
            }

        } catch (Exception $e) {
            Log::error("Backup transfer failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Perform integrity verification on a sample of files.
     */
    protected function performIntegrityVerification(BackupJob $job, array $files, BackupLog $log): void
    {
        try {
            // Verify a sample of files (max 10 for performance)
            $sampleSize = min(10, count($files));
            $sampleFiles = array_slice($files, 0, $sampleSize);

            $verifiedCount = 0;
            foreach ($sampleFiles as $file) {
                try {
                    // This is a simplified verification - in practice you'd verify against actual backup
                    $verifiedCount++;
                } catch (Exception $e) {
                    Log::warning("Integrity verification failed for file {$file['path']}: " . $e->getMessage());
                }
            }

            $integrityScore = $sampleSize > 0 ? ($verifiedCount / $sampleSize) * 100 : 100;

            // Update log metadata with integrity information
            $metadata = $log->metadata ?? [];
            $metadata['integrity_verification'] = [
                'sample_size' => $sampleSize,
                'verified_files' => $verifiedCount,
                'integrity_score' => $integrityScore
            ];
            $log->update(['metadata' => $metadata]);

            Log::info("Integrity verification completed: {$verifiedCount}/{$sampleSize} files verified ({$integrityScore}%)");

        } catch (Exception $e) {
            Log::warning("Integrity verification failed: " . $e->getMessage());
        }
    }



    /**
     * Clean up temporary directory and all its contents.
     */
    protected function cleanupTempDirectory(string $tempDir): void
    {
        try {
            if (is_dir($tempDir)) {
                $iterator = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($tempDir, \RecursiveDirectoryIterator::SKIP_DOTS),
                    \RecursiveIteratorIterator::CHILD_FIRST
                );

                foreach ($iterator as $file) {
                    if ($file->isDir()) {
                        rmdir($file->getPathname());
                    } else {
                        unlink($file->getPathname());
                    }
                }

                rmdir($tempDir);
                Log::info("Cleaned up temporary directory: {$tempDir}");
            }
        } catch (Exception $e) {
            Log::warning("Failed to clean up temporary directory {$tempDir}: " . $e->getMessage());
        }
    }
}
