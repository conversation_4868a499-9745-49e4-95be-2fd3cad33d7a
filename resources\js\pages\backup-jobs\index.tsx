import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Search, Plus, MoreHorizontal, Eye, Edit, Trash2, Play, Pause, Calendar, Clock, Server, HardDrive } from 'lucide-react';
import { useState } from 'react';

interface BackupJob {
    id: number;
    name: string;
    source_server: {
        id: number;
        name: string;
        ip_address: string;
    };
    backup_server: {
        id: number;
        name: string;
        protocol: string;
    };
    source_path: string;
    destination_path: string;
    schedule: string;
    status: 'active' | 'paused' | 'disabled';
    last_run?: string;
    next_run?: string;
    compression_enabled: boolean;
    encryption_enabled: boolean;
    description?: string;
    created_at: string;
}

interface Props {
    backupJobs: {
        data: BackupJob[];
        links: any[];
        meta: any;
    };
    sourceServers: Array<{ id: number; name: string }>;
    backupServers: Array<{ id: number; name: string }>;
}

export default function BackupJobsIndex({ backupJobs, sourceServers, backupServers }: Props) {
    const { data, setData, get, processing } = useForm({
        search: '',
        status: '',
        source_server: '',
        backup_server: '',
    });

    const handleSearch = () => {
        get(route('backup-jobs.index'), {
            preserveState: true,
            replace: true,
        });
    };

    const handleDelete = (id: number) => {
        if (confirm('Are you sure you want to delete this backup job?')) {
            router.delete(route('backup-jobs.destroy', id));
        }
    };

    const handleTrigger = (id: number) => {
        if (confirm('Are you sure you want to manually trigger this backup job?')) {
            router.post(route('backup-jobs.trigger', id));
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'default';
            case 'paused': return 'secondary';
            case 'disabled': return 'destructive';
            default: return 'secondary';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active': return Play;
            case 'paused': return Pause;
            case 'disabled': return Pause;
            default: return Pause;
        }
    };

    return (
        <AppLayout>
            <Head title="Backup Jobs" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Backup Jobs</h1>
                        <p className="text-muted-foreground">
                            Manage scheduled backup tasks between source and backup servers
                        </p>
                    </div>
                    <Link href={route('backup-jobs.create')}>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Create Backup Job
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                        <CardDescription>Search and filter backup jobs</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <Input
                                placeholder="Search jobs..."
                                value={data.search}
                                onChange={(e) => setData('search', e.target.value)}
                            />
                            <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="paused">Paused</SelectItem>
                                    <SelectItem value="disabled">Disabled</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={data.source_server} onValueChange={(value) => setData('source_server', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Sources" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Sources</SelectItem>
                                    {sourceServers.map((server) => (
                                        <SelectItem key={server.id} value={server.id.toString()}>
                                            {server.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Select value={data.backup_server} onValueChange={(value) => setData('backup_server', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Destinations" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Destinations</SelectItem>
                                    {backupServers.map((server) => (
                                        <SelectItem key={server.id} value={server.id.toString()}>
                                            {server.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Button onClick={handleSearch} disabled={processing}>
                                <Search className="mr-2 h-4 w-4" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Backup Jobs Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {backupJobs.data.map((job) => {
                        const StatusIcon = getStatusIcon(job.status);
                        return (
                            <Card key={job.id} className="relative">
                                <CardHeader className="pb-3">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center space-x-2">
                                            <Calendar className="h-5 w-5 text-muted-foreground" />
                                            <CardTitle className="text-lg">{job.name}</CardTitle>
                                        </div>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem asChild>
                                                    <Link href={route('backup-jobs.show', job.id)}>
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        View
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem asChild>
                                                    <Link href={route('backup-jobs.edit', job.id)}>
                                                        <Edit className="mr-2 h-4 w-4" />
                                                        Edit
                                                    </Link>
                                                </DropdownMenuItem>
                                                {job.status === 'active' && (
                                                    <DropdownMenuItem onClick={() => handleTrigger(job.id)}>
                                                        <Play className="mr-2 h-4 w-4" />
                                                        Trigger Now
                                                    </DropdownMenuItem>
                                                )}
                                                <DropdownMenuItem 
                                                    onClick={() => handleDelete(job.id)}
                                                    className="text-destructive"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                    <CardDescription>{job.description || 'No description'}</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">Status</span>
                                        <Badge variant={getStatusColor(job.status)}>
                                            <StatusIcon className="mr-1 h-3 w-3" />
                                            {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                                        </Badge>
                                    </div>
                                    
                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-2 text-sm">
                                            <Server className="h-4 w-4 text-muted-foreground" />
                                            <span className="text-muted-foreground">Source:</span>
                                            <span className="font-medium">{job.source_server.name}</span>
                                        </div>
                                        <div className="flex items-center space-x-2 text-sm">
                                            <HardDrive className="h-4 w-4 text-muted-foreground" />
                                            <span className="text-muted-foreground">Destination:</span>
                                            <span className="font-medium">{job.backup_server.name}</span>
                                        </div>
                                    </div>

                                    <div className="space-y-1">
                                        <div className="text-sm">
                                            <span className="text-muted-foreground">Schedule:</span>
                                            <span className="ml-2 font-mono text-xs">{job.schedule}</span>
                                        </div>
                                        <div className="text-sm">
                                            <span className="text-muted-foreground">Path:</span>
                                            <span className="ml-2 font-mono text-xs">{job.source_path}</span>
                                        </div>
                                        {job.next_run && (
                                            <div className="text-sm">
                                                <span className="text-muted-foreground">Next Run:</span>
                                                <span className="ml-2">{new Date(job.next_run).toLocaleString()}</span>
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex space-x-2">
                                        {job.compression_enabled && (
                                            <Badge variant="outline" className="text-xs">Compressed</Badge>
                                        )}
                                        {job.encryption_enabled && (
                                            <Badge variant="outline" className="text-xs">Encrypted</Badge>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {/* Empty State */}
                {backupJobs.data.length === 0 && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No backup jobs found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Create your first backup job to start automated backups.
                            </p>
                            <Link href={route('backup-jobs.create')}>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Create Backup Job
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
