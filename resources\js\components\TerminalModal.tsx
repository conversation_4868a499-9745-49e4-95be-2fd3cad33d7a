import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X, Copy, Download, Terminal, Play, Square } from 'lucide-react';
import { router } from '@inertiajs/react';

interface TerminalModalProps {
    isOpen: boolean;
    onClose: () => void;
    action: {
        id: number;
        name: string;
        command: string;
        source_server: { name: string };
    } | null;
}

interface ExecutionLog {
    timestamp: string;
    type: 'info' | 'success' | 'error' | 'output';
    message: string;
}

export default function TerminalModal({ isOpen, onClose, action }: TerminalModalProps) {
    const [logs, setLogs] = useState<ExecutionLog[]>([]);
    const [isRunning, setIsRunning] = useState(false);
    const [status, setStatus] = useState<'idle' | 'running' | 'completed' | 'failed'>('idle');
    const terminalRef = useRef<HTMLDivElement>(null);

    const addLog = (type: ExecutionLog['type'], message: string) => {
        const newLog: ExecutionLog = {
            timestamp: new Date().toLocaleTimeString(),
            type,
            message,
        };
        setLogs(prev => [...prev, newLog]);
    };

    const scrollToBottom = () => {
        if (terminalRef.current) {
            terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
        }
    };

    useEffect(() => {
        scrollToBottom();
    }, [logs]);

    const runAction = async () => {
        if (!action) return;

        setIsRunning(true);
        setStatus('running');
        setLogs([]);

        addLog('info', `Starting execution on server: ${action.source_server.name}`);
        addLog('info', `Command: ${action.command}`);
        addLog('info', '--- Execution Output ---');

        try {
            // Get CSRF token from the page's meta tag
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            
            if (!csrfToken) {
                throw new Error('CSRF token not found. Please refresh the page.');
            }

            const response = await fetch(`/actions/${action.id}/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                },
                credentials: 'same-origin',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body?.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (reader) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || ''; // Keep incomplete line in buffer

                for (const line of lines) {
                    if (line.trim() && line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.substring(6)); // Remove 'data: ' prefix
                            if (data.type && data.message) {
                                addLog(data.type, data.message);
                            }
                        } catch {
                            // If not valid JSON, treat as plain output
                            addLog('output', line.substring(6));
                        }
                    }
                }
            }

            setStatus('completed');
            addLog('success', 'Command execution completed');
        } catch (error) {
            setStatus('failed');
            addLog('error', `Execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
            setIsRunning(false);
        }
    };

    const copyToClipboard = () => {
        const logText = logs.map(log => `[${log.timestamp}] ${log.message}`).join('\n');
        navigator.clipboard.writeText(logText);
    };

    const downloadLogs = () => {
        const logText = logs.map(log => `[${log.timestamp}] ${log.message}`).join('\n');
        const blob = new Blob([logText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `action-${action?.id}-${new Date().toISOString().split('T')[0]}.log`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const getStatusColor = () => {
        switch (status) {
            case 'running': return 'bg-blue-500';
            case 'completed': return 'bg-green-500';
            case 'failed': return 'bg-red-500';
            default: return 'bg-gray-500';
        }
    };

    const getLogColor = (type: ExecutionLog['type']) => {
        switch (type) {
            case 'success': return 'text-green-400';
            case 'error': return 'text-red-400';
            case 'info': return 'text-blue-400';
            default: return 'text-gray-300';
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-4xl max-h-[80vh] p-0">
                <DialogHeader className="p-6 pb-0">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <Terminal className="h-5 w-5" />
                            <DialogTitle>Execute Action: {action?.name}</DialogTitle>
                            <Badge className={`${getStatusColor()} text-white`}>
                                {status.charAt(0).toUpperCase() + status.slice(1)}
                            </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={copyToClipboard}
                                disabled={logs.length === 0}
                            >
                                <Copy className="h-4 w-4" />
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={downloadLogs}
                                disabled={logs.length === 0}
                            >
                                <Download className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={onClose}>
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                    {action && (
                        <div className="text-sm text-muted-foreground">
                            Server: {action.source_server.name} | Command: {action.command}
                        </div>
                    )}
                </DialogHeader>

                <div className="flex-1 flex flex-col min-h-0 px-6">
                    <div className="flex items-center justify-between py-4">
                        <div className="text-sm text-muted-foreground">
                            {logs.length} log entries
                        </div>
                        <Button
                            onClick={runAction}
                            disabled={isRunning}
                            className="bg-green-600 hover:bg-green-700"
                        >
                            {isRunning ? (
                                <>
                                    <Square className="mr-2 h-4 w-4" />
                                    Running...
                                </>
                            ) : (
                                <>
                                    <Play className="mr-2 h-4 w-4" />
                                    Execute
                                </>
                            )}
                        </Button>
                    </div>

                    <div
                        ref={terminalRef}
                        className="flex-1 bg-black text-white p-4 rounded-md font-mono text-sm overflow-y-auto min-h-[400px] max-h-[500px]"
                    >
                        {logs.length === 0 && !isRunning && (
                            <div className="text-gray-500 text-center py-8">
                                Click "Execute" to run the action
                            </div>
                        )}
                        {logs.map((log, index) => (
                            <div key={index} className="mb-1">
                                <span className="text-gray-500">[{log.timestamp}]</span>{' '}
                                <span className={getLogColor(log.type)}>{log.message}</span>
                            </div>
                        ))}
                        {isRunning && (
                            <div className="text-blue-400 animate-pulse">
                                <span className="text-gray-500">[{new Date().toLocaleTimeString()}]</span> Executing...
                            </div>
                        )}
                    </div>
                </div>

                <div className="p-6 pt-0">
                    <Button variant="outline" onClick={onClose} className="w-full">
                        Close
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
} 