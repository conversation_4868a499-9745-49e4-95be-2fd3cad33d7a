<?php

namespace App\Services;

use App\Models\BackupLog;
use App\Models\BackupJob;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

class BackupValidationService
{
    protected IntegrityVerificationService $integrityService;
    protected IncrementalBackupService $incrementalService;
    protected FileDiscoveryService $fileDiscovery;

    public function __construct(
        IntegrityVerificationService $integrityService,
        IncrementalBackupService $incrementalService,
        FileDiscoveryService $fileDiscovery
    ) {
        $this->integrityService = $integrityService;
        $this->incrementalService = $incrementalService;
        $this->fileDiscovery = $fileDiscovery;
    }

    /**
     * Validate a completed backup.
     */
    public function validateBackup(BackupLog $log): array
    {
        $validation = [
            'valid' => false,
            'backup_log_id' => $log->id,
            'validation_date' => now()->toISOString(),
            'checks' => [],
            'files_verified' => 0,
            'integrity_score' => 0,
            'errors' => [],
            'warnings' => []
        ];

        try {
            // Check 1: Backup status
            $validation['checks']['status'] = $this->validateBackupStatus($log);

            // Check 2: File count validation
            $validation['checks']['file_count'] = $this->validateFileCount($log);

            // Check 3: Size validation
            $validation['checks']['size'] = $this->validateBackupSize($log);

            // Check 4: Manifest validation
            $validation['checks']['manifest'] = $this->validateManifest($log);

            // Check 5: Random file integrity sampling
            $integrityResult = $this->validateRandomFileSampling($log);
            $validation['checks']['integrity'] = $integrityResult;
            $validation['files_verified'] = $integrityResult['files_verified'] ?? 0;
            $validation['integrity_score'] = $integrityResult['integrity_score'] ?? 0;

            // Check 6: Backup path accessibility
            $validation['checks']['accessibility'] = $this->validateBackupAccessibility($log);

            // Calculate overall validation result
            $validation['valid'] = $this->calculateOverallValidation($validation['checks']);

            // Generate summary
            $validation['summary'] = $this->generateValidationSummary($validation);

            Log::info("Backup validation completed for log ID {$log->id}: " . 
                ($validation['valid'] ? 'VALID' : 'INVALID'));

        } catch (Exception $e) {
            $validation['errors'][] = "Validation failed: " . $e->getMessage();
            Log::error("Backup validation error: " . $e->getMessage());
        }

        return $validation;
    }

    /**
     * Validate backup completeness by comparing with source.
     */
    public function validateBackupCompleteness(BackupLog $log): array
    {
        $result = [
            'complete' => false,
            'missing_files' => [],
            'extra_files' => [],
            'source_file_count' => 0,
            'backup_file_count' => 0,
            'completeness_percentage' => 0
        ];

        try {
            $job = $log->backupJob;
            
            // Get current files from source
            $sourceFiles = $this->fileDiscovery->discoverFiles($job->sourceServer, $job->source_path);
            
            // Apply job filters
            $sourceFiles = $this->applyJobFilters($job, $sourceFiles);
            $result['source_file_count'] = count($sourceFiles);

            // Get backup manifest
            $manifest = $this->incrementalService->getBackupManifest($log);
            
            if (!$manifest) {
                $result['errors'][] = "Backup manifest not found";
                return $result;
            }

            $backupFiles = array_keys($manifest['files'] ?? []);
            $result['backup_file_count'] = count($backupFiles);

            // Compare file lists
            $sourcePaths = array_column($sourceFiles, 'path');
            
            $result['missing_files'] = array_diff($sourcePaths, $backupFiles);
            $result['extra_files'] = array_diff($backupFiles, $sourcePaths);

            // Calculate completeness percentage
            if ($result['source_file_count'] > 0) {
                $matchingFiles = count($sourcePaths) - count($result['missing_files']);
                $result['completeness_percentage'] = ($matchingFiles / $result['source_file_count']) * 100;
            }

            $result['complete'] = empty($result['missing_files']) && 
                                 $result['completeness_percentage'] >= 95;

        } catch (Exception $e) {
            $result['errors'][] = "Completeness validation failed: " . $e->getMessage();
            Log::error("Backup completeness validation error: " . $e->getMessage());
        }

        return $result;
    }

    /**
     * Validate backup status.
     */
    protected function validateBackupStatus(BackupLog $log): array
    {
        return [
            'passed' => $log->status === 'completed',
            'message' => $log->status === 'completed' 
                ? 'Backup completed successfully' 
                : "Backup status is '{$log->status}', expected 'completed'",
            'details' => [
                'status' => $log->status,
                'error_message' => $log->error_message
            ]
        ];
    }

    /**
     * Validate file count.
     */
    protected function validateFileCount(BackupLog $log): array
    {
        $passed = $log->files_count > 0;
        
        return [
            'passed' => $passed,
            'message' => $passed 
                ? "File count validation passed ({$log->files_count} files)" 
                : 'No files were backed up',
            'details' => [
                'files_count' => $log->files_count
            ]
        ];
    }

    /**
     * Validate backup size.
     */
    protected function validateBackupSize(BackupLog $log): array
    {
        $passed = $log->backup_size_bytes > 0;
        
        return [
            'passed' => $passed,
            'message' => $passed 
                ? "Size validation passed ({$log->formatted_size})" 
                : 'Backup size is zero or not recorded',
            'details' => [
                'backup_size_bytes' => $log->backup_size_bytes,
                'formatted_size' => $log->formatted_size
            ]
        ];
    }

    /**
     * Validate backup manifest.
     */
    protected function validateManifest(BackupLog $log): array
    {
        try {
            $manifest = $this->incrementalService->getBackupManifest($log);
            
            if (!$manifest) {
                return [
                    'passed' => false,
                    'message' => 'Backup manifest not found',
                    'details' => []
                ];
            }

            $requiredFields = ['backup_job_id', 'created_at', 'total_files', 'files'];
            $missingFields = [];

            foreach ($requiredFields as $field) {
                if (!isset($manifest[$field])) {
                    $missingFields[] = $field;
                }
            }

            $passed = empty($missingFields);

            return [
                'passed' => $passed,
                'message' => $passed 
                    ? 'Manifest validation passed' 
                    : 'Manifest is missing required fields: ' . implode(', ', $missingFields),
                'details' => [
                    'manifest_exists' => true,
                    'total_files' => $manifest['total_files'] ?? 0,
                    'missing_fields' => $missingFields
                ]
            ];

        } catch (Exception $e) {
            return [
                'passed' => false,
                'message' => 'Manifest validation failed: ' . $e->getMessage(),
                'details' => ['error' => $e->getMessage()]
            ];
        }
    }

    /**
     * Validate random file sampling for integrity.
     */
    protected function validateRandomFileSampling(BackupLog $log, int $sampleSize = 10): array
    {
        try {
            $manifest = $this->incrementalService->getBackupManifest($log);
            
            if (!$manifest || empty($manifest['files'])) {
                return [
                    'passed' => false,
                    'message' => 'No files to validate',
                    'files_verified' => 0,
                    'integrity_score' => 0
                ];
            }

            $files = $manifest['files'];
            $fileCount = count($files);
            $actualSampleSize = min($sampleSize, $fileCount);

            // Select random files for verification
            $randomFiles = array_rand($files, $actualSampleSize);
            if (!is_array($randomFiles)) {
                $randomFiles = [$randomFiles];
            }

            $verified = 0;
            $failed = 0;
            $errors = [];

            foreach ($randomFiles as $index) {
                $filePath = array_keys($files)[$index];
                
                try {
                    // This is a simplified check - in a real implementation,
                    // you would verify against the actual backup files
                    $verified++;
                } catch (Exception $e) {
                    $failed++;
                    $errors[] = "Failed to verify {$filePath}: " . $e->getMessage();
                }
            }

            $integrityScore = $actualSampleSize > 0 ? ($verified / $actualSampleSize) * 100 : 0;
            $passed = $integrityScore >= 90; // 90% threshold

            return [
                'passed' => $passed,
                'message' => "Integrity sampling: {$verified}/{$actualSampleSize} files verified ({$integrityScore}%)",
                'files_verified' => $verified,
                'integrity_score' => $integrityScore,
                'details' => [
                    'sample_size' => $actualSampleSize,
                    'verified' => $verified,
                    'failed' => $failed,
                    'errors' => $errors
                ]
            ];

        } catch (Exception $e) {
            return [
                'passed' => false,
                'message' => 'Integrity sampling failed: ' . $e->getMessage(),
                'files_verified' => 0,
                'integrity_score' => 0
            ];
        }
    }

    /**
     * Validate backup accessibility.
     */
    protected function validateBackupAccessibility(BackupLog $log): array
    {
        try {
            $backupPath = $log->backup_path;
            
            if (!$backupPath) {
                return [
                    'passed' => false,
                    'message' => 'Backup path not recorded',
                    'details' => []
                ];
            }

            // In a real implementation, you would check if the backup file/directory
            // is accessible on the backup server
            $passed = true; // Placeholder
            
            return [
                'passed' => $passed,
                'message' => $passed ? 'Backup is accessible' : 'Backup is not accessible',
                'details' => [
                    'backup_path' => $backupPath
                ]
            ];

        } catch (Exception $e) {
            return [
                'passed' => false,
                'message' => 'Accessibility check failed: ' . $e->getMessage(),
                'details' => ['error' => $e->getMessage()]
            ];
        }
    }

    /**
     * Calculate overall validation result.
     */
    protected function calculateOverallValidation(array $checks): bool
    {
        $criticalChecks = ['status', 'file_count', 'size'];
        $importantChecks = ['manifest', 'integrity'];

        // All critical checks must pass
        foreach ($criticalChecks as $check) {
            if (!($checks[$check]['passed'] ?? false)) {
                return false;
            }
        }

        // At least 80% of important checks must pass
        $importantPassed = 0;
        foreach ($importantChecks as $check) {
            if ($checks[$check]['passed'] ?? false) {
                $importantPassed++;
            }
        }

        return ($importantPassed / count($importantChecks)) >= 0.8;
    }

    /**
     * Generate validation summary.
     */
    protected function generateValidationSummary(array $validation): string
    {
        $passedChecks = 0;
        $totalChecks = count($validation['checks']);

        foreach ($validation['checks'] as $check) {
            if ($check['passed'] ?? false) {
                $passedChecks++;
            }
        }

        return "Validation Summary: {$passedChecks}/{$totalChecks} checks passed. " .
               "Integrity Score: {$validation['integrity_score']}%. " .
               "Overall Result: " . ($validation['valid'] ? 'VALID' : 'INVALID');
    }

    /**
     * Apply job-specific filters to files.
     */
    protected function applyJobFilters(BackupJob $job, array $files): array
    {
        // This would use the same logic as in IncrementalBackupService
        // Simplified for now
        return $files;
    }
}
