# DevOps Backup Management System

A comprehensive Laravel 12 + React/TypeScript application for managing server backups with modern UI and robust backend architecture.

## Features

- **Server Management**: Full CRUD operations for source and backup servers
- **Backup Job Scheduling**: Create and manage scheduled backup jobs with CRON expressions
- **Connection Testing**: Real-time connection testing for all servers
- **Comprehensive Logging**: Detailed backup execution logs and history
- **Modern UI**: Clean, responsive interface built with React/TypeScript and Inertia.js
- **Queue-based Processing**: Asynchronous backup execution and connection testing

## Quick Start

### Prerequisites
- PHP 8.2+
- Node.js 18+
- Composer
- SQLite (default) or MySQL/PostgreSQL

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd devops

# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Run database migrations
php artisan migrate

# Seed the database (optional)
php artisan db:seed

# Build frontend assets
npm run build

# Start the development server
php artisan serve
```

### Queue Worker Setup (Required for Connection Testing)
The application uses Laravel queues for background processing. You must run a queue worker for connection testing and backup jobs to function properly.

```bash
# Start queue worker (in a separate terminal)
php artisan queue:work --queue=testing,backup-execution,maintenance,default --tries=3 --timeout=300

# Or use the provided script for development
./scripts/start-queue-worker.sh
```

**Important**: Without a running queue worker, connection tests will remain in "pending" status indefinitely.

## Troubleshooting

### Connection Tests Stuck in "Pending" Status
This usually means the queue worker is not running. Check:

1. **Start the queue worker**:
   ```bash
   php artisan queue:work --queue=testing,backup-execution,maintenance,default
   ```

2. **Check queued jobs**:
   ```bash
   php artisan queue:monitor
   ```

3. **Clear stuck jobs** (if needed):
   ```bash
   php artisan queue:clear
   ```

### Failed Jobs
View and retry failed jobs:
```bash
# View failed jobs
php artisan queue:failed

# Retry all failed jobs
php artisan queue:retry all

# Retry specific job
php artisan queue:retry <job-id>
```
