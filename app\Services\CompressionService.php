<?php

namespace App\Services;

use Symfony\Component\Process\Process;
use Illuminate\Support\Facades\Log;
use Exception;
use ZipArchive;
use PharData;

class CompressionService
{
    protected array $supportedFormats = ['gzip', 'bzip2', 'lzma', 'zip'];
    protected array $compressionLevels = [1, 2, 3, 4, 5, 6, 7, 8, 9];

    /**
     * Compress files into an archive.
     */
    public function compressFiles(array $files, string $outputPath, string $algorithm = 'gzip', int $level = 6, ?callable $progressCallback = null): string
    {
        try {
            // Validate compression algorithm and level
            $this->validateCompressionParameters($algorithm, $level);

            // Determine compression type based on file extension or algorithm
            $extension = pathinfo($outputPath, PATHINFO_EXTENSION);

            if (in_array($extension, ['gz', 'tgz']) || $algorithm === 'gzip') {
                return $this->createTarGzArchive($files, $outputPath, $level, $progressCallback);
            } elseif (in_array($extension, ['bz2', 'tbz2']) || $algorithm === 'bzip2') {
                return $this->createTarBz2Archive($files, $outputPath, $level, $progressCallback);
            } elseif ($extension === 'zip' || $algorithm === 'zip') {
                return $this->createZipArchive($files, $outputPath, $level, $progressCallback);
            } elseif ($extension === 'tar') {
                return $this->createTarArchive($files, $outputPath, $progressCallback);
            } else {
                throw new Exception("Unsupported compression format: {$extension}");
            }
        } catch (Exception $e) {
            Log::error("Compression failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Compress a directory into an archive.
     */
    public function compressDirectory(string $directory, string $outputPath, string $algorithm = 'gzip', int $level = 6, ?callable $progressCallback = null): string
    {
        try {
            if (!is_dir($directory)) {
                throw new Exception("Directory does not exist: {$directory}");
            }

            // Get all files in directory recursively
            $files = $this->getDirectoryFiles($directory);

            if (empty($files)) {
                throw new Exception("No files found in directory: {$directory}");
            }

            return $this->compressFiles($files, $outputPath, $algorithm, $level, $progressCallback);
        } catch (Exception $e) {
            Log::error("Directory compression failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Validate compression parameters.
     */
    protected function validateCompressionParameters(string $algorithm, int $level): void
    {
        if (!in_array($algorithm, $this->supportedFormats)) {
            throw new Exception("Unsupported compression algorithm: {$algorithm}");
        }

        if (!in_array($level, $this->compressionLevels)) {
            throw new Exception("Invalid compression level: {$level}. Must be between 1-9");
        }
    }

    /**
     * Get all files in a directory recursively.
     */
    protected function getDirectoryFiles(string $directory): array
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * Get compression ratio between original and compressed data.
     */
    public function getCompressionRatio(string $originalPath, string $compressedPath): float
    {
        try {
            if (!file_exists($originalPath) || !file_exists($compressedPath)) {
                throw new Exception("One or both files do not exist");
            }

            $originalSize = $this->getDirectorySize($originalPath);
            $compressedSize = filesize($compressedPath);

            if ($originalSize === 0) {
                return 0.0;
            }

            $ratio = (($originalSize - $compressedSize) / $originalSize) * 100;
            return round($ratio, 2);

        } catch (Exception $e) {
            Log::error("Failed to calculate compression ratio: " . $e->getMessage());
            return 0.0;
        }
    }

    /**
     * Compress file using streaming for large files.
     */
    public function compressFileStreaming(string $inputPath, string $outputPath, string $algorithm = 'gzip', int $level = 6, ?callable $progressCallback = null): string
    {
        try {
            if (!file_exists($inputPath)) {
                throw new Exception("Input file does not exist: {$inputPath}");
            }

            $inputSize = filesize($inputPath);
            $processedBytes = 0;

            switch ($algorithm) {
                case 'gzip':
                    $this->compressFileWithGzip($inputPath, $outputPath, $level, function($bytes) use (&$processedBytes, $inputSize, $progressCallback) {
                        $processedBytes += $bytes;
                        if ($progressCallback && $inputSize > 0) {
                            $progress = ($processedBytes / $inputSize) * 100;
                            $progressCallback(min(100, round($progress, 2)));
                        }
                    });
                    break;
                default:
                    throw new Exception("Streaming compression not supported for algorithm: {$algorithm}");
            }

            Log::info("Streaming compression completed: {$outputPath}");
            return $outputPath;

        } catch (Exception $e) {
            Log::error("Streaming compression failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Extract an archive.
     */
    public function extractArchive(string $archivePath, string $destination): bool
    {
        try {
            $extension = pathinfo($archivePath, PATHINFO_EXTENSION);
            
            if (in_array($extension, ['gz', 'tgz'])) {
                return $this->extractTarGz($archivePath, $destination);
            } elseif ($extension === 'zip') {
                return $this->extractZip($archivePath, $destination);
            } elseif ($extension === 'tar') {
                return $this->extractTar($archivePath, $destination);
            } else {
                throw new Exception("Unsupported archive format: {$extension}");
            }
        } catch (Exception $e) {
            Log::error("Extraction failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a tar.gz archive with compression level and progress tracking.
     */
    protected function createTarGzArchive(array $files, string $outputPath, int $level = 6, ?callable $progressCallback = null): string
    {
        try {
            // Ensure output directory exists
            $outputDir = dirname($outputPath);
            if (!is_dir($outputDir)) {
                mkdir($outputDir, 0755, true);
            }

            // Build the tar command with compression level
            $command = ['tar', "--use-compress-program=gzip -$level", '-cf', $outputPath];

            // Add files to command
            foreach ($files as $file) {
                $command[] = $file;
            }

            $process = new Process($command);
            $process->setTimeout(3600); // 1 hour timeout

            if ($progressCallback) {
                $totalFiles = count($files);
                $processedFiles = 0;

                $process->run(function ($type, $buffer) use (&$processedFiles, $totalFiles, $progressCallback) {
                    if ($type === Process::OUT) {
                        $processedFiles++;
                        $progress = ($processedFiles / $totalFiles) * 100;
                        $progressCallback(min(100, round($progress, 2)));
                    }
                });
            } else {
                $process->run();
            }

            if (!$process->isSuccessful()) {
                throw new Exception("tar.gz creation failed: " . $process->getErrorOutput());
            }

            Log::info("tar.gz archive created: {$outputPath}");
            return $outputPath;

        } catch (Exception $e) {
            Log::error("tar.gz creation failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create a tar.bz2 archive with compression level and progress tracking.
     */
    protected function createTarBz2Archive(array $files, string $outputPath, int $level = 6, ?callable $progressCallback = null): string
    {
        try {
            // Ensure output directory exists
            $outputDir = dirname($outputPath);
            if (!is_dir($outputDir)) {
                mkdir($outputDir, 0755, true);
            }

            // Build the tar command with bzip2 compression
            $command = ['tar', "--use-compress-program=bzip2 -$level", '-cf', $outputPath];

            // Add files to command
            foreach ($files as $file) {
                $command[] = $file;
            }

            $process = new Process($command);
            $process->setTimeout(3600); // 1 hour timeout

            if ($progressCallback) {
                $totalFiles = count($files);
                $processedFiles = 0;

                $process->run(function ($type, $buffer) use (&$processedFiles, $totalFiles, $progressCallback) {
                    if ($type === Process::OUT) {
                        $processedFiles++;
                        $progress = ($processedFiles / $totalFiles) * 100;
                        $progressCallback(min(100, round($progress, 2)));
                    }
                });
            } else {
                $process->run();
            }

            if (!$process->isSuccessful()) {
                throw new Exception("tar.bz2 creation failed: " . $process->getErrorOutput());
            }

            Log::info("tar.bz2 archive created: {$outputPath}");
            return $outputPath;

        } catch (Exception $e) {
            Log::error("tar.bz2 creation failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create a zip archive with compression level and progress tracking.
     */
    protected function createZipArchive(array $files, string $outputPath, int $level = 6, ?callable $progressCallback = null): string
    {
        try {
            // Ensure output directory exists
            $outputDir = dirname($outputPath);
            if (!is_dir($outputDir)) {
                mkdir($outputDir, 0755, true);
            }

            $zip = new ZipArchive();
            $result = $zip->open($outputPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

            if ($result !== TRUE) {
                throw new Exception("Cannot create ZIP archive: {$outputPath}");
            }

            // Set compression level
            $zip->setCompressionIndex(0, ZipArchive::CM_DEFLATE, $level);

            $totalFiles = count($files);
            $processedFiles = 0;

            foreach ($files as $file) {
                if (is_file($file)) {
                    $relativePath = basename($file);
                    $zip->addFile($file, $relativePath);
                } elseif (is_dir($file)) {
                    $this->addDirectoryToZip($zip, $file, basename($file));
                }

                $processedFiles++;
                if ($progressCallback) {
                    $progress = ($processedFiles / $totalFiles) * 100;
                    $progressCallback(min(100, round($progress, 2)));
                }
            }

            $zip->close();

            Log::info("ZIP archive created: {$outputPath}");
            return $outputPath;

        } catch (Exception $e) {
            Log::error("ZIP creation failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add directory to ZIP archive recursively.
     */
    protected function addDirectoryToZip(ZipArchive $zip, string $directory, string $localPath): void
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            $filePath = $file->getPathname();
            $relativePath = $localPath . '/' . substr($filePath, strlen($directory) + 1);

            if ($file->isDir()) {
                $zip->addEmptyDir($relativePath);
            } elseif ($file->isFile()) {
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * Create a tar archive (uncompressed) with progress tracking.
     */
    protected function createTarArchive(array $files, string $outputPath, ?callable $progressCallback = null): string
    {
        try {
            // Ensure output directory exists
            $outputDir = dirname($outputPath);
            if (!is_dir($outputDir)) {
                mkdir($outputDir, 0755, true);
            }

            $command = ['tar', '-cf', $outputPath];

            // Add files to command
            foreach ($files as $file) {
                $command[] = $file;
            }

            $process = new Process($command);
            $process->setTimeout(3600); // 1 hour timeout

            if ($progressCallback) {
                $totalFiles = count($files);
                $processedFiles = 0;

                $process->run(function ($type, $buffer) use (&$processedFiles, $totalFiles, $progressCallback) {
                    if ($type === Process::OUT) {
                        $processedFiles++;
                        $progress = ($processedFiles / $totalFiles) * 100;
                        $progressCallback(min(100, round($progress, 2)));
                    }
                });
            } else {
                $process->run();
            }

            if (!$process->isSuccessful()) {
                throw new Exception("TAR creation failed: " . $process->getErrorOutput());
            }

            Log::info("TAR archive created: {$outputPath}");
            return $outputPath;

        } catch (Exception $e) {
            Log::error("TAR creation failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Compress file with gzip using streaming.
     */
    protected function compressFileWithGzip(string $inputPath, string $outputPath, int $level, ?callable $progressCallback = null): void
    {
        $chunkSize = 8192; // 8KB chunks
        $inputHandle = fopen($inputPath, 'rb');
        $outputHandle = gzopen($outputPath, "wb{$level}");

        if (!$inputHandle || !$outputHandle) {
            throw new Exception("Failed to open files for streaming compression");
        }

        try {
            while (!feof($inputHandle)) {
                $chunk = fread($inputHandle, $chunkSize);
                if ($chunk !== false) {
                    gzwrite($outputHandle, $chunk);
                    if ($progressCallback) {
                        $progressCallback(strlen($chunk));
                    }
                }
            }
        } finally {
            fclose($inputHandle);
            gzclose($outputHandle);
        }
    }

    /**
     * Extract tar.gz archive.
     */
    protected function extractTarGz(string $archivePath, string $destination): bool
    {
        $process = new Process(['tar', '-xzf', $archivePath, '-C', $destination]);
        $process->run();

        return $process->isSuccessful();
    }

    /**
     * Extract zip archive.
     */
    protected function extractZip(string $archivePath, string $destination): bool
    {
        $process = new Process(['unzip', $archivePath, '-d', $destination]);
        $process->run();

        return $process->isSuccessful();
    }

    /**
     * Extract tar archive.
     */
    protected function extractTar(string $archivePath, string $destination): bool
    {
        $process = new Process(['tar', '-xf', $archivePath, '-C', $destination]);
        $process->run();

        return $process->isSuccessful();
    }

    /**
     * Get the total size of a directory.
     */
    public function getDirectorySize(string $path): int
    {
        if (is_file($path)) {
            return filesize($path);
        }

        $process = new Process(['du', '-sb', $path]);
        $process->run();

        if ($process->isSuccessful()) {
            $output = trim($process->getOutput());
            $parts = explode("\t", $output);
            return (int) $parts[0];
        }

        return 0;
    }
}
