<?php

namespace App\Jobs;

use App\Models\SourceServer;
use App\Models\BackupServer;
use App\Services\ConnectionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ConnectionTestJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 2;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 60; // 1 minute

    /**
     * The server to test connection to.
     */
    public SourceServer|BackupServer $server;

    /**
     * The test ID for tracking status.
     */
    public ?string $testId = null;

    /**
     * Create a new job instance.
     */
    public function __construct(SourceServer|BackupServer $server, ?string $testId = null)
    {
        $this->server = $server;
        $this->testId = $testId;
        $this->onQueue('testing');
    }

    /**
     * Get the test ID for this job.
     */
    protected function getTestId(): ?string
    {
        return $this->testId;
    }

    /**
     * Execute the job.
     */
    public function handle(ConnectionService $connectionService): void
    {
        $serverType = $this->server instanceof SourceServer ? 'source' : 'backup';

        Log::info("Testing connection to {$serverType} server: {$this->server->name}");

        // Get the test ID from the job properties if available
        $testId = $this->getTestId();

        try {
            // Update status to running
            if ($testId) {
                app(\App\Services\ConnectionStatusService::class)->updateTestStatus($testId, [
                    'status' => 'running',
                    'progress' => 25,
                    'message' => 'Establishing connection...'
                ]);
            }

            $startTime = microtime(true);

            // Update progress during connection
            if ($testId) {
                app(\App\Services\ConnectionStatusService::class)->updateTestStatus($testId, [
                    'progress' => 50,
                    'message' => 'Testing authentication...'
                ]);
            }

            $connectionResult = $connectionService->testConnection($this->server);

            // Update progress after connection test
            if ($testId) {
                app(\App\Services\ConnectionStatusService::class)->updateTestStatus($testId, [
                    'progress' => 75,
                    'message' => 'Verifying connection...'
                ]);
            }

            $duration = round((microtime(true) - $startTime) * 1000); // milliseconds

            // Update the server's last connection test timestamp
            $this->server->update([
                'last_connection_test' => now(),
            ]);

            if ($connectionResult) {
                Log::info("Connection test successful for {$serverType} server {$this->server->name} ({$duration}ms)");

                // Store successful connection metadata
                $this->storeConnectionResult(true, $duration);

                // Update status service with success
                if ($testId) {
                    Log::info("Updating connection test status to success for test ID: {$testId}");
                    app(\App\Services\ConnectionStatusService::class)->completeTest(
                        $testId,
                        true,
                        "Connection successful",
                        null,
                        $duration
                    );
                }
            } else {
                Log::warning("Connection test failed for {$serverType} server {$this->server->name} ({$duration}ms)");

                // Store failed connection metadata
                $this->storeConnectionResult(false, $duration, 'Connection test failed');

                // Update status service with failure
                if ($testId) {
                    Log::info("Updating connection test status to failed for test ID: {$testId}");
                    app(\App\Services\ConnectionStatusService::class)->completeTest(
                        $testId,
                        false,
                        "Connection test failed",
                        null,
                        $duration
                    );
                }
            }

        } catch (Exception $e) {
            Log::error("Connection test error for {$serverType} server {$this->server->name}: " . $e->getMessage());

            // Update timestamp even on error
            $this->server->update([
                'last_connection_test' => now(),
            ]);

            // Store error result
            $this->storeConnectionResult(false, 0, $e->getMessage());

            // Update status service with error
            if ($testId) {
                Log::info("Updating connection test status to error for test ID: {$testId}");
                app(\App\Services\ConnectionStatusService::class)->completeTest(
                    $testId,
                    false,
                    "Connection error: " . $e->getMessage()
                );
            }

            // Don't re-throw the exception for connection failures - they are expected
            // Only re-throw for unexpected system errors
            if (!str_contains($e->getMessage(), 'Cannot connect') &&
                !str_contains($e->getMessage(), 'Connection timed out') &&
                !str_contains($e->getMessage(), 'Authentication failed')) {
                throw $e;
            }
        }
    }

    /**
     * Store connection test result in server metadata.
     */
    protected function storeConnectionResult(bool $success, int $duration, ?string $error = null): void
    {
        $connectionOptions = $this->server->connection_options ?? [];

        $connectionOptions['last_test_result'] = [
            'success' => $success,
            'duration_ms' => $duration,
            'tested_at' => now()->toISOString(),
            'error' => $error,
        ];

        // Keep history of last 10 connection tests
        if (!isset($connectionOptions['test_history'])) {
            $connectionOptions['test_history'] = [];
        }

        array_unshift($connectionOptions['test_history'], $connectionOptions['last_test_result']);
        $connectionOptions['test_history'] = array_slice($connectionOptions['test_history'], 0, 10);

        $this->server->update(['connection_options' => $connectionOptions]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        $serverType = $this->server instanceof SourceServer ? 'source' : 'backup';

        Log::error("Connection test failed permanently for {$serverType} server {$this->server->name}: " .
                  $exception->getMessage());

        // Ensure the timestamp is updated even on permanent failure
        $this->server->update([
            'last_connection_test' => now(),
        ]);

        // Store permanent failure result
        $this->storeConnectionResult(false, 0, 'Permanent failure: ' . $exception->getMessage());

        // Update status service with permanent failure
        $testId = $this->getTestId();
        if ($testId) {
            app(\App\Services\ConnectionStatusService::class)->completeTest(
                $testId,
                false,
                'Permanent failure: ' . $exception->getMessage()
            );
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $serverType = $this->server instanceof SourceServer ? 'source' : 'backup';

        return [
            'connection-test',
            $serverType . '-server',
            'server:' . $this->server->id,
        ];
    }
}
