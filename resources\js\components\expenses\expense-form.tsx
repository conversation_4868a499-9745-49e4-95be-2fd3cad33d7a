import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Save, DollarSign } from 'lucide-react';

interface ExpenseFormData {
    name: string;
    expense_date: string;
    value: string;
    description: string;
}

interface ExpenseFormProps {
    data: ExpenseFormData;
    setData: (key: keyof ExpenseFormData, value: string) => void;
    onSubmit: FormEventHandler;
    processing: boolean;
    errors: Partial<Record<keyof ExpenseFormData, string>>;
    submitText?: string;
    title?: string;
}

export default function ExpenseForm({
    data,
    setData,
    onSubmit,
    processing,
    errors,
    submitText = 'Save Expense',
    title = 'Expense Information'
}: ExpenseFormProps) {
    return (
        <Card className="max-w-2xl">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <DollarSign className="w-5 h-5 mr-2" />
                    {title}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={onSubmit} className="space-y-6">
                    <div className="space-y-2">
                        <Label htmlFor="name">Expense Name *</Label>
                        <Input
                            id="name"
                            type="text"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            placeholder="e.g., Groceries, Gas, Lunch"
                            className={errors.name ? 'border-red-500' : ''}
                        />
                        {errors.name && (
                            <Alert className="border-red-200 bg-red-50">
                                <AlertDescription className="text-red-800">
                                    {errors.name}
                                </AlertDescription>
                            </Alert>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="expense_date">Date *</Label>
                        <Input
                            id="expense_date"
                            type="date"
                            value={data.expense_date}
                            onChange={(e) => setData('expense_date', e.target.value)}
                            className={errors.expense_date ? 'border-red-500' : ''}
                        />
                        {errors.expense_date && (
                            <Alert className="border-red-200 bg-red-50">
                                <AlertDescription className="text-red-800">
                                    {errors.expense_date}
                                </AlertDescription>
                            </Alert>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="value">Amount *</Label>
                        <div className="relative">
                            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                            <Input
                                id="value"
                                type="number"
                                step="0.01"
                                min="0"
                                max="99999999.99"
                                value={data.value}
                                onChange={(e) => setData('value', e.target.value)}
                                placeholder="0.00"
                                className={`pl-10 ${errors.value ? 'border-red-500' : ''}`}
                            />
                        </div>
                        {errors.value && (
                            <Alert className="border-red-200 bg-red-50">
                                <AlertDescription className="text-red-800">
                                    {errors.value}
                                </AlertDescription>
                            </Alert>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                            id="description"
                            value={data.description}
                            onChange={(e) => setData('description', e.target.value)}
                            placeholder="Optional description or notes about this expense"
                            rows={3}
                            className={errors.description ? 'border-red-500' : ''}
                        />
                        {errors.description && (
                            <Alert className="border-red-200 bg-red-50">
                                <AlertDescription className="text-red-800">
                                    {errors.description}
                                </AlertDescription>
                            </Alert>
                        )}
                    </div>

                    <div className="flex items-center space-x-4 pt-4">
                        <Button type="submit" disabled={processing}>
                            <Save className="w-4 h-4 mr-2" />
                            {processing ? 'Saving...' : submitText}
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    );
}
