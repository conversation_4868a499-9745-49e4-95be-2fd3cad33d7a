<?php

namespace Database\Seeders;

use App\Models\Expense;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExpenseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user or create one if none exists
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Demo User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        // Sample expense categories and names
        $expenseData = [
            // Current month expenses
            ['name' => 'Groceries', 'value' => 85.50, 'description' => 'Weekly grocery shopping at supermarket'],
            ['name' => 'Gas', 'value' => 45.00, 'description' => 'Fuel for car'],
            ['name' => 'Coffee', 'value' => 12.75, 'description' => 'Morning coffee at cafe'],
            ['name' => 'Lunch', 'value' => 18.50, 'description' => 'Business lunch meeting'],
            ['name' => 'Internet Bill', 'value' => 79.99, 'description' => 'Monthly internet subscription'],
            ['name' => 'Phone Bill', 'value' => 55.00, 'description' => 'Monthly mobile phone plan'],
            ['name' => 'Parking', 'value' => 8.00, 'description' => 'Downtown parking fee'],
            ['name' => 'Uber', 'value' => 22.30, 'description' => 'Ride to airport'],
            ['name' => 'Books', 'value' => 34.99, 'description' => 'Technical books for learning'],
            ['name' => 'Gym Membership', 'value' => 49.99, 'description' => 'Monthly gym subscription'],

            // Previous month expenses
            ['name' => 'Groceries', 'value' => 92.30, 'description' => 'Weekly grocery shopping', 'days_ago' => 35],
            ['name' => 'Electricity Bill', 'value' => 125.45, 'description' => 'Monthly electricity bill', 'days_ago' => 40],
            ['name' => 'Gas', 'value' => 52.00, 'description' => 'Fuel for car', 'days_ago' => 32],
            ['name' => 'Restaurant', 'value' => 67.80, 'description' => 'Dinner with friends', 'days_ago' => 28],
            ['name' => 'Movie Tickets', 'value' => 24.00, 'description' => 'Weekend movie night', 'days_ago' => 30],

            // Two months ago
            ['name' => 'Groceries', 'value' => 78.90, 'description' => 'Weekly grocery shopping', 'days_ago' => 65],
            ['name' => 'Car Insurance', 'value' => 156.00, 'description' => 'Monthly car insurance premium', 'days_ago' => 70],
            ['name' => 'Gas', 'value' => 48.75, 'description' => 'Fuel for car', 'days_ago' => 62],
            ['name' => 'Pharmacy', 'value' => 23.45, 'description' => 'Prescription medication', 'days_ago' => 68],
            ['name' => 'Clothing', 'value' => 89.99, 'description' => 'New work shirt', 'days_ago' => 60],
        ];

        foreach ($expenseData as $expense) {
            $daysAgo = $expense['days_ago'] ?? rand(1, 15); // Random date within last 15 days if not specified

            Expense::create([
                'name' => $expense['name'],
                'expense_date' => now()->subDays($daysAgo)->format('Y-m-d'),
                'value' => $expense['value'],
                'description' => $expense['description'],
                'user_id' => $user->id,
            ]);
        }

        // Add some random expenses for variety
        $randomExpenses = [
            'Coffee Shop', 'Fast Food', 'Taxi', 'Parking Meter', 'Snacks',
            'Office Supplies', 'Software Subscription', 'Streaming Service',
            'Magazine', 'Charity Donation', 'Gift', 'Flowers', 'Pet Food',
            'Car Wash', 'Dry Cleaning', 'Hardware Store', 'Pharmacy'
        ];

        for ($i = 0; $i < 20; $i++) {
            Expense::create([
                'name' => $randomExpenses[array_rand($randomExpenses)],
                'expense_date' => now()->subDays(rand(1, 90))->format('Y-m-d'),
                'value' => rand(500, 15000) / 100, // Random amount between $5.00 and $150.00
                'description' => 'Sample expense for testing purposes',
                'user_id' => $user->id,
            ]);
        }
    }
}
