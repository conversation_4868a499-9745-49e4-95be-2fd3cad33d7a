<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('source_server_id')->constrained('source_servers')->onDelete('cascade');
            $table->foreignId('backup_server_id')->constrained('backup_servers')->onDelete('cascade');
            $table->string('source_path');
            $table->string('destination_path');
            $table->string('schedule'); // CRON expression
            $table->enum('status', ['active', 'paused', 'disabled'])->default('active');
            $table->timestamp('last_run')->nullable();
            $table->timestamp('next_run')->nullable();
            $table->integer('retention_policy_days')->nullable();
            $table->boolean('compression_enabled')->default(true);
            $table->boolean('encryption_enabled')->default(false);
            $table->text('description')->nullable();
            $table->json('backup_options')->nullable(); // Additional backup options
            $table->timestamps();

            // Indexes
            $table->index(['status', 'next_run']);
            $table->index('source_server_id');
            $table->index('backup_server_id');
            $table->index('schedule');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_jobs');
    }
};
