# Task 1.2: Create Service Layer Architecture ✅ COMPLETED

## Overview
Create a robust service layer architecture to handle backup operations, server connections, compression, retention policies, and scheduling. This provides a clean separation of concerns and makes the system maintainable and testable.

## Subtasks

### Subtask 1.2.1: Create BackupService

**Description:** Main orchestration service that coordinates the entire backup process.

**Implementation Steps:**
1. Create the service file:
   ```bash
   mkdir -p app/Services
   touch app/Services/BackupService.php
   ```

2. Implement the service with these key methods:
   - `executeBackup(BackupJob $job): BackupLog`
   - `validateBackupJob(BackupJob $job): bool`
   - `calculateBackupSize(string $path, SourceServer $server): int`
   - `createBackupLog(BackupJob $job): BackupLog`

**Manual Testing:**
- Create a test backup job in the UI
- Use tinker to test the service:
  ```php
  php artisan tinker
  $job = App\Models\BackupJob::first();
  $service = new App\Services\BackupService();
  $service->validateBackupJob($job);
  ```

### Subtask 1.2.2: Create ConnectionService

**Description:** Manages connections to source and backup servers with different protocols.

**Implementation Steps:**
1. Create the service file:
   ```bash
   touch app/Services/ConnectionService.php
   ```

2. Implement methods for:
   - `connectToSourceServer(SourceServer $server): Connection`
   - `connectToBackupServer(BackupServer $server): Connection`
   - `testConnection(Server $server): bool`
   - `closeConnection(Connection $connection): void`

**Manual Testing:**
- Test with existing servers in the database:
  ```php
  php artisan tinker
  $server = App\Models\SourceServer::first();
  $service = new App\Services\ConnectionService();
  $result = $service->testConnection($server);
  var_dump($result);
  ```

### Subtask 1.2.3: Create CompressionService

**Description:** Handles file compression, encryption, and archive creation.

**Implementation Steps:**
1. Create the service file:
   ```bash
   touch app/Services/CompressionService.php
   ```

2. Implement methods for:
   - `compressFiles(array $files, string $outputPath): string`
   - `encryptArchive(string $archivePath, string $password): string`
   - `calculateCompressionRatio(string $originalPath, string $compressedPath): float`
   - `extractArchive(string $archivePath, string $destination): bool`

**Manual Testing:**
- Create test files and compress them:
  ```php
  php artisan tinker
  $service = new App\Services\CompressionService();
  // Create test files first
  file_put_contents('/tmp/test1.txt', 'Test content 1');
  file_put_contents('/tmp/test2.txt', 'Test content 2');
  $result = $service->compressFiles(['/tmp/test1.txt', '/tmp/test2.txt'], '/tmp/test.tar.gz');
  ```

### Subtask 1.2.4: Create RetentionService

**Description:** Manages backup retention policies and cleanup of old backups.

**Implementation Steps:**
1. Create the service file:
   ```bash
   touch app/Services/RetentionService.php
   ```

2. Implement methods for:
   - `applyRetentionPolicy(BackupJob $job): array`
   - `getBackupsToDelete(BackupJob $job): Collection`
   - `deleteOldBackups(Collection $backups): int`
   - `calculateStorageUsage(BackupServer $server): array`

**Manual Testing:**
- Test with backup jobs that have retention policies:
  ```php
  php artisan tinker
  $job = App\Models\BackupJob::where('retention_policy_days', '>', 0)->first();
  $service = new App\Services\RetentionService();
  $toDelete = $service->getBackupsToDelete($job);
  echo "Backups to delete: " . $toDelete->count();
  ```

### Subtask 1.2.5: Create ScheduleService

**Description:** Manages CRON schedules and calculates next run times for backup jobs.

**Implementation Steps:**
1. Create the service file:
   ```bash
   touch app/Services/ScheduleService.php
   ```

2. Implement methods for:
   - `calculateNextRun(string $cronExpression): Carbon`
   - `validateCronExpression(string $expression): bool`
   - `getJobsDueForExecution(): Collection`
   - `updateJobSchedule(BackupJob $job): void`

**Manual Testing:**
- Test CRON expression parsing:
  ```php
  php artisan tinker
  $service = new App\Services\ScheduleService();
  $nextRun = $service->calculateNextRun('0 2 * * *');
  echo "Next run: " . $nextRun->format('Y-m-d H:i:s');
  
  $isValid = $service->validateCronExpression('0 2 * * *');
  echo "Valid: " . ($isValid ? 'Yes' : 'No');
  ```

## Service Registration

### Subtask 1.2.6: Register Services in Service Provider

**Implementation Steps:**
1. Update `app/Providers/AppServiceProvider.php`:
   ```php
   public function register()
   {
       $this->app->singleton(BackupService::class);
       $this->app->singleton(ConnectionService::class);
       $this->app->singleton(CompressionService::class);
       $this->app->singleton(RetentionService::class);
       $this->app->singleton(ScheduleService::class);
   }
   ```

**Manual Testing:**
- Test service resolution:
  ```php
  php artisan tinker
  $backupService = app(App\Services\BackupService::class);
  $connectionService = app(App\Services\ConnectionService::class);
  // Should not throw errors
  ```

## Verification Checklist

After completing all subtasks, verify:

- [ ] All service files are created in `app/Services/`
- [ ] Each service has proper namespace and class structure
- [ ] Services can be instantiated without errors
- [ ] Services are registered in the service provider
- [ ] Basic method signatures are implemented
- [ ] Services can be resolved from the container

## Expected Files Created

- `app/Services/BackupService.php`
- `app/Services/ConnectionService.php`
- `app/Services/CompressionService.php`
- `app/Services/RetentionService.php`
- `app/Services/ScheduleService.php`

## Expected Files Modified

- `app/Providers/AppServiceProvider.php`

## Architecture Benefits

1. **Separation of Concerns:** Each service handles a specific aspect of backup operations
2. **Testability:** Services can be easily unit tested in isolation
3. **Maintainability:** Changes to one service don't affect others
4. **Reusability:** Services can be used across different parts of the application
5. **Dependency Injection:** Services can be easily mocked for testing

## Next Steps

After completing this task, proceed to [Task 1.3: Create Job Classes](./task-1.3-job-classes.md).

---

## ✅ TASK COMPLETED

**Completion Date:** 2025-06-14
**Status:** All service classes successfully created and tested

**Created Services:**
- ✅ `BackupService` - Main orchestration service for backup operations
- ✅ `ConnectionService` - Server connection management (SSH/SFTP/FTP)
- ✅ `CompressionService` - File compression, encryption, and archive creation
- ✅ `RetentionService` - Backup retention policies and cleanup
- ✅ `ScheduleService` - CRON schedule management and job scheduling

**Service Registration:**
- ✅ All services registered as singletons in `AppServiceProvider`
- ✅ Services can be resolved from Laravel's service container

**Manual Testing Results:**
- ✅ All services instantiate without errors
- ✅ ScheduleService CRON parsing works correctly
- ✅ CompressionService creates tar.gz archives successfully
- ✅ BackupService validation logic functions properly
- ✅ Service dependency injection works correctly

**Architecture Benefits Achieved:**
- ✅ Clean separation of concerns
- ✅ Testable service layer
- ✅ Maintainable code structure
- ✅ Reusable components
- ✅ Proper dependency injection
