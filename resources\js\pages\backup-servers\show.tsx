import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ConnectionTestButton } from '@/components/ConnectionTestButton';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, MoreHorizontal, Edit, Trash2, Wifi, WifiOff, HardDrive, Shield, Calendar, Clock, Play } from 'lucide-react';

interface BackupJob {
    id: number;
    name: string;
    status: 'active' | 'paused' | 'disabled';
    schedule: string;
    next_run?: string;
    source_server: {
        name: string;
        ip_address: string;
    };
}

interface BackupServer {
    id: number;
    name: string;
    protocol: 'ftp' | 'sftp';
    ip_address: string;
    port: number;
    username: string;
    base_directory: string;
    description?: string;
    is_active: boolean;
    last_connection_test?: string;
    created_at: string;
    updated_at: string;
    backup_jobs: BackupJob[];
}

interface Props {
    backupServer: BackupServer;
}

export default function BackupServerShow({ backupServer }: Props) {
    const handleDelete = () => {
        // Count backup jobs for this server
        const backupJobsCount = backupServer.backup_jobs?.length || 0;
        const activeJobsCount = backupServer.backup_jobs?.filter((job: any) => job.status === 'active').length || 0;

        let message = `Are you sure you want to delete "${backupServer.name}"?\n\n`;

        if (backupJobsCount === 0) {
            message += `✅ This backup server has no backup jobs and can be safely deleted.`;
        } else {
            message += `⚠️  WARNING: This backup server has ${backupJobsCount} backup job(s):\n`;
            message += `   • ${activeJobsCount} active job(s)\n`;
            message += `   • ${backupJobsCount - activeJobsCount} inactive job(s)\n\n`;

            if (activeJobsCount > 0) {
                message += `❌ Cannot delete backup server with active backup jobs.\n\n`;
                message += `REQUIRED ACTIONS:\n`;
                message += `1. Go to Backup Jobs and disable/delete the active jobs first\n`;
                message += `2. Then return here to delete this backup server\n\n`;
                message += `Active backup jobs using this server:\n`;
                backupServer.backup_jobs?.filter((job: any) => job.status === 'active').forEach((job: any, index: number) => {
                    message += `   ${index + 1}. ${job.name}\n`;
                });
                message += `\nClick OK to see the error message, or Cancel to keep the server.`;
            } else {
                message += `✅ All backup jobs are inactive, deletion should work.`;
            }
        }

        const choice = confirm(message);

        if (choice) {
            router.delete(`/backup-servers/${backupServer.id}`, {
                onSuccess: () => {
                    // Redirect will be handled by the controller
                },
                onError: (errors) => {
                    console.error('Delete failed:', errors);
                    alert(
                        `❌ DELETE FAILED\n\n` +
                        `Backup server "${backupServer.name}" cannot be deleted because it has ${activeJobsCount} active backup jobs.\n\n` +
                        `🔧 TO DELETE THIS SERVER:\n` +
                        `1. Go to "Backup Jobs" in the sidebar\n` +
                        `2. Find and disable/delete these active jobs:\n` +
                        backupServer.backup_jobs?.filter((job: any) => job.status === 'active').map((job: any, index: number) =>
                            `   ${index + 1}. ${job.name}`
                        ).join('\n') +
                        `\n\n3. Return here and try deleting the server again\n\n` +
                        `💡 TIP: You can filter backup jobs by this server to find them quickly.`
                    );
                }
            });
        }
    };



    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'default';
            case 'paused': return 'secondary';
            case 'disabled': return 'outline';
            default: return 'secondary';
        }
    };

    return (
        <AppLayout>
            <Head title={`Backup Server: ${backupServer.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href="/backup-servers">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Backup Servers
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">{backupServer.name}</h1>
                            <p className="text-muted-foreground">
                                Backup destination server details and jobs
                            </p>
                        </div>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline">
                                <MoreHorizontal className="mr-2 h-4 w-4" />
                                Actions
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                                <Link href={`/backup-servers/${backupServer.id}/edit`}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Server
                                </Link>
                            </DropdownMenuItem>

                            <DropdownMenuItem 
                                onClick={handleDelete}
                                className="text-destructive"
                            >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Server
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Server Details */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <HardDrive className="mr-2 h-5 w-5" />
                                Server Information
                            </CardTitle>
                            <CardDescription>FTP/SFTP connection details and configuration</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">Status</span>
                                <Badge variant={backupServer.is_active ? 'default' : 'secondary'}>
                                    {backupServer.is_active ? (
                                        <>
                                            <Wifi className="mr-1 h-3 w-3" />
                                            Active
                                        </>
                                    ) : (
                                        <>
                                            <WifiOff className="mr-1 h-3 w-3" />
                                            Inactive
                                        </>
                                    )}
                                </Badge>
                            </div>
                            
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Protocol:</span>
                                    <Badge variant="outline" className="text-xs">
                                        {backupServer.protocol === 'sftp' ? (
                                            <>
                                                <Shield className="mr-1 h-3 w-3" />
                                                SFTP (Secure)
                                            </>
                                        ) : (
                                            <>
                                                FTP
                                            </>
                                        )}
                                    </Badge>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">IP Address:</span>
                                    <span className="text-sm font-mono">{backupServer.ip_address}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Port:</span>
                                    <span className="text-sm font-mono">{backupServer.port}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Username:</span>
                                    <span className="text-sm font-mono">{backupServer.username}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Base Directory:</span>
                                    <span className="text-sm font-mono">{backupServer.base_directory}</span>
                                </div>
                            </div>

                            {backupServer.description && (
                                <div>
                                    <span className="text-sm font-medium">Description</span>
                                    <p className="text-sm text-muted-foreground mt-1">{backupServer.description}</p>
                                </div>
                            )}

                            <div className="pt-2 border-t">
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>Created: {new Date(backupServer.created_at).toLocaleDateString()}</span>
                                    <span>Updated: {new Date(backupServer.updated_at).toLocaleDateString()}</span>
                                </div>
                                {backupServer.last_connection_test && (
                                    <div className="text-xs text-muted-foreground mt-1">
                                        Last test: {new Date(backupServer.last_connection_test).toLocaleString()}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Connection Test */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Wifi className="mr-2 h-5 w-5" />
                                Connection Status
                            </CardTitle>
                            <CardDescription>Test {backupServer.protocol.toUpperCase()} connectivity to this server</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="py-4">
                                {backupServer.last_connection_test && (
                                    <div className="mb-4 text-center">
                                        <div className="text-sm text-muted-foreground mb-2">Last Connection Test</div>
                                        <div className="text-lg font-semibold">
                                            {new Date(backupServer.last_connection_test).toLocaleString()}
                                        </div>
                                    </div>
                                )}

                                <ConnectionTestButton
                                    serverType="backup"
                                    serverId={backupServer.id}
                                    serverName={backupServer.name}
                                    showProgress={true}
                                    onTestComplete={(success, result) => {
                                        if (success) {
                                            // Optionally refresh the page or update the last_connection_test
                                            console.log('Connection test successful:', result);
                                        }
                                    }}
                                />

                                <div className="mt-4 p-3 bg-muted rounded-lg text-sm">
                                    <div className="font-medium mb-1">Connection String:</div>
                                    <div className="font-mono text-xs break-all">
                                        {backupServer.protocol}://{backupServer.username}@{backupServer.ip_address}:{backupServer.port}{backupServer.base_directory}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Backup Jobs */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    Backup Jobs ({backupServer.backup_jobs.length})
                                </CardTitle>
                                <CardDescription>Backup tasks using this destination server</CardDescription>
                            </div>
                            <Link href="/backup-jobs/create">
                                <Button size="sm">
                                    Create Backup Job
                                </Button>
                            </Link>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {backupServer.backup_jobs.length > 0 ? (
                            <div className="space-y-4">
                                {backupServer.backup_jobs.map((job) => (
                                    <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center space-x-4">
                                            <div>
                                                <h4 className="font-medium">{job.name}</h4>
                                                <p className="text-sm text-muted-foreground">
                                                    Source: {job.source_server.name} ({job.source_server.ip_address})
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    Schedule: {job.schedule}
                                                    {job.next_run && ` • Next run: ${new Date(job.next_run).toLocaleString()}`}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Badge variant={getStatusColor(job.status)}>
                                                {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                                            </Badge>
                                            <Link href={`/backup-jobs/${job.id}`}>
                                                <Button variant="outline" size="sm">
                                                    View
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No backup jobs configured</h3>
                                <p className="text-muted-foreground mb-4">
                                    Create a backup job to start using this destination server.
                                </p>
                                <Link href="/backup-jobs/create">
                                    <Button>
                                        Create First Backup Job
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
