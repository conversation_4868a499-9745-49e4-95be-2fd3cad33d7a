import { Head, <PERSON>, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { ArrowLeft, Edit, Mail, Trash2, User, Calendar, CheckCircle, XCircle } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

interface Props {
    user: User;
}

export default function ShowUser({ user }: Props) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Users',
            href: '/users',
        },
        {
            title: user.name,
            href: `/users/${user.id}`,
        },
    ];

    const handleDelete = () => {
        if (confirm(`Are you sure you want to delete ${user.name}?`)) {
            router.delete(`/users/${user.id}`);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={user.name} />
            
            <div className="px-4 py-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-2xl font-bold">User Details</h1>
                        <p className="text-muted-foreground">View user information</p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button variant="outline" asChild>
                            <Link href="/users">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Users
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={`/users/${user.id}/edit`}>
                                <Edit className="w-4 h-4 mr-2" />
                                Edit
                            </Link>
                        </Button>
                        <Button
                            variant="outline"
                            onClick={handleDelete}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* User Profile Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <User className="w-5 h-5" />
                                <span>Profile Information</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-center mb-6">
                                <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
                                    <User className="w-10 h-10 text-primary" />
                                </div>
                            </div>
                            
                            <div className="space-y-3">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Name</label>
                                    <p className="text-lg font-medium">{user.name}</p>
                                </div>
                                
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Email</label>
                                    <div className="flex items-center space-x-2">
                                        <Mail className="w-4 h-4 text-muted-foreground" />
                                        <p className="text-lg">{user.email}</p>
                                    </div>
                                </div>
                                
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Email Status</label>
                                    <div className="flex items-center space-x-2 mt-1">
                                        {user.email_verified_at ? (
                                            <>
                                                <CheckCircle className="w-4 h-4 text-green-600" />
                                                <Badge variant="secondary" className="bg-green-100 text-green-800">
                                                    Verified
                                                </Badge>
                                                <span className="text-sm text-muted-foreground">
                                                    on {new Date(user.email_verified_at).toLocaleDateString()}
                                                </span>
                                            </>
                                        ) : (
                                            <>
                                                <XCircle className="w-4 h-4 text-red-600" />
                                                <Badge variant="outline" className="border-red-200 text-red-800">
                                                    Unverified
                                                </Badge>
                                            </>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Account Information Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <Calendar className="w-5 h-5" />
                                <span>Account Information</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">User ID</label>
                                <p className="text-lg font-mono">{user.id}</p>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Created</label>
                                <p className="text-lg">{new Date(user.created_at).toLocaleDateString()}</p>
                                <p className="text-sm text-muted-foreground">
                                    {new Date(user.created_at).toLocaleTimeString()}
                                </p>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                                <p className="text-lg">{new Date(user.updated_at).toLocaleDateString()}</p>
                                <p className="text-sm text-muted-foreground">
                                    {new Date(user.updated_at).toLocaleTimeString()}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
