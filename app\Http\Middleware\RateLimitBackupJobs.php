<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class RateLimitBackupJobs
{
    /**
     * Maximum number of backup jobs that can be dispatched per minute per server.
     */
    protected int $maxJobsPerMinute = 5;

    /**
     * Maximum number of concurrent backup jobs per server.
     */
    protected int $maxConcurrentJobs = 2;

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // This middleware is primarily for job rate limiting
        // For HTTP requests, we'll check if backup job dispatch is being rate limited

        if ($request->is('api/backup-jobs/*/execute') || $request->is('backup-jobs/*/execute')) {
            $serverId = $request->route('id') ?? $request->input('server_id');

            if ($serverId && $this->isRateLimited($serverId)) {
                return response()->json([
                    'error' => 'Rate limit exceeded. Too many backup jobs dispatched recently.',
                    'retry_after' => 60
                ], 429);
            }
        }

        return $next($request);
    }

    /**
     * Check if backup job dispatching is rate limited for a server.
     */
    public function isRateLimited(int $serverId): bool
    {
        $key = "backup_jobs_rate_limit:server:{$serverId}";
        $currentCount = Cache::get($key, 0);

        if ($currentCount >= $this->maxJobsPerMinute) {
            Log::warning("Rate limit exceeded for server {$serverId}: {$currentCount} jobs in the last minute");
            return true;
        }

        return false;
    }

    /**
     * Increment the rate limit counter for a server.
     */
    public function incrementRateLimit(int $serverId): void
    {
        $key = "backup_jobs_rate_limit:server:{$serverId}";
        $currentCount = Cache::get($key, 0);
        Cache::put($key, $currentCount + 1, 60); // 60 seconds TTL
    }

    /**
     * Check if the maximum number of concurrent jobs is exceeded.
     */
    public function isConcurrencyLimitExceeded(int $serverId): bool
    {
        // This would typically check the queue or database for running jobs
        // For now, we'll use a simple cache-based approach
        $key = "backup_jobs_concurrent:server:{$serverId}";
        $currentJobs = Cache::get($key, 0);

        return $currentJobs >= $this->maxConcurrentJobs;
    }

    /**
     * Increment the concurrent jobs counter.
     */
    public function incrementConcurrentJobs(int $serverId): void
    {
        $key = "backup_jobs_concurrent:server:{$serverId}";
        $currentJobs = Cache::get($key, 0);
        Cache::put($key, $currentJobs + 1, 3600); // 1 hour TTL
    }

    /**
     * Decrement the concurrent jobs counter.
     */
    public function decrementConcurrentJobs(int $serverId): void
    {
        $key = "backup_jobs_concurrent:server:{$serverId}";
        $currentJobs = Cache::get($key, 0);

        if ($currentJobs > 0) {
            Cache::put($key, $currentJobs - 1, 3600);
        }
    }

    /**
     * Get rate limiting statistics for a server.
     */
    public function getRateLimitStats(int $serverId): array
    {
        $rateLimitKey = "backup_jobs_rate_limit:server:{$serverId}";
        $concurrentKey = "backup_jobs_concurrent:server:{$serverId}";

        return [
            'jobs_last_minute' => Cache::get($rateLimitKey, 0),
            'max_jobs_per_minute' => $this->maxJobsPerMinute,
            'concurrent_jobs' => Cache::get($concurrentKey, 0),
            'max_concurrent_jobs' => $this->maxConcurrentJobs,
            'rate_limited' => $this->isRateLimited($serverId),
            'concurrency_limited' => $this->isConcurrencyLimitExceeded($serverId),
        ];
    }
}
