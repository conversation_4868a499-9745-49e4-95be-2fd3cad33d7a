<?php

namespace App\Jobs;

use App\Models\BackupJob;
use App\Models\BackupServer;
use App\Services\RetentionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class RetentionCleanupJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 2;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 1800; // 30 minutes

    /**
     * The backup job or server to clean up.
     */
    public BackupJob|BackupServer $target;

    /**
     * Create a new job instance.
     */
    public function __construct(BackupJob|BackupServer $target)
    {
        $this->target = $target;
        $this->onQueue('maintenance');
    }

    /**
     * Execute the job.
     */
    public function handle(RetentionService $retentionService): void
    {
        try {
            if ($this->target instanceof BackupJob) {
                $this->cleanupBackupJob($retentionService);
            } elseif ($this->target instanceof BackupServer) {
                $this->cleanupBackupServer($retentionService);
            }

        } catch (Exception $e) {
            Log::error("Retention cleanup failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Clean up old backups for a specific backup job.
     */
    protected function cleanupBackupJob(RetentionService $retentionService): void
    {
        Log::info("Starting retention cleanup for backup job: {$this->target->name}");

        $result = $retentionService->applyRetentionPolicy($this->target);

        Log::info("Retention cleanup completed for job {$this->target->name}: " .
                 "{$result['deleted']} deleted, {$result['kept']} kept");
    }

    /**
     * Clean up old backups for all jobs on a backup server.
     */
    protected function cleanupBackupServer(RetentionService $retentionService): void
    {
        Log::info("Starting retention cleanup for backup server: {$this->target->name}");

        $totalDeleted = 0;
        $totalKept = 0;

        foreach ($this->target->backupJobs as $job) {
            if ($job->retention_policy_days) {
                $result = $retentionService->applyRetentionPolicy($job);
                $totalDeleted += $result['deleted'];
                $totalKept += $result['kept'];
            }
        }

        Log::info("Retention cleanup completed for server {$this->target->name}: " .
                 "{$totalDeleted} total deleted, {$totalKept} total kept");
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        $targetType = $this->target instanceof BackupJob ? 'job' : 'server';
        $targetName = $this->target->name;

        Log::error("Retention cleanup failed permanently for {$targetType} {$targetName}: " .
                  $exception->getMessage());
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $tags = ['retention', 'cleanup'];

        if ($this->target instanceof BackupJob) {
            $tags[] = 'job:' . $this->target->id;
        } elseif ($this->target instanceof BackupServer) {
            $tags[] = 'server:' . $this->target->id;
        }

        return $tags;
    }
}
