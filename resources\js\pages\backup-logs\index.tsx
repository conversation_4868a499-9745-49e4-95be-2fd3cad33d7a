import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Search, MoreHorizontal, Eye, Trash2, Download, RotateCcw, XCircle, CheckCircle, Clock, FileText } from 'lucide-react';

interface BackupLog {
    id: number;
    backup_job: {
        id: number;
        name: string;
        source_server: {
            name: string;
        };
        backup_server: {
            name: string;
        };
    };
    status: 'running' | 'completed' | 'failed' | 'cancelled';
    started_at: string;
    completed_at?: string;
    duration_seconds?: number;
    backup_size_bytes?: number;
    error_message?: string;
}

interface BackupJob {
    id: number;
    name: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface BackupLogsData {
    data: BackupLog[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: PaginationLink[];
}

interface Props {
    backupLogs: BackupLogsData;
    backupJobs: BackupJob[];
}

export default function BackupLogsIndex({ backupLogs, backupJobs }: Props) {
    const { data, setData, get, processing } = useForm({
        search: '',
        status: '',
        backup_job: '',
        date_from: '',
        date_to: '',
    });

    const handleSearch = () => {
        get('/backup-logs', {
            preserveState: true,
            replace: true,
        });
    };

    const handleDelete = (id: number) => {
        if (confirm('Are you sure you want to delete this backup log?')) {
            router.delete(`/backup-logs/${id}`);
        }
    };

    const handleCancel = (id: number) => {
        if (confirm('Are you sure you want to cancel this backup?')) {
            router.post(`/backup-logs/${id}/cancel`);
        }
    };

    const handleRetry = (id: number) => {
        if (confirm('Are you sure you want to retry this backup?')) {
            router.post(`/backup-logs/${id}/retry`);
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed': return 'default';
            case 'running': return 'secondary';
            case 'failed': return 'destructive';
            case 'cancelled': return 'outline';
            default: return 'secondary';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed': return CheckCircle;
            case 'running': return Clock;
            case 'failed': return XCircle;
            case 'cancelled': return XCircle;
            default: return Clock;
        }
    };

    const formatFileSize = (bytes?: number) => {
        if (!bytes) return 'N/A';
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const formatDuration = (seconds?: number) => {
        if (!seconds) return 'N/A';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    };

    return (
        <AppLayout>
            <Head title="Backup Logs" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Backup Logs</h1>
                        <p className="text-muted-foreground">
                            Monitor backup execution history and status
                        </p>
                    </div>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                        <CardDescription>Search and filter backup logs</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                            <Input
                                placeholder="Search jobs..."
                                value={data.search}
                                onChange={(e) => setData('search', e.target.value)}
                            />
                            <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    <SelectItem value="running">Running</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                    <SelectItem value="failed">Failed</SelectItem>
                                    <SelectItem value="cancelled">Cancelled</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={data.backup_job} onValueChange={(value) => setData('backup_job', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Jobs" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Jobs</SelectItem>
                                    {backupJobs.map((job) => (
                                        <SelectItem key={job.id} value={job.id.toString()}>
                                            {job.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Input
                                type="date"
                                placeholder="From date"
                                value={data.date_from}
                                onChange={(e) => setData('date_from', e.target.value)}
                            />
                            <Input
                                type="date"
                                placeholder="To date"
                                value={data.date_to}
                                onChange={(e) => setData('date_to', e.target.value)}
                            />
                            <Button onClick={handleSearch} disabled={processing}>
                                <Search className="mr-2 h-4 w-4" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Backup Logs Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Backup Execution History</CardTitle>
                        <CardDescription>Recent backup job executions and their status</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {backupLogs.data.length > 0 ? (
                            <div className="space-y-4">
                                {backupLogs.data.map((log) => {
                                    const StatusIcon = getStatusIcon(log.status);
                                    return (
                                        <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex items-center space-x-4">
                                                <StatusIcon className="h-5 w-5" />
                                                <div>
                                                    <h4 className="font-medium">{log.backup_job.name}</h4>
                                                    <p className="text-sm text-muted-foreground">
                                                        {log.backup_job.source_server.name} → {log.backup_job.backup_server.name}
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">
                                                        Started: {new Date(log.started_at).toLocaleString()}
                                                        {log.completed_at && ` • Completed: ${new Date(log.completed_at).toLocaleString()}`}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-4">
                                                <div className="text-right text-sm">
                                                    <div>Duration: {formatDuration(log.duration_seconds)}</div>
                                                    <div>Size: {formatFileSize(log.backup_size_bytes)}</div>
                                                </div>
                                                <Badge variant={getStatusColor(log.status)}>
                                                    {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                                                </Badge>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/backup-logs/${log.id}`}>
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View Details
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        {log.status === 'completed' && (
                                                            <DropdownMenuItem onClick={() => router.get(`/backup-logs/${log.id}/download`)}>
                                                                <Download className="mr-2 h-4 w-4" />
                                                                Download
                                                            </DropdownMenuItem>
                                                        )}
                                                        {log.status === 'running' && (
                                                            <DropdownMenuItem onClick={() => handleCancel(log.id)}>
                                                                <XCircle className="mr-2 h-4 w-4" />
                                                                Cancel
                                                            </DropdownMenuItem>
                                                        )}
                                                        {log.status === 'failed' && (
                                                            <DropdownMenuItem onClick={() => handleRetry(log.id)}>
                                                                <RotateCcw className="mr-2 h-4 w-4" />
                                                                Retry
                                                            </DropdownMenuItem>
                                                        )}
                                                        {log.status !== 'running' && (
                                                            <DropdownMenuItem 
                                                                onClick={() => handleDelete(log.id)}
                                                                className="text-destructive"
                                                            >
                                                                <Trash2 className="mr-2 h-4 w-4" />
                                                                Delete
                                                            </DropdownMenuItem>
                                                        )}
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No backup logs found</h3>
                                <p className="text-muted-foreground">
                                    No backup executions match your current filters.
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pagination */}
                {backupLogs.data.length > 0 && (
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                            Showing {((backupLogs.current_page - 1) * backupLogs.per_page) + 1} to{' '}
                            {Math.min(backupLogs.current_page * backupLogs.per_page, backupLogs.total)} of {backupLogs.total} results
                        </div>
                        <div className="flex space-x-2">
                            {backupLogs.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
