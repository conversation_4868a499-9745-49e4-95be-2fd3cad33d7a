<?php

namespace App\Services\Connections;

use App\Models\SourceServer;
use App\Models\BackupServer;
use phpseclib3\Net\SFTP;
use phpseclib3\Crypt\PublicKeyLoader;
use Illuminate\Support\Facades\Log;
use Exception;

class SftpConnection implements ConnectionInterface
{
    protected ?SFTP $sftp = null;
    protected string $lastError = '';
    protected int $timeout = 30;
    protected SourceServer|BackupServer|null $server = null;

    /**
     * Connect to the specified server via SFTP.
     */
    public function connect(SourceServer|BackupServer $server): bool
    {
        try {
            $this->server = $server;
            $this->lastError = '';

            // Create SFTP connection
            $this->sftp = new SFTP($server->ip_address, $server->port ?: 22);
            $this->sftp->setTimeout($this->timeout);

            // Authenticate based on server type and method
            if ($server instanceof SourceServer) {
                $authenticated = $this->authenticateSourceServer($server);
            } else {
                $authenticated = $this->authenticateBackupServer($server);
            }

            if (!$authenticated) {
                $this->lastError = "SFTP authentication failed for server {$server->name}";
                Log::warning($this->lastError);
                return false;
            }

            Log::info("Successfully connected to server via SFTP: {$server->name}");
            return true;

        } catch (Exception $e) {
            $this->lastError = "SFTP connection failed: " . $e->getMessage();
            Log::error("Failed to connect to server {$server->name} via SFTP: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Authenticate to a source server.
     */
    protected function authenticateSourceServer(SourceServer $server): bool
    {
        if ($server->authentication_method === 'password') {
            return $this->sftp->login($server->username, $server->password);
        } elseif ($server->authentication_method === 'key') {
            return $this->authenticateWithPrivateKey($server);
        }

        $this->lastError = "Unsupported authentication method: {$server->authentication_method}";
        return false;
    }

    /**
     * Authenticate to a backup server.
     */
    protected function authenticateBackupServer(BackupServer $server): bool
    {
        return $this->sftp->login($server->username, $server->password);
    }

    /**
     * Authenticate using private key.
     */
    protected function authenticateWithPrivateKey(SourceServer $server): bool
    {
        try {
            if (empty($server->private_key)) {
                $this->lastError = "Private key is empty for server {$server->name}";
                return false;
            }

            $key = PublicKeyLoader::load($server->private_key, $server->private_key_passphrase ?? '');
            return $this->sftp->login($server->username, $key);

        } catch (Exception $e) {
            $this->lastError = "Private key authentication failed: " . $e->getMessage();
            Log::error("Private key authentication failed for server {$server->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Upload a file to the remote server.
     */
    public function uploadFile(string $localPath, string $remotePath, callable $progressCallback = null): bool
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            if ($progressCallback) {
                return $this->sftp->put($remotePath, $localPath, SFTP::SOURCE_LOCAL_FILE, $progressCallback);
            } else {
                return $this->sftp->put($remotePath, $localPath, SFTP::SOURCE_LOCAL_FILE);
            }
        } catch (Exception $e) {
            $this->lastError = "File upload failed: " . $e->getMessage();
            Log::error("SFTP upload failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Download a file from the remote server.
     */
    public function downloadFile(string $remotePath, string $localPath, callable $progressCallback = null): bool
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            if ($progressCallback) {
                return $this->sftp->get($remotePath, $localPath, $progressCallback);
            } else {
                return $this->sftp->get($remotePath, $localPath);
            }
        } catch (Exception $e) {
            $this->lastError = "File download failed: " . $e->getMessage();
            Log::error("SFTP download failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * List directory contents.
     */
    public function listDirectory(string $path = '.'): array
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            $listing = $this->sftp->nlist($path);
            return is_array($listing) ? $listing : [];
        } catch (Exception $e) {
            $this->lastError = "Directory listing failed: " . $e->getMessage();
            Log::error("SFTP directory listing failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get detailed directory listing.
     */
    public function listDirectoryDetailed(string $path = '.'): array
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            $listing = $this->sftp->rawlist($path);
            return is_array($listing) ? $listing : [];
        } catch (Exception $e) {
            $this->lastError = "Detailed directory listing failed: " . $e->getMessage();
            Log::error("SFTP detailed directory listing failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Create a directory.
     */
    public function createDirectory(string $path, int $mode = 0755, bool $recursive = false): bool
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            return $this->sftp->mkdir($path, $mode, $recursive);
        } catch (Exception $e) {
            $this->lastError = "Directory creation failed: " . $e->getMessage();
            Log::error("SFTP directory creation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a file.
     */
    public function deleteFile(string $path): bool
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            return $this->sftp->delete($path);
        } catch (Exception $e) {
            $this->lastError = "File deletion failed: " . $e->getMessage();
            Log::error("SFTP file deletion failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if a file or directory exists.
     */
    public function exists(string $path): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            return $this->sftp->file_exists($path);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get file size.
     */
    public function getFileSize(string $path): int
    {
        if (!$this->isConnected()) {
            return 0;
        }

        try {
            return $this->sftp->filesize($path) ?: 0;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Disconnect from the server.
     */
    public function disconnect(): void
    {
        if ($this->sftp) {
            $this->sftp->disconnect();
            $this->sftp = null;
        }
        $this->server = null;
        Log::debug("SFTP connection disconnected");
    }

    /**
     * Check if currently connected to the server.
     */
    public function isConnected(): bool
    {
        return $this->sftp !== null && $this->sftp->isConnected();
    }

    /**
     * Test the connection to verify it's working.
     */
    public function testConnection(): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            // Test by listing the current directory
            $this->listDirectory('.');
            return true;
        } catch (Exception $e) {
            $this->lastError = "Connection test failed: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Get the last error message if any.
     */
    public function getLastError(): string
    {
        return $this->lastError;
    }

    /**
     * Get connection timeout in seconds.
     */
    public function getTimeout(): int
    {
        return $this->timeout;
    }

    /**
     * Set connection timeout in seconds.
     */
    public function setTimeout(int $timeout): void
    {
        $this->timeout = $timeout;
        if ($this->sftp) {
            $this->sftp->setTimeout($timeout);
        }
    }

    /**
     * Get connection information for debugging.
     */
    public function getConnectionInfo(): array
    {
        if (!$this->server) {
            return [];
        }

        return [
            'type' => 'sftp',
            'host' => $this->server->ip_address,
            'port' => $this->server->port ?: 22,
            'username' => $this->server->username,
            'connected' => $this->isConnected(),
            'timeout' => $this->timeout,
            'auth_method' => $this->server instanceof SourceServer ? $this->server->authentication_method : 'password'
        ];
    }

    /**
     * Get the underlying SFTP connection for advanced operations.
     */
    public function getSftpConnection(): ?SFTP
    {
        return $this->sftp;
    }
}
