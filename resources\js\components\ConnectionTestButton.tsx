import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useConnectionTest } from '@/hooks/useConnectionTest';
import { 
    Play, 
    Loader2, 
    CheckCircle, 
    XCircle, 
    AlertCircle,
    X,
    Wifi
} from 'lucide-react';

interface ConnectionTestButtonProps {
    serverType: 'source' | 'backup';
    serverId: number;
    serverName: string;
    variant?: 'default' | 'outline' | 'ghost';
    size?: 'default' | 'sm' | 'lg';
    className?: string;
    showProgress?: boolean;
    onTestComplete?: (success: boolean, result: any) => void;
}

export function ConnectionTestButton({
    serverType,
    serverId,
    serverName,
    variant = 'default',
    size = 'default',
    className = '',
    showProgress = true,
    onTestComplete
}: ConnectionTestButtonProps) {
    const { isLoading, status, error, startTest, cancelTest, reset, cleanup, testId } = useConnectionTest({
        serverType,
        serverId
    });

    useEffect(() => {
        return cleanup;
    }, [cleanup]);

    useEffect(() => {
        if (status && ['completed', 'failed'].includes(status.status) && onTestComplete) {
            onTestComplete(status.success || false, status);
        }
    }, [status, onTestComplete]);

    const handleStartTest = () => {
        startTest(serverType, serverId);
    };

    const handleCancel = () => {
        if ((status?.status === 'running' || status?.status === 'pending') && testId) {
            // Cancel the actual test if we have a test ID
            cancelTest(testId);
        } else {
            // Just reset the UI state
            reset();
        }
    };

    const getStatusIcon = () => {
        if (isLoading || status?.status === 'pending' || status?.status === 'running') {
            return <Loader2 className="h-4 w-4 animate-spin" />;
        }
        
        if (status?.status === 'completed' && status.success) {
            return <CheckCircle className="h-4 w-4 text-green-600" />;
        }
        
        if (status?.status === 'failed' || error) {
            return <XCircle className="h-4 w-4 text-red-600" />;
        }
        
        if (status?.status === 'cancelled') {
            return <AlertCircle className="h-4 w-4 text-yellow-600" />;
        }
        
        return <Play className="h-4 w-4" />;
    };

    const getStatusText = () => {
        if (error) return 'Test Connection';
        if (!status) return 'Test Connection';

        switch (status.status) {
            case 'pending':
                return 'Queuing Test...';
            case 'running':
                return `Testing... ${status.progress || 0}%`;
            case 'completed':
                return status.success ? '✓ Connected' : '✗ Failed';
            case 'failed':
                return '✗ Failed';
            case 'cancelled':
                return '⚠ Cancelled';
            default:
                return 'Test Connection';
        }
    };

    const getStatusColor = () => {
        if (error) return 'destructive';
        if (!status) return variant;
        
        switch (status.status) {
            case 'pending':
            case 'running':
                return 'secondary';
            case 'completed':
                return status.success ? 'default' : 'destructive';
            case 'failed':
                return 'destructive';
            case 'cancelled':
                return 'outline';
            default:
                return variant;
        }
    };

    const isTestActive = isLoading || (status && ['pending', 'running'].includes(status.status));

    return (
        <div className={`space-y-2 ${className}`}>
            <div className="flex items-center space-x-2">
                <Button
                    variant={getStatusColor() as any}
                    size={size}
                    onClick={isTestActive ? handleCancel : handleStartTest}
                    disabled={false}
                    className="flex-1"
                >
                    {getStatusIcon()}
                    <span className="ml-2">{getStatusText()}</span>
                </Button>
                
                {isTestActive && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCancel}
                        className="px-2"
                    >
                        <X className="h-4 w-4" />
                    </Button>
                )}
            </div>

            {showProgress && (status || error) && (
                <div className="space-y-2">
                    {/* Progress bar for running tests */}
                    {status && (status.status === 'running' || status.status === 'pending') && (
                        <div className="space-y-1">
                            <Progress
                                value={status.progress || (status.status === 'pending' ? 10 : 0)}
                                className="h-2"
                            />
                            <div className="flex items-center justify-between text-xs">
                                <span className="text-muted-foreground">
                                    {status.message || (status.status === 'pending' ? 'Queuing test...' : 'Testing connection...')}
                                </span>
                                <span className="text-muted-foreground">
                                    {status.progress || (status.status === 'pending' ? 10 : 0)}%
                                </span>
                            </div>
                        </div>
                    )}

                    {/* Result messages for completed tests */}
                    {status && ['completed', 'failed', 'cancelled'].includes(status.status) && (
                        <>
                            {/* Success message */}
                            {status.status === 'completed' && status.success && (
                                <div className="p-2 bg-green-50 border border-green-200 rounded text-xs">
                                    <div className="font-medium text-green-800 mb-1">Connection Successful!</div>
                                    <div className="text-green-600">
                                        Server is reachable and credentials are valid.
                                        {status.duration_ms && (
                                            <span className="block mt-1">Response time: {status.duration_ms}ms</span>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Error message */}
                            {(status.status === 'failed' || !status.success) && (
                                <div className="p-2 bg-red-50 border border-red-200 rounded text-xs">
                                    <div className="font-medium text-red-800 mb-1">Connection Failed</div>
                                    <div className="text-red-600">
                                        {status.error_message || status.message || 'Connection test failed'}
                                        {status.duration_ms && (
                                            <span className="block mt-1">Timeout after: {status.duration_ms}ms</span>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Cancelled message */}
                            {status.status === 'cancelled' && (
                                <div className="p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
                                    <div className="font-medium text-yellow-800 mb-1">Test Cancelled</div>
                                    <div className="text-yellow-600">
                                        Connection test was cancelled by user.
                                    </div>
                                </div>
                            )}

                            {/* Duration and timing info */}
                            {status.completed_at && (
                                <div className="text-xs text-muted-foreground text-center">
                                    Completed: {new Date(status.completed_at).toLocaleTimeString()}
                                </div>
                            )}
                        </>
                    )}

                    {/* Error from hook (not from status) */}
                    {error && !status && (
                        <div className="p-2 bg-red-50 border border-red-200 rounded text-xs">
                            <div className="font-medium text-red-800 mb-1">Error</div>
                            <div className="text-red-600">{error}</div>
                        </div>
                    )}
                </div>
            )}

            {/* Status badge for completed tests */}
            {status && ['completed', 'failed', 'cancelled'].includes(status.status) && (
                <div className="flex items-center space-x-2">
                    <Badge variant={status.success ? 'default' : 'destructive'}>
                        {status.success ? 'Connected' : 'Failed'}
                    </Badge>
                    {status.completed_at && (
                        <span className="text-xs text-muted-foreground">
                            {new Date(status.completed_at).toLocaleTimeString()}
                        </span>
                    )}
                </div>
            )}
        </div>
    );
}
