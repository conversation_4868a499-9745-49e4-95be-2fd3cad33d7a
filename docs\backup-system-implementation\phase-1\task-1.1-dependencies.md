# Task 1.1: Install Required Dependencies ✅ COMPLETED

## Overview
Install all necessary PHP and JavaScript dependencies required for backup system functionality including SSH/SFTP libraries, CRON expression parsing, compression tools, and process management.

## Subtasks

### Subtask 1.1.1: Install SSH/SFTP Libraries

**Description:** Add PHP libraries for secure server connections and file transfers.

**Implementation Steps:**
1. Install phpseclib for SSH/SFTP connections:
   ```bash
   composer require phpseclib/phpseclib
   ```

2. Install Flysystem SFTP adapter:
   ```bash
   composer require league/flysystem-sftp-v3
   ```

**Manual Testing:**
- Verify installation: `composer show | grep phpseclib`
- Verify installation: `composer show | grep flysystem-sftp`
- Check autoloading: `php artisan tinker` then `use phpseclib3\Net\SSH2;`

### Subtask 1.1.2: Install CRON Expression Parser

**Description:** Add library for parsing and calculating CRON schedule expressions.

**Implementation Steps:**
1. Install CRON expression library:
   ```bash
   composer require dragonmantank/cron-expression
   ```

**Manual Testing:**
- Verify installation: `composer show | grep cron-expression`
- Test in tinker:
  ```php
  php artisan tinker
  use Cron\CronExpression;
  $cron = new CronExpression('0 2 * * *');
  echo $cron->getNextRunDate()->format('Y-m-d H:i:s');
  ```

### Subtask 1.1.3: Install Compression Libraries

**Description:** Add libraries for creating compressed archives and handling file compression.

**Implementation Steps:**
1. Install Zippy for archive creation:
   ```bash
   composer require alchemy/zippy
   ```

2. Verify system has required compression tools:
   ```bash
   which tar
   which gzip
   which zip
   ```

**Manual Testing:**
- Verify installation: `composer show | grep zippy`
- Test archive creation in tinker:
  ```php
  php artisan tinker
  use Alchemy\Zippy\Zippy;
  $zippy = Zippy::load();
  // This should not throw an error
  ```

### Subtask 1.1.4: Install Process Management

**Description:** Add Symfony Process component for executing system commands safely.

**Implementation Steps:**
1. Install Symfony Process component:
   ```bash
   composer require symfony/process
   ```

**Manual Testing:**
- Verify installation: `composer show | grep symfony/process`
- Test process execution in tinker:
  ```php
  php artisan tinker
  use Symfony\Component\Process\Process;
  $process = new Process(['echo', 'Hello World']);
  $process->run();
  echo $process->getOutput();
  ```

### Subtask 1.1.5: Install Additional Utilities

**Description:** Add supporting libraries for enhanced functionality.

**Implementation Steps:**
1. Install Carbon for advanced date handling:
   ```bash
   composer require nesbot/carbon
   ```

2. Install Monolog for advanced logging:
   ```bash
   composer require monolog/monolog
   ```

**Manual Testing:**
- Verify installations: `composer show | grep -E "(carbon|monolog)"`
- Test Carbon in tinker:
  ```php
  php artisan tinker
  use Carbon\Carbon;
  echo Carbon::now()->addHours(2)->format('Y-m-d H:i:s');
  ```

## Verification Checklist

After completing all subtasks, verify the following:

- [ ] All composer packages are installed without conflicts
- [ ] `composer.json` includes all new dependencies
- [ ] `composer.lock` is updated
- [ ] No version conflicts exist
- [ ] All libraries can be imported in PHP
- [ ] System compression tools are available

## Expected Files Modified

- `composer.json` - New dependencies added
- `composer.lock` - Updated with new package versions
- `vendor/` directory - New packages installed

## Troubleshooting

**Common Issues:**
1. **Version conflicts:** Run `composer update` to resolve dependencies
2. **Missing system tools:** Install required compression tools via package manager
3. **Memory issues:** Increase PHP memory limit in `php.ini`

**System Requirements:**
- PHP 8.2+
- Composer 2.0+
- System tools: tar, gzip, zip (for compression)
- OpenSSL extension (for encryption)

## Next Steps

After completing this task, proceed to [Task 1.2: Create Service Layer Architecture](./task-1.2-service-layer.md).

---

## ✅ TASK COMPLETED

**Completion Date:** 2025-06-14
**Status:** All dependencies successfully installed and tested

**Installed Packages:**
- ✅ phpseclib/phpseclib (v3.0.43) - SSH/SFTP connections
- ✅ league/flysystem-sftp-v3 (v3.29.0) - Flysystem SFTP adapter
- ✅ dragonmantank/cron-expression (v3.4.0) - CRON expression parser
- ✅ symfony/process (v7.3.0) - Process management
- ✅ nesbot/carbon (v3.10.0) - Date handling (already included with Laravel)
- ✅ monolog/monolog (v3.9.0) - Advanced logging (already included with Laravel)

**System Tools Verified:**
- ✅ tar (/usr/bin/tar)
- ✅ gzip (/usr/bin/gzip)
- ✅ zip (/usr/bin/zip)

**Manual Testing Results:**
- ✅ phpseclib SSH2 class loads successfully
- ✅ CRON expression parser calculates next run dates correctly
- ✅ Symfony Process executes commands successfully
- ✅ Carbon date manipulation works correctly

**Code Updates:**
- ✅ Updated `BackupJob::calculateNextRun()` method to use CRON expression parser

**Note:** Skipped alchemy/zippy due to Symfony version compatibility issues. Will use native compression tools with Symfony Process instead.
