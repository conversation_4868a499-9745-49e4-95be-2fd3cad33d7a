<?php

namespace App\Services\Connections;

use App\Models\SourceServer;
use App\Models\BackupServer;

interface ConnectionInterface
{
    /**
     * Connect to the specified server.
     */
    public function connect(SourceServer|BackupServer $server): bool;

    /**
     * Disconnect from the server.
     */
    public function disconnect(): void;

    /**
     * Check if currently connected to the server.
     */
    public function isConnected(): bool;

    /**
     * Test the connection to verify it's working.
     */
    public function testConnection(): bool;

    /**
     * Get the last error message if any.
     */
    public function getLastError(): string;

    /**
     * Get connection timeout in seconds.
     */
    public function getTimeout(): int;

    /**
     * Set connection timeout in seconds.
     */
    public function setTimeout(int $timeout): void;

    /**
     * Get connection information for debugging.
     */
    public function getConnectionInfo(): array;
}
