import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save } from 'lucide-react';

export default function CreateSourceServer() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        ip_address: '',
        port: 22,
        username: '',
        authentication_method: 'password' as 'password' | 'private_key',
        password: '',
        private_key: '',
        description: '',
        is_active: true,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/source-servers');
    };

    return (
        <AppLayout>
            <Head title="Create Source Server" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center space-x-4">
                    <Link href="/source-servers">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Create Source Server</h1>
                        <p className="text-muted-foreground">
                            Add a new SSH server to backup
                        </p>
                    </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Server Information</CardTitle>
                            <CardDescription>Basic information about the source server</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Server Name *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., Production Web Server"
                                        className={errors.name ? 'border-destructive' : ''}
                                    />
                                    {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="ip_address">Server Address *</Label>
                                    <Input
                                        id="ip_address"
                                        value={data.ip_address}
                                        onChange={(e) => setData('ip_address', e.target.value)}
                                        placeholder="e.g., ************* or server.example.com"
                                        className={errors.ip_address ? 'border-destructive' : ''}
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Enter an IP address (*************) or domain name (server.example.com)
                                    </p>
                                    {errors.ip_address && <p className="text-sm text-destructive">{errors.ip_address}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="port">SSH Port *</Label>
                                    <Input
                                        id="port"
                                        type="number"
                                        value={data.port.toString()}
                                        onChange={(e) => setData('port', parseInt(e.target.value) || 22)}
                                        placeholder="22"
                                        min="1"
                                        max="65535"
                                        className={errors.port ? 'border-destructive' : ''}
                                    />
                                    {errors.port && <p className="text-sm text-destructive">{errors.port}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="username">Username *</Label>
                                    <Input
                                        id="username"
                                        value={data.username}
                                        onChange={(e) => setData('username', e.target.value)}
                                        placeholder="e.g., root, ubuntu"
                                        className={errors.username ? 'border-destructive' : ''}
                                    />
                                    {errors.username && <p className="text-sm text-destructive">{errors.username}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Optional description of this server"
                                    rows={3}
                                />
                                {errors.description && <p className="text-sm text-destructive">{errors.description}</p>}
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                />
                                <Label htmlFor="is_active">Active</Label>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Authentication</CardTitle>
                            <CardDescription>Configure how to authenticate with this server</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="authentication_method">Authentication Method *</Label>
                                <Select
                                    value={data.authentication_method}
                                    onValueChange={(value: 'password' | 'private_key') => setData('authentication_method', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select authentication method" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="password">Password</SelectItem>
                                        <SelectItem value="private_key">Private Key</SelectItem>
                                    </SelectContent>
                                </Select>
                                {errors.authentication_method && <p className="text-sm text-destructive">{errors.authentication_method}</p>}
                            </div>

                            {data.authentication_method === 'password' && (
                                <div className="space-y-2">
                                    <Label htmlFor="password">Password *</Label>
                                    <Input
                                        id="password"
                                        type="password"
                                        value={data.password}
                                        onChange={(e) => setData('password', e.target.value)}
                                        placeholder="Enter SSH password"
                                        className={errors.password ? 'border-destructive' : ''}
                                    />
                                    {errors.password && <p className="text-sm text-destructive">{errors.password}</p>}
                                </div>
                            )}

                            {data.authentication_method === 'private_key' && (
                                <div className="space-y-2">
                                    <Label htmlFor="private_key">Private Key *</Label>
                                    <Textarea
                                        id="private_key"
                                        value={data.private_key}
                                        onChange={(e) => setData('private_key', e.target.value)}
                                        placeholder="-----BEGIN PRIVATE KEY-----&#10;...&#10;-----END PRIVATE KEY-----"
                                        rows={8}
                                        className={`font-mono text-sm ${errors.private_key ? 'border-destructive' : ''}`}
                                    />
                                    {errors.private_key && <p className="text-sm text-destructive">{errors.private_key}</p>}
                                    <p className="text-sm text-muted-foreground">
                                        Paste your SSH private key here. The key will be encrypted and stored securely.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Actions */}
                    <div className="flex items-center justify-end space-x-4">
                        <Link href="/source-servers">
                            <Button variant="outline">Cancel</Button>
                        </Link>
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Creating...' : 'Create Source Server'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
