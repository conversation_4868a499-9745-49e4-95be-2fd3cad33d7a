<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Action extends Model
{
    protected $fillable = [
        'name',
        'source_server_id',
        'command',
        'template',
        'created_by',
    ];

    public function sourceServer()
    {
        return $this->belongsTo(SourceServer::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function logs()
    {
        return $this->hasMany(ActionLog::class);
    }
}
