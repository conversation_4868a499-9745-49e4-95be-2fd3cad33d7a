<?php

namespace App\Http\Controllers;

use App\Models\BackupServer;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class BackupServerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $query = BackupServer::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        // Filter by protocol
        if ($request->filled('protocol') && $request->get('protocol') !== 'all') {
            $query->where('protocol', $request->get('protocol'));
        }

        // Filter by status
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $query->where('is_active', $request->get('status') === 'active');
        }

        $backupServers = $query->with('backupJobs')->orderBy('name')->paginate(10);

        return Inertia::render('backup-servers/index', [
            'backupServers' => $backupServers,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        return Inertia::render('backup-servers/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:backup_servers',
            'protocol' => 'required|in:ftp,sftp',
            'ip_address' => 'required|string|max:255|unique:backup_servers',
            'port' => 'nullable|integer|min:1|max:65535',
            'username' => 'required|string|max:255',
            'password' => 'required|string',
            'base_directory' => 'required|string|max:500',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'connection_options' => 'nullable|array',
        ]);

        // Validate that ip_address is either a valid IP or a valid domain name
        $isValidIP = filter_var($validated['ip_address'], FILTER_VALIDATE_IP);
        $isValidDomain = filter_var($validated['ip_address'], FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) &&
                        strpos($validated['ip_address'], '.') !== false; // Must contain at least one dot

        if (!$isValidIP && !$isValidDomain) {
            return redirect()->back()
                ->withErrors(['ip_address' => 'The server address must be a valid IP address or domain name (e.g., ************* or ftp.example.com).'])
                ->withInput();
        }

        // Set default port based on protocol if not provided
        if (!$validated['port']) {
            $validated['port'] = $validated['protocol'] === 'ftp' ? 21 : 22;
        }

        $backupServer = BackupServer::create($validated);

        return redirect()->route('backup-servers.index')
            ->with('success', 'Backup server created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(BackupServer $backupServer): Response
    {
        $backupServer->load('backupJobs.sourceServer');

        return Inertia::render('backup-servers/show', [
            'backupServer' => $backupServer,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BackupServer $backupServer): Response
    {
        return Inertia::render('backup-servers/edit', [
            'backupServer' => $backupServer,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BackupServer $backupServer): RedirectResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('backup_servers')->ignore($backupServer)],
            'protocol' => 'required|in:ftp,sftp',
            'ip_address' => ['required', 'string', 'max:255', Rule::unique('backup_servers')->ignore($backupServer)],
            'port' => 'nullable|integer|min:1|max:65535',
            'username' => 'required|string|max:255',
            'password' => 'nullable|string',
            'base_directory' => 'required|string|max:500',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'connection_options' => 'nullable|array',
        ]);

        // Validate that ip_address is either a valid IP or a valid domain name
        $isValidIP = filter_var($validated['ip_address'], FILTER_VALIDATE_IP);
        $isValidDomain = filter_var($validated['ip_address'], FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) &&
                        strpos($validated['ip_address'], '.') !== false; // Must contain at least one dot

        if (!$isValidIP && !$isValidDomain) {
            return redirect()->back()
                ->withErrors(['ip_address' => 'The server address must be a valid IP address or domain name (e.g., ************* or ftp.example.com).'])
                ->withInput();
        }

        // Set default port based on protocol if not provided
        if (!$validated['port']) {
            $validated['port'] = $validated['protocol'] === 'ftp' ? 21 : 22;
        }

        // Only update password if provided
        if (empty($validated['password'])) {
            unset($validated['password']);
        }

        $backupServer->update($validated);

        return redirect()->route('backup-servers.index')
            ->with('success', 'Backup server updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BackupServer $backupServer): RedirectResponse
    {
        // Check if server has active backup jobs
        if ($backupServer->backupJobs()->where('status', 'active')->exists()) {
            return redirect()->route('backup-servers.index')
                ->with('error', 'Cannot delete backup server with active backup jobs.');
        }

        $backupServer->delete();

        return redirect()->route('backup-servers.index')
            ->with('success', 'Backup server deleted successfully.');
    }

    /**
     * Test connection to the backup server.
     */
    public function testConnection(BackupServer $backupServer): RedirectResponse
    {
        try {
            $statusService = app(\App\Services\ConnectionStatusService::class);
            $testId = $statusService->startConnectionTest($backupServer);

            return redirect()->back()
                ->with('success', 'Connection test started successfully')
                ->with('test_id', $testId)
                ->with('status_url', route('connection-test.status', ['testId' => $testId]));

        } catch (\Exception $e) {
            \Log::error("Failed to start connection test for backup server {$backupServer->name}: " . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Failed to start connection test: ' . $e->getMessage());
        }
    }
}
