import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarMenuSub, SidebarMenuSubItem, SidebarMenuSubButton } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import React from 'react';

export function NavMain({ items = [] }: { items: NavItem[] }) {
    const page = usePage();
    const [open, setOpen] = React.useState(false);
    // Filter backup-related items
    const backupItems = items.filter(item => [
        'Backup Dashboard',
        'Source Servers',
        'Backup Servers',
        'Backup Jobs',
        'Backup Logs',
    ].includes(item.title));
    // Other items
    const otherItems = items.filter(item => ![
        'Backup Dashboard',
        'Source Servers',
        'Backup Servers',
        'Backup Jobs',
        'Backup Logs',
    ].includes(item.title));
    // Responsive: close sub-menu on mobile nav change
    React.useEffect(() => {
        setOpen(false);
    }, [page.url]);
    return (
        <SidebarGroup className="px-2 py-0">
            <SidebarGroupLabel>Backup Platform</SidebarGroupLabel>
            <SidebarMenu>
                {/* Other main items */}
                {otherItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild isActive={page.url.startsWith(item.href)} tooltip={{ children: item.title }}>
                            <Link href={item.href} prefetch>
                                {item.icon && <item.icon />}
                                <span>{item.title}</span>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                ))}
                {/* Add Actions menu item to main menu */}
                <SidebarMenuItem key="Actions">
                    <SidebarMenuButton asChild isActive={page.url.startsWith('/actions')} tooltip={{ children: 'Actions' }}>
                        <Link href="/actions" prefetch>
                            <span>Actions</span>
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
                {/* Backup parent menu with collapsible sub-menu */}
                <SidebarMenuItem>
                    <SidebarMenuButton
                        onClick={() => setOpen((v) => !v)}
                        aria-expanded={open}
                        aria-controls="backup-submenu"
                        className="flex justify-between items-center"
                    >
                        <span>Backup</span>
                        <svg className={`ml-2 transition-transform duration-200 ${open ? 'rotate-90' : ''}`} width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M9 5l7 7-7 7"/></svg>
                    </SidebarMenuButton>
                    <SidebarMenuSub id="backup-submenu" style={{ display: open ? 'block' : 'none' }}>
                        {backupItems.map((item) => (
                            <SidebarMenuSubItem key={item.title}>
                                <SidebarMenuSubButton asChild isActive={page.url.startsWith(item.href)}>
                                    <Link href={item.href} prefetch>
                                        {item.icon && <item.icon />}
                                        <span>{item.title}</span>
                                    </Link>
                                </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                        ))}
                    </SidebarMenuSub>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarGroup>
    );
}
