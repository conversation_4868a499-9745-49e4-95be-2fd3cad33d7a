<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BackupProgress extends Model
{
    use HasFactory;

    protected $table = 'backup_progress';

    protected $fillable = [
        'backup_log_id',
        'stage',
        'percentage',
        'message',
        'stage_data',
        'files_processed',
        'total_files',
        'bytes_processed',
        'total_bytes',
        'transfer_speed_mbps',
        'stage_started_at',
        'stage_completed_at',
        'is_current_stage',
    ];

    protected $casts = [
        'percentage' => 'decimal:2',
        'stage_data' => 'array',
        'files_processed' => 'integer',
        'total_files' => 'integer',
        'bytes_processed' => 'integer',
        'total_bytes' => 'integer',
        'transfer_speed_mbps' => 'decimal:2',
        'stage_started_at' => 'datetime',
        'stage_completed_at' => 'datetime',
        'is_current_stage' => 'boolean',
    ];

    /**
     * Get the backup log that owns this progress record.
     */
    public function backupLog(): BelongsTo
    {
        return $this->belongsTo(BackupLog::class);
    }

    /**
     * Get the progress percentage as a formatted string.
     */
    public function getFormattedPercentageAttribute(): string
    {
        return number_format($this->percentage, 2) . '%';
    }

    /**
     * Get the transfer speed in human readable format.
     */
    public function getFormattedSpeedAttribute(): string
    {
        if (!$this->transfer_speed_mbps) {
            return 'N/A';
        }

        if ($this->transfer_speed_mbps >= 1000) {
            return number_format($this->transfer_speed_mbps / 1000, 2) . ' GB/s';
        }

        return number_format($this->transfer_speed_mbps, 2) . ' MB/s';
    }

    /**
     * Get the bytes processed in human readable format.
     */
    public function getFormattedBytesProcessedAttribute(): string
    {
        return $this->formatBytes($this->bytes_processed);
    }

    /**
     * Get the total bytes in human readable format.
     */
    public function getFormattedTotalBytesAttribute(): string
    {
        return $this->formatBytes($this->total_bytes);
    }

    /**
     * Format bytes into human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes === 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor(log($bytes, 1024));

        return round($bytes / pow(1024, $factor), 2) . ' ' . $units[$factor];
    }

    /**
     * Check if this stage is completed.
     */
    public function isCompleted(): bool
    {
        return !is_null($this->stage_completed_at);
    }

    /**
     * Get the stage duration in seconds.
     */
    public function getStageDurationAttribute(): ?int
    {
        if (!$this->stage_started_at || !$this->stage_completed_at) {
            return null;
        }

        return $this->stage_completed_at->diffInSeconds($this->stage_started_at);
    }
}
