import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface MonthlyTotalsProps {
    monthlyTotals: Record<number, string>;
    currentMonth: number;
    year: number;
    onMonthClick?: (month: number) => void;
}

const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
];

export default function MonthlyTotals({ 
    monthlyTotals, 
    currentMonth, 
    year, 
    onMonthClick 
}: MonthlyTotalsProps) {
    const formatCurrency = (value: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(parseFloat(value));
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Monthly Totals - {year}</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    {monthNames.map((monthName, index) => {
                        const monthNumber = index + 1;
                        const total = monthlyTotals[monthNumber] || '0';
                        const isCurrentMonth = monthNumber === currentMonth;
                        
                        return (
                            <div 
                                key={monthNumber}
                                className={`p-3 rounded-lg border transition-colors ${
                                    isCurrentMonth 
                                        ? 'border-primary bg-primary/5' 
                                        : 'border-border hover:border-primary/50'
                                } ${onMonthClick ? 'cursor-pointer' : ''}`}
                                onClick={() => onMonthClick?.(monthNumber)}
                            >
                                <div className="text-sm font-medium">{monthName}</div>
                                <div className="text-lg font-bold text-primary">
                                    {formatCurrency(total)}
                                </div>
                                {parseFloat(total) > 0 && (
                                    <div className="text-xs text-muted-foreground mt-1">
                                        {parseFloat(total) > 1000 ? 'High' : parseFloat(total) > 500 ? 'Medium' : 'Low'}
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            </CardContent>
        </Card>
    );
}
