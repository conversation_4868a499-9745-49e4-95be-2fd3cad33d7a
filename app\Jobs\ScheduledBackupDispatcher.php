<?php

namespace App\Jobs;

use App\Services\ScheduleService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ScheduledBackupDispatcher implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 2;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(ScheduleService $scheduleService): void
    {
        Log::info("Starting scheduled backup dispatcher");

        try {
            $dueJobs = $scheduleService->getJobsDueForExecution();

            if ($dueJobs->isEmpty()) {
                Log::info("No backup jobs due for execution");
                return;
            }

            Log::info("Found {$dueJobs->count()} backup jobs due for execution");

            foreach ($dueJobs as $job) {
                try {
                    // Check if there's already a running backup for this job
                    $runningBackup = $job->backupLogs()
                        ->where('status', 'running')
                        ->where('started_at', '>=', now()->subHours(2))
                        ->exists();

                    if ($runningBackup) {
                        Log::warning("Skipping job {$job->name} - backup already running");
                        continue;
                    }

                    // Dispatch the backup job
                    ExecuteBackupJob::dispatch($job);

                    Log::info("Dispatched backup job: {$job->name}");

                } catch (Exception $e) {
                    Log::error("Failed to dispatch backup job {$job->name}: " . $e->getMessage());
                }
            }

            Log::info("Scheduled backup dispatcher completed");

        } catch (Exception $e) {
            Log::error("Scheduled backup dispatcher failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("Scheduled backup dispatcher failed permanently: " . $exception->getMessage());
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['scheduler', 'dispatcher'];
    }
}
