<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('backup_log_id')->constrained('backup_logs')->onDelete('cascade');
            $table->string('metric_type'); // transfer_speed, compression_ratio, cpu_usage, memory_usage, etc.
            $table->string('metric_name');
            $table->decimal('metric_value', 15, 4);
            $table->string('metric_unit')->nullable(); // MB/s, %, ratio, etc.
            $table->json('metric_metadata')->nullable(); // Additional context data
            $table->timestamp('measured_at');
            $table->timestamps();

            // Indexes for performance
            $table->index(['backup_log_id', 'metric_type']);
            $table->index(['backup_log_id', 'measured_at']);
            $table->index(['metric_type', 'measured_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_metrics');
    }
};
