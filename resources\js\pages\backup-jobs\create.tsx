import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Calendar, Server, HardDrive } from 'lucide-react';

interface SourceServer {
    id: number;
    name: string;
    ip_address: string;
}

interface BackupServer {
    id: number;
    name: string;
    protocol: string;
    ip_address: string;
}

interface Props {
    sourceServers: SourceServer[];
    backupServers: BackupServer[];
}

export default function BackupJobCreate({ sourceServers, backupServers }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        source_server_id: '',
        backup_server_id: '',
        source_path: '/',
        destination_path: '',
        schedule: '0 2 * * *', // Daily at 2 AM
        status: 'active' as 'active' | 'paused' | 'disabled',
        retention_policy_days: 30,
        compression_enabled: true,
        encryption_enabled: false,
        description: '',
        backup_options: {},
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/backup-jobs');
    };

    const schedulePresets = [
        { value: '0 2 * * *', label: 'Daily at 2:00 AM' },
        { value: '0 2 * * 0', label: 'Weekly (Sunday at 2:00 AM)' },
        { value: '0 2 1 * *', label: 'Monthly (1st day at 2:00 AM)' },
        { value: '0 */6 * * *', label: 'Every 6 hours' },
        { value: '0 */12 * * *', label: 'Every 12 hours' },
        { value: 'custom', label: 'Custom CRON expression' },
    ];

    return (
        <AppLayout>
            <Head title="Create Backup Job" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href="/backup-jobs">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Backup Jobs
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Create Backup Job</h1>
                            <p className="text-muted-foreground">
                                Schedule automated backups between source and destination servers
                            </p>
                        </div>
                    </div>
                </div>

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Calendar className="mr-2 h-5 w-5" />
                            Backup Job Configuration
                        </CardTitle>
                        <CardDescription>
                            Configure the backup source, destination, schedule, and options
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Basic Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Job Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Daily Website Backup"
                                            className={errors.name ? 'border-destructive' : ''}
                                        />
                                        {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="source_server_id">Source Server *</Label>
                                        <Select
                                            value={data.source_server_id}
                                            onValueChange={(value) => setData('source_server_id', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select source server" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {sourceServers.map((server) => (
                                                    <SelectItem key={server.id} value={server.id.toString()}>
                                                        <div className="flex items-center">
                                                            <Server className="mr-2 h-4 w-4" />
                                                            {server.name} ({server.ip_address})
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.source_server_id && <p className="text-sm text-destructive">{errors.source_server_id}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="backup_server_id">Backup Server *</Label>
                                        <Select
                                            value={data.backup_server_id}
                                            onValueChange={(value) => setData('backup_server_id', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select backup server" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {backupServers.map((server) => (
                                                    <SelectItem key={server.id} value={server.id.toString()}>
                                                        <div className="flex items-center">
                                                            <HardDrive className="mr-2 h-4 w-4" />
                                                            {server.name} ({server.protocol.toUpperCase()})
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.backup_server_id && <p className="text-sm text-destructive">{errors.backup_server_id}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="status">Status *</Label>
                                        <Select
                                            value={data.status}
                                            onValueChange={(value: 'active' | 'paused' | 'disabled') => setData('status', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="paused">Paused</SelectItem>
                                                <SelectItem value="disabled">Disabled</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.status && <p className="text-sm text-destructive">{errors.status}</p>}
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="source_path">Source Path *</Label>
                                        <Input
                                            id="source_path"
                                            value={data.source_path}
                                            onChange={(e) => setData('source_path', e.target.value)}
                                            placeholder="/var/www/html"
                                            className={errors.source_path ? 'border-destructive' : ''}
                                        />
                                        {errors.source_path && <p className="text-sm text-destructive">{errors.source_path}</p>}
                                        <p className="text-xs text-muted-foreground">
                                            Directory or file path to backup from source server
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="destination_path">Destination Path *</Label>
                                        <Input
                                            id="destination_path"
                                            value={data.destination_path}
                                            onChange={(e) => setData('destination_path', e.target.value)}
                                            placeholder="/backups/website"
                                            className={errors.destination_path ? 'border-destructive' : ''}
                                        />
                                        {errors.destination_path && <p className="text-sm text-destructive">{errors.destination_path}</p>}
                                        <p className="text-xs text-muted-foreground">
                                            Directory path on backup server to store backups
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="retention_policy_days">Retention Policy (Days)</Label>
                                        <Input
                                            id="retention_policy_days"
                                            type="number"
                                            value={data.retention_policy_days?.toString() || ''}
                                            onChange={(e) => setData('retention_policy_days', parseInt(e.target.value) || null)}
                                            placeholder="30"
                                            min="1"
                                            max="3650"
                                            className={errors.retention_policy_days ? 'border-destructive' : ''}
                                        />
                                        {errors.retention_policy_days && <p className="text-sm text-destructive">{errors.retention_policy_days}</p>}
                                        <p className="text-xs text-muted-foreground">
                                            How many days to keep backups (leave empty for no limit)
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="description">Description</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            placeholder="Optional description for this backup job"
                                            rows={3}
                                            className={errors.description ? 'border-destructive' : ''}
                                        />
                                        {errors.description && <p className="text-sm text-destructive">{errors.description}</p>}
                                    </div>
                                </div>
                            </div>

                            {/* Schedule Configuration */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Schedule Configuration</h3>
                                
                                <div className="space-y-2">
                                    <Label htmlFor="schedule_preset">Schedule Preset</Label>
                                    <Select
                                        value={schedulePresets.find(p => p.value === data.schedule)?.value || 'custom'}
                                        onValueChange={(value) => {
                                            if (value !== 'custom') {
                                                setData('schedule', value);
                                            }
                                        }}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select schedule preset" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {schedulePresets.map((preset) => (
                                                <SelectItem key={preset.value} value={preset.value}>
                                                    {preset.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="schedule">CRON Expression *</Label>
                                    <Input
                                        id="schedule"
                                        value={data.schedule}
                                        onChange={(e) => setData('schedule', e.target.value)}
                                        placeholder="0 2 * * *"
                                        className={errors.schedule ? 'border-destructive' : ''}
                                    />
                                    {errors.schedule && <p className="text-sm text-destructive">{errors.schedule}</p>}
                                    <p className="text-xs text-muted-foreground">
                                        CRON format: minute hour day month weekday (e.g., "0 2 * * *" = daily at 2 AM)
                                    </p>
                                </div>
                            </div>

                            {/* Backup Options */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Backup Options</h3>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="compression_enabled"
                                            checked={data.compression_enabled}
                                            onCheckedChange={(checked) => setData('compression_enabled', checked)}
                                        />
                                        <Label htmlFor="compression_enabled">Enable Compression</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="encryption_enabled"
                                            checked={data.encryption_enabled}
                                            onCheckedChange={(checked) => setData('encryption_enabled', checked)}
                                        />
                                        <Label htmlFor="encryption_enabled">Enable Encryption</Label>
                                    </div>
                                </div>

                                <div className="p-4 bg-muted rounded-lg text-sm">
                                    <h4 className="font-medium mb-2">Backup Options Info:</h4>
                                    <ul className="space-y-1 text-muted-foreground">
                                        <li>• <strong>Compression:</strong> Reduces backup file size but increases CPU usage</li>
                                        <li>• <strong>Encryption:</strong> Encrypts backup files for security (recommended for sensitive data)</li>
                                    </ul>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <div className="flex items-center justify-end space-x-4 pt-6 border-t">
                                <Link href="/backup-jobs">
                                    <Button variant="outline" type="button">
                                        Cancel
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    <Save className="mr-2 h-4 w-4" />
                                    {processing ? 'Creating...' : 'Create Backup Job'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
