<?php

namespace App\Services;

use App\Models\SourceServer;
use App\Models\BackupServer;
use App\Jobs\ConnectionTestJob;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ConnectionStatusService
{
    /**
     * Cache prefix for connection test status.
     */
    protected const CACHE_PREFIX = 'connection_test_';

    /**
     * Cache TTL for test status (in seconds).
     */
    protected const CACHE_TTL = 300; // 5 minutes

    /**
     * Start a connection test for a server.
     */
    public function startConnectionTest(SourceServer|BackupServer $server): string
    {
        $testId = $this->generateTestId($server);

        // Store initial test status
        $this->storeTestStatus($testId, [
            'status' => 'pending',
            'server_type' => $server instanceof SourceServer ? 'source' : 'backup',
            'server_id' => $server->id,
            'server_name' => $server->name,
            'started_at' => Carbon::now()->toISOString(),
            'progress' => 10,
            'message' => 'Connection test queued'
        ]);

        // Dispatch the connection test job with the test ID
        $job = ConnectionTestJob::dispatch($server, $testId)->onQueue('testing');

        Log::info("Connection test started for server: {$server->name} (Test ID: {$testId}) - Job dispatched: " . get_class($job));

        return $testId;
    }

    /**
     * Get the status of a connection test.
     */
    public function getTestStatus(string $testId): ?array
    {
        $status = Cache::get($this->getCacheKey($testId));

        // Check if test is stuck (pending or running for more than 2 minutes)
        if ($status && in_array($status['status'], ['pending', 'running'])) {
            $startTime = Carbon::parse($status['started_at']);
            if ($startTime->diffInMinutes(Carbon::now()) > 2) {
                // Mark as failed due to timeout
                $timeoutMessage = $status['status'] === 'pending'
                    ? 'Connection test timed out while queued'
                    : 'Connection test timed out during execution';

                Log::info("Connection test timed out: {$testId} - Status was: {$status['status']}");

                // Complete the test directly without circular dependency
                $this->completeTestDirect($testId, $status, false, $timeoutMessage);
                $status = Cache::get($this->getCacheKey($testId));
            }
        }

        return $status;
    }

    /**
     * Update the status of a connection test.
     */
    public function updateTestStatus(string $testId, array $updates): void
    {
        $currentStatus = Cache::get($this->getCacheKey($testId)) ?? [];
        $newStatus = array_merge($currentStatus, $updates);
        $newStatus['updated_at'] = Carbon::now()->toISOString();

        $this->storeTestStatus($testId, $newStatus);
    }

    /**
     * Mark a connection test as completed.
     */
    public function completeTest(string $testId, bool $success, ?string $message = null, ?array $diagnostics = null, ?int $duration = null): void
    {
        $currentStatus = Cache::get($this->getCacheKey($testId));
        $this->completeTestDirect($testId, $currentStatus, $success, $message, $diagnostics, $duration);
    }

    /**
     * Mark a connection test as completed (direct method to avoid circular dependencies).
     */
    protected function completeTestDirect(string $testId, ?array $currentStatus, bool $success, ?string $message = null, ?array $diagnostics = null, ?int $duration = null): void
    {
        $startTime = $currentStatus['started_at'] ?? null;

        $updates = [
            'status' => $success ? 'completed' : 'failed',
            'success' => $success,
            'progress' => 100,
            'completed_at' => Carbon::now()->toISOString(),
            'message' => $message ?? ($success ? 'Connection test successful' : 'Connection test failed')
        ];

        // Calculate duration if not provided
        if ($duration !== null) {
            $updates['duration_ms'] = $duration;
        } elseif ($startTime) {
            $startCarbon = Carbon::parse($startTime);
            $updates['duration_ms'] = round($startCarbon->diffInMilliseconds(Carbon::now()));
        }

        if ($diagnostics) {
            $updates['diagnostics'] = $diagnostics;
        }

        $this->updateTestStatus($testId, $updates);

        Log::info("Connection test completed: {$testId} - " . ($success ? 'Success' : 'Failed') .
                 (isset($updates['duration_ms']) ? " ({$updates['duration_ms']}ms)" : ''));
    }

    /**
     * Mark a connection test as failed.
     */
    public function failTest(string $testId, string $error, ?array $diagnostics = null): void
    {
        $this->completeTest($testId, false, $error, $diagnostics);
    }

    /**
     * Get connection test history for a server.
     */
    public function getServerTestHistory(SourceServer|BackupServer $server, int $limit = 10): array
    {
        $serverType = $server instanceof SourceServer ? 'source' : 'backup';
        $pattern = $this->getCacheKey("{$serverType}_{$server->id}_*");
        
        // This is a simplified implementation
        // In a real application, you'd store this in the database
        $history = [];
        
        // Get recent test results from server's connection_options
        $connectionOptions = $server->connection_options ?? [];
        if (isset($connectionOptions['test_history'])) {
            $history = array_slice($connectionOptions['test_history'], -$limit);
        }

        return $history;
    }

    /**
     * Get overall connection health for a server.
     */
    public function getServerHealth(SourceServer|BackupServer $server): array
    {
        $history = $this->getServerTestHistory($server, 20);
        
        if (empty($history)) {
            return [
                'status' => 'unknown',
                'success_rate' => 0,
                'last_test' => null,
                'avg_response_time' => 0,
                'total_tests' => 0
            ];
        }

        $successCount = 0;
        $totalResponseTime = 0;
        $validResponseTimes = 0;

        foreach ($history as $test) {
            if ($test['success'] ?? false) {
                $successCount++;
            }
            
            if (isset($test['duration_ms']) && $test['duration_ms'] > 0) {
                $totalResponseTime += $test['duration_ms'];
                $validResponseTimes++;
            }
        }

        $successRate = (count($history) > 0) ? ($successCount / count($history)) * 100 : 0;
        $avgResponseTime = ($validResponseTimes > 0) ? $totalResponseTime / $validResponseTimes : 0;

        // Determine overall status
        $status = 'healthy';
        if ($successRate < 50) {
            $status = 'critical';
        } elseif ($successRate < 80) {
            $status = 'warning';
        }

        return [
            'status' => $status,
            'success_rate' => round($successRate, 1),
            'last_test' => end($history),
            'avg_response_time' => round($avgResponseTime),
            'total_tests' => count($history)
        ];
    }

    /**
     * Clean up old test status entries.
     */
    public function cleanupOldTests(): int
    {
        // This would typically clean up database records
        // For now, we'll just log the cleanup action
        Log::info("Connection test cleanup performed");
        return 0;
    }

    /**
     * Get active connection tests.
     */
    public function getActiveTests(): array
    {
        // This is a simplified implementation
        // In a real application, you'd query the database or cache
        return [];
    }

    /**
     * Cancel a connection test.
     */
    public function cancelTest(string $testId): bool
    {
        $status = $this->getTestStatus($testId);
        
        if (!$status || in_array($status['status'], ['completed', 'failed', 'cancelled'])) {
            return false;
        }

        $this->updateTestStatus($testId, [
            'status' => 'cancelled',
            'progress' => 100,
            'completed_at' => Carbon::now()->toISOString(),
            'message' => 'Connection test cancelled by user'
        ]);

        Log::info("Connection test cancelled: {$testId}");
        return true;
    }

    /**
     * Generate a unique test ID for a server.
     */
    protected function generateTestId(SourceServer|BackupServer $server): string
    {
        $serverType = $server instanceof SourceServer ? 'source' : 'backup';
        $timestamp = Carbon::now()->format('YmdHis');
        $random = Str::random(6);
        
        return "{$serverType}_{$server->id}_{$timestamp}_{$random}";
    }

    /**
     * Store test status in cache.
     */
    protected function storeTestStatus(string $testId, array $status): void
    {
        Cache::put($this->getCacheKey($testId), $status, self::CACHE_TTL);
    }

    /**
     * Get cache key for a test ID.
     */
    protected function getCacheKey(string $testId): string
    {
        return self::CACHE_PREFIX . $testId;
    }

    /**
     * Get connection status summary for dashboard.
     */
    public function getConnectionSummary(): array
    {
        // Get all servers and their health status
        $sourceServers = SourceServer::where('is_active', true)->get();
        $backupServers = BackupServer::where('is_active', true)->get();

        $summary = [
            'total_servers' => $sourceServers->count() + $backupServers->count(),
            'healthy_servers' => 0,
            'warning_servers' => 0,
            'critical_servers' => 0,
            'unknown_servers' => 0,
            'active_tests' => count($this->getActiveTests())
        ];

        foreach ($sourceServers as $server) {
            $health = $this->getServerHealth($server);
            $summary[$health['status'] . '_servers']++;
        }

        foreach ($backupServers as $server) {
            $health = $this->getServerHealth($server);
            $summary[$health['status'] . '_servers']++;
        }

        return $summary;
    }
}
