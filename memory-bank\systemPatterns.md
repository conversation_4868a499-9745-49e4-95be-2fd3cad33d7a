# System Patterns: DevOps Backup Management System

## System Architecture

### High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (React/TS)    │◄──►│   (Laravel)     │◄──►│   Servers       │
│   Inertia.js    │    │   Queue System  │    │   (SSH/SFTP/FTP)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Components │    │   Service Layer │    │   Connection    │
│   Dashboard     │    │   Backup Logic  │    │   Handlers      │
│   Forms         │    │   Scheduling    │    │   Protocols     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

#### 1. Frontend Layer (React/TypeScript)
- **Inertia.js**: Server-side rendering and SPA-like experience
- **Tailwind CSS**: Utility-first styling framework
- **Radix UI**: Accessible component primitives
- **TypeScript**: Type safety and developer experience

#### 2. Backend Layer (Laravel 12)
- **MVC Architecture**: Model-View-Controller pattern
- **Service Layer**: Business logic encapsulation
- **Queue System**: Asynchronous job processing
- **Eloquent ORM**: Database abstraction and relationships

#### 3. Data Layer
- **SQLite**: Default database (supports MySQL/PostgreSQL)
- **Migrations**: Version-controlled schema changes
- **Seeders**: Test data and initial setup
- **Factories**: Model instance generation for testing

#### 4. External Integration Layer
- **Connection Handlers**: SSH, SFTP, FTP protocols
- **File Transfer**: Secure data transmission
- **Authentication**: Server credential management

## Key Technical Decisions

### 1. Technology Stack
- **Laravel 12**: Modern PHP framework with excellent ecosystem
- **React 19**: Latest React with concurrent features
- **TypeScript**: Type safety for better developer experience
- **Inertia.js**: Bridges Laravel and React seamlessly
- **Tailwind CSS**: Rapid UI development with utility classes

### 2. Queue System
- **Database Driver**: Persistent job storage
- **Multiple Queues**: Prioritized job processing
  - `testing`: Connection tests (highest priority)
  - `backup-execution`: Backup jobs (high priority)
  - `maintenance`: Cleanup and retention (medium priority)
  - `default`: General tasks (low priority)

### 3. Connection Management
- **Factory Pattern**: Dynamic connection creation based on protocol
- **Interface Abstraction**: Common interface for all connection types
- **Retry Logic**: Automatic retry with exponential backoff
- **Connection Pooling**: Efficient resource management

### 4. File Transfer Strategy
- **Streaming**: Memory-efficient large file handling
- **Compression**: Gzip compression for bandwidth optimization
- **Encryption**: AES encryption for data security
- **Progress Tracking**: Real-time transfer progress updates

## Design Patterns

### 1. Service Layer Pattern
```php
// Service classes encapsulate business logic
class BackupService
{
    public function executeBackup(BackupJob $job): void
    {
        // Backup execution logic
    }
}
```

### 2. Factory Pattern
```php
// Connection factory creates appropriate connection type
class ConnectionFactory
{
    public function create(string $protocol): ConnectionInterface
    {
        return match($protocol) {
            'ssh' => new SshConnection(),
            'sftp' => new SftpConnection(),
            'ftp' => new FtpConnection(),
        };
    }
}
```

### 3. Repository Pattern
```php
// Models handle data access and relationships
class BackupJob extends Model
{
    public function sourceServer(): BelongsTo
    {
        return $this->belongsTo(SourceServer::class);
    }
}
```

### 4. Observer Pattern
```php
// Events and listeners for decoupled communication
class BackupProgressUpdated
{
    public function __construct(
        public BackupJob $job,
        public int $progress
    ) {}
}
```

### 5. Strategy Pattern
```php
// Different compression/encryption strategies
interface CompressionStrategy
{
    public function compress(string $data): string;
    public function decompress(string $data): string;
}
```

## Component Relationships

### Database Relationships
```
Users
├── SourceServers (1:N)
├── BackupServers (1:N)
└── BackupJobs (1:N)

SourceServers
└── BackupJobs (1:N)

BackupServers
└── BackupJobs (1:N)

BackupJobs
├── BackupLogs (1:N)
├── BackupProgress (1:1)
└── BackupMetrics (1:1)
```

### Service Dependencies
```
BackupService
├── ConnectionService
├── CompressionService
├── EncryptionService
├── ProgressTrackingService
└── BackupLoggerService

ConnectionService
├── ConnectionFactory
├── ConnectionRetryService
└── ConnectionStatusService

ScheduleService
├── CronExpression (dragonmantank/cron-expression)
└── ScheduledBackupDispatcher
```

### Job Dependencies
```
ExecuteBackupJob
├── BackupService
├── ProgressTrackingService
└── BackupLoggerService

ConnectionTestJob
├── ConnectionService
└── ConnectionStatusService

ScheduledBackupDispatcher
├── ScheduleService
└── ExecuteBackupJob
```

## Security Patterns

### 1. Credential Management
- **Encrypted Storage**: Credentials encrypted at rest
- **Secure Transmission**: HTTPS for all web communication
- **Access Control**: Role-based permissions
- **Audit Logging**: All credential access logged

### 2. Data Protection
- **Encryption in Transit**: SSH/SFTP for secure file transfer
- **Encryption at Rest**: AES encryption for backup files
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Eloquent ORM protection

### 3. Authentication & Authorization
- **Laravel Breeze**: Built-in authentication system
- **Session Management**: Secure session handling
- **CSRF Protection**: Cross-site request forgery prevention
- **Rate Limiting**: API endpoint protection

## Performance Patterns

### 1. Queue Processing
- **Asynchronous Jobs**: Non-blocking backup execution
- **Job Prioritization**: Critical jobs processed first
- **Retry Logic**: Automatic failure recovery
- **Timeout Handling**: Prevent hanging jobs

### 2. Database Optimization
- **Indexing**: Strategic database indexes
- **Eager Loading**: Prevent N+1 query problems
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Optimized Eloquent queries

### 3. Frontend Performance
- **Code Splitting**: Lazy-loaded components
- **Asset Optimization**: Minified and compressed assets
- **Caching**: Browser and server-side caching
- **Progressive Enhancement**: Graceful degradation

## Error Handling Patterns

### 1. Exception Handling
- **Custom Exceptions**: Domain-specific error types
- **Graceful Degradation**: System continues operating on errors
- **Error Logging**: Comprehensive error tracking
- **User Feedback**: Clear error messages to users

### 2. Validation
- **Form Validation**: Client and server-side validation
- **Business Rule Validation**: Domain-specific validation
- **Data Integrity**: Database constraint validation
- **Input Sanitization**: XSS and injection prevention

### 3. Monitoring
- **Health Checks**: System health monitoring
- **Performance Metrics**: Response time and throughput
- **Error Tracking**: Error rate and type monitoring
- **Alert System**: Proactive issue notification
