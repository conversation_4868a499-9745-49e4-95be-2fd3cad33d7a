# Technical Context: DevOps Backup Management System

## Technology Stack

### Backend
- PHP 8.2+
- <PERSON><PERSON> 12
- SQLite (default), MySQL/PostgreSQL (optional)
- <PERSON><PERSON> Queues (database driver)
- Eloquent ORM

### Frontend
- React 19
- TypeScript 5.7+
- Inertia.js 2.0
- Tailwind CSS 4.0
- Radix UI
- Vite 6.0

### Tooling
- Composer (PHP dependencies)
- npm (Node.js dependencies)
- E<PERSON><PERSON>, <PERSON><PERSON><PERSON> (code quality)
- <PERSON><PERSON>, <PERSON><PERSON> (testing, code style)

## Key Dependencies

### PHP (composer.json)
- dragonmantank/cron-expression
- inertiajs/inertia-laravel
- laravel/framework
- league/flysystem-sftp-v3
- phpseclib/phpseclib
- symfony/process

### Node.js (package.json)
- @inertiajs/react
- @radix-ui/react-*
- @tailwindcss/vite
- @vitejs/plugin-react
- tailwindcss
- typescript

## Development Setup
- PHP 8.2+, Node.js 18+, Composer, Git
- `composer install` for PHP deps
- `npm install` for Node deps
- `.env` setup, `php artisan key:generate`
- `php artisan migrate` and `db:seed`
- `npm run build` for frontend assets
- `php artisan serve` to run server

## Constraints
- PHP 8.2+ required
- Minimum 512MB RAM (1GB+ recommended)
- Sufficient disk space for backups
- Network: SSH (22), SFTP (22), FTP (21) open
- SQLite for dev, MySQL/PostgreSQL for prod
- File upload and execution time limits (php.ini)

## Architecture Decisions
- Database queue driver for reliability
- Multiple queues for job prioritization
- Factory pattern for connection management
- Service layer for business logic
- Inertia.js for SPA-like UX
- Tailwind + Radix for UI

## Known Limitations
- No cloud storage (S3, GCS) yet
- No clustering/high availability
- Basic monitoring only
- No REST API (planned)

## Testing & Quality
- Pest for PHP tests
- ESLint/Prettier for JS/TS
- Feature, unit, and integration tests
- Code review and CI/CD (planned) 