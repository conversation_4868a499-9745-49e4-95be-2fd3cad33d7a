import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Edit, Eye, Plus, Search, Trash2, DollarSign, Calendar, TrendingUp, List, CalendarDays } from 'lucide-react';
import MonthCalendar from '@/components/expenses/month-calendar';
import WeekCalendar from '@/components/expenses/week-calendar';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Expenses',
        href: '/expenses',
    },
];

interface Expense {
    id: number;
    name: string;
    expense_date: string;
    value: string;
    description: string | null;
    user_id: number;
    created_at: string;
    updated_at: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface ExpensesData {
    data: Expense[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: PaginationLink[];
}

interface Props {
    expenses: ExpensesData | Expense[]; // Can be paginated data or array for calendar views
    search?: string;
    month: number;
    year: number;
    view: string;
    weekStart?: string;
    monthlyTotals: Record<number, string>;
    currentMonthTotal: string;
    weekTotal?: number;
    flash?: {
        success?: string;
        error?: string;
    };
}

const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
];

export default function ExpensesIndex({
    expenses,
    search = '',
    month,
    year,
    view,
    weekStart,
    monthlyTotals,
    currentMonthTotal,
    weekTotal = 0,
    flash
}: Props) {
    const [searchTerm, setSearchTerm] = useState(search);
    const [selectedMonth, setSelectedMonth] = useState(month.toString());
    const [selectedYear, setSelectedYear] = useState(year.toString());
    const [currentView, setCurrentView] = useState(view);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/expenses', {
            search: searchTerm,
            month: selectedMonth,
            year: selectedYear,
            view: currentView,
            week_start: weekStart
        }, { preserveState: true });
    };

    const handleMonthYearChange = () => {
        router.get('/expenses', {
            search: searchTerm,
            month: selectedMonth,
            year: selectedYear,
            view: currentView,
            week_start: weekStart
        }, { preserveState: true });
    };

    const handleViewChange = (newView: string) => {
        setCurrentView(newView);
        const params: any = {
            search: searchTerm,
            view: newView
        };

        if (newView === 'week') {
            // Set week start to current week
            const today = new Date();
            const startOfWeek = new Date(today);
            startOfWeek.setDate(today.getDate() - today.getDay());
            params.week_start = startOfWeek.toISOString().split('T')[0];
        } else {
            params.month = selectedMonth;
            params.year = selectedYear;
        }

        router.get('/expenses', params, { preserveState: true });
    };

    const handleMonthChange = (newMonth: number, newYear: number) => {
        setSelectedMonth(newMonth.toString());
        setSelectedYear(newYear.toString());
        router.get('/expenses', {
            search: searchTerm,
            month: newMonth,
            year: newYear,
            view: currentView
        }, { preserveState: true });
    };

    const handleWeekChange = (weekStartDate: Date) => {
        router.get('/expenses', {
            search: searchTerm,
            view: 'week',
            week_start: weekStartDate.toISOString().split('T')[0]
        }, { preserveState: true });
    };

    const handleDelete = (expense: Expense) => {
        if (confirm(`Are you sure you want to delete "${expense.name}"?`)) {
            router.delete(`/expenses/${expense.id}`, {
                onSuccess: () => {
                    // Refresh the current view
                    const params: any = {
                        search: searchTerm,
                        view: currentView
                    };

                    if (currentView === 'week' && weekStart) {
                        params.week_start = weekStart;
                    } else {
                        params.month = selectedMonth;
                        params.year = selectedYear;
                    }

                    router.get('/expenses', params, { preserveState: true });
                }
            });
        }
    };

    const formatCurrency = (value: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(parseFloat(value));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    // Generate year options (current year ± 5 years)
    const currentYear = new Date().getFullYear();
    const yearOptions = [];
    for (let i = currentYear - 5; i <= currentYear + 5; i++) {
        yearOptions.push(i);
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Expenses Calendar" />
            
            <div className="px-4 py-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 gap-4">
                    <div>
                        <h1 className="text-2xl font-bold">Expenses Calendar</h1>
                        <p className="text-muted-foreground">Track your expenses and view monthly totals</p>
                    </div>
                    <div className="flex items-center gap-3">
                        <ToggleGroup
                            type="single"
                            value={currentView}
                            onValueChange={(value) => value && handleViewChange(value)}
                            className="border rounded-md"
                        >
                            <ToggleGroupItem value="list" aria-label="List view">
                                <List className="w-4 h-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="month" aria-label="Month view">
                                <Calendar className="w-4 h-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="week" aria-label="Week view">
                                <CalendarDays className="w-4 h-4" />
                            </ToggleGroupItem>
                        </ToggleGroup>
                        <Button asChild>
                            <Link href="/expenses/create">
                                <Plus className="w-4 h-4 mr-2" />
                                Add Expense
                            </Link>
                        </Button>
                    </div>
                </div>

                {flash?.success && (
                    <Alert className="mb-6 border-green-200 bg-green-50 text-green-800">
                        <AlertDescription>{flash.success}</AlertDescription>
                    </Alert>
                )}

                {flash?.error && (
                    <Alert className="mb-6 border-red-200 bg-red-50 text-red-800">
                        <AlertDescription>{flash.error}</AlertDescription>
                    </Alert>
                )}

                {/* Show overview cards only for list view */}
                {currentView === 'list' && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Current Month Total</CardTitle>
                                <DollarSign className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatCurrency(currentMonthTotal)}</div>
                                <p className="text-xs text-muted-foreground">
                                    {monthNames[month - 1]} {year}
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {Array.isArray(expenses) ? expenses.length : expenses.total}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    {monthNames[month - 1]} {year}
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Average per Expense</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {(() => {
                                        const total = Array.isArray(expenses) ? expenses.length : expenses.total;
                                        return total > 0
                                            ? formatCurrency((parseFloat(currentMonthTotal) / total).toString())
                                            : formatCurrency('0');
                                    })()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    This month
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Monthly Totals Grid - only show for list view */}
                {currentView === 'list' && (
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle>Monthly Totals - {year}</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                                {monthNames.map((monthName, index) => {
                                    const monthNumber = index + 1;
                                    const total = monthlyTotals[monthNumber] || '0';
                                    const isCurrentMonth = monthNumber === month;

                                    return (
                                        <div
                                            key={monthNumber}
                                            className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                                                isCurrentMonth
                                                    ? 'border-primary bg-primary/5'
                                                    : 'border-border hover:border-primary/50'
                                            }`}
                                            onClick={() => handleMonthChange(monthNumber, year)}
                                        >
                                            <div className="text-sm font-medium">{monthName}</div>
                                            <div className="text-lg font-bold text-primary">
                                                {formatCurrency(total)}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Calendar Views */}
                {currentView === 'month' && (
                    <MonthCalendar
                        expenses={Array.isArray(expenses) ? expenses : []}
                        currentMonth={month}
                        currentYear={year}
                        onMonthChange={handleMonthChange}
                    />
                )}

                {currentView === 'week' && weekStart && (
                    <WeekCalendar
                        expenses={Array.isArray(expenses) ? expenses : []}
                        currentWeek={new Date(weekStart)}
                        onWeekChange={handleWeekChange}
                    />
                )}

                {/* List View */}
                {currentView === 'list' && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Expenses for {monthNames[month - 1]} {year}</CardTitle>
                            <div className="flex flex-col sm:flex-row gap-4">
                                <form onSubmit={handleSearch} className="flex gap-2 flex-1">
                                    <div className="relative flex-1">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                                        <Input
                                            type="text"
                                            placeholder="Search expenses..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                    <Button type="submit" variant="outline">
                                        Search
                                    </Button>
                                </form>

                                <div className="flex gap-2">
                                    <Select value={selectedMonth} onValueChange={(value) => {
                                        setSelectedMonth(value);
                                        setTimeout(handleMonthYearChange, 0);
                                    }}>
                                        <SelectTrigger className="w-32">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {monthNames.map((monthName, index) => (
                                                <SelectItem key={index + 1} value={(index + 1).toString()}>
                                                    {monthName}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>

                                    <Select value={selectedYear} onValueChange={(value) => {
                                        setSelectedYear(value);
                                        setTimeout(handleMonthYearChange, 0);
                                    }}>
                                        <SelectTrigger className="w-24">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {yearOptions.map((yearOption) => (
                                                <SelectItem key={yearOption} value={yearOption.toString()}>
                                                    {yearOption}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            {(() => {
                                const expenseData = Array.isArray(expenses) ? expenses : expenses.data;
                                return expenseData.length === 0 ? (
                                    <div className="text-center py-8">
                                        <DollarSign className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                                        <p className="text-muted-foreground">No expenses found for this period</p>
                                        <Button asChild className="mt-4">
                                            <Link href="/expenses/create">Add your first expense</Link>
                                        </Button>
                                    </div>
                                ) : (
                                    <div className="space-y-4">
                                        {expenseData.map((expense) => (
                                            <div key={expense.id} className="flex items-center justify-between p-4 border rounded-lg">
                                                <div className="flex items-center space-x-4">
                                                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                                                        <DollarSign className="w-5 h-5 text-primary" />
                                                    </div>
                                                    <div>
                                                        <h3 className="font-medium">{expense.name}</h3>
                                                        <p className="text-sm text-muted-foreground">
                                                            {formatDate(expense.expense_date)} • {formatCurrency(expense.value)}
                                                        </p>
                                                        {expense.description && (
                                                            <p className="text-sm text-muted-foreground mt-1">
                                                                {expense.description}
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={`/expenses/${expense.id}`}>
                                                            <Eye className="w-4 h-4" />
                                                        </Link>
                                                    </Button>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={`/expenses/${expense.id}/edit`}>
                                                            <Edit className="w-4 h-4" />
                                                        </Link>
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handleDelete(expense)}
                                                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                );
                            })()}

                            {/* Pagination */}
                            {!Array.isArray(expenses) && expenses.last_page > 1 && (
                                <div className="flex items-center justify-between mt-6">
                                    <div className="text-sm text-muted-foreground">
                                        Showing {((expenses.current_page - 1) * expenses.per_page) + 1} to{' '}
                                        {Math.min(expenses.current_page * expenses.per_page, expenses.total)} of{' '}
                                        {expenses.total} results
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        {expenses.links.map((link, index) => (
                                            <Button
                                                key={index}
                                                variant={link.active ? "default" : "outline"}
                                                size="sm"
                                                onClick={() => link.url && router.get(link.url)}
                                                disabled={!link.url}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
