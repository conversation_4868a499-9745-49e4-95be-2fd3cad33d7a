#!/bin/bash

# === CONFIGURATION ===
GIT_TOKEN="****************************************"
GIT_USER="mharsikarim"
REPO="WebVue-tn/devops"
BRANCH="main"



# === CHECK COMMIT MESSAGE ===
if [ -z "$1" ]; then
  echo "❌ Commit message is required."
  echo "Usage: ./push.sh \"Your commit message here\""
  exit 1
fi
COMMIT_MSG="$1"

# === ADD, COMMIT, PUSH ===
git add .
git commit -m "$COMMIT_MSG"

git push -u https://mharsikarim:<EMAIL>/WebVue-tn/devops.git main