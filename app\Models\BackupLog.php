<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class BackupLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'backup_job_id',
        'status',
        'started_at',
        'completed_at',
        'duration_seconds',
        'backup_size_bytes',
        'files_count',
        'backup_path',
        'error_message',
        'output_log',
        'metadata',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the backup job for this log.
     */
    public function backupJob(): BelongsTo
    {
        return $this->belongsTo(BackupJob::class);
    }

    /**
     * Get the progress records for this backup log.
     */
    public function progressRecords(): HasMany
    {
        return $this->hasMany(BackupProgress::class);
    }

    /**
     * Get the current progress record.
     */
    public function currentProgress(): Has<PERSON>any
    {
        return $this->hasMany(BackupProgress::class)->where('is_current_stage', true);
    }

    /**
     * Get the metrics for this backup log.
     */
    public function metrics(): HasMany
    {
        return $this->hasMany(BackupMetrics::class);
    }

    /**
     * Check if the backup is running.
     */
    public function isRunning(): bool
    {
        return $this->status === 'running';
    }

    /**
     * Check if the backup completed successfully.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the backup failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the backup was cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get the duration in human readable format.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_seconds) {
            return 'N/A';
        }

        $hours = floor($this->duration_seconds / 3600);
        $minutes = floor(($this->duration_seconds % 3600) / 60);
        $seconds = $this->duration_seconds % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    /**
     * Get the backup size in human readable format.
     */
    public function getFormattedSizeAttribute(): string
    {
        if (!$this->backup_size_bytes) {
            return 'N/A';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = $this->backup_size_bytes;

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the status with color coding.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'running' => 'blue',
            'completed' => 'green',
            'failed' => 'red',
            'cancelled' => 'yellow',
            default => 'gray',
        };
    }

    /**
     * Calculate duration if completed_at is set.
     */
    public function calculateDuration(): void
    {
        if ($this->started_at && $this->completed_at) {
            $this->duration_seconds = $this->completed_at->diffInSeconds($this->started_at);
            $this->save();
        }
    }
}
