import { useState, useEffect } from 'react';
import axios from 'axios';

interface DashboardStats {
    total_source_servers: number;
    active_source_servers: number;
    total_backup_servers: number;
    active_backup_servers: number;
    total_backup_jobs: number;
    active_backup_jobs: number;
    total_backups: number;
    successful_backups: number;
    failed_backups: number;
    running_backups: number;
    recent_jobs?: any[];
}

interface ConnectionSummary {
    total_servers: number;
    healthy_servers: number;
    warning_servers: number;
    critical_servers: number;
    unknown_servers: number;
    active_tests: number;
}

export function useDashboardData() {
    const [stats, setStats] = useState<DashboardStats>({
        total_source_servers: 0,
        active_source_servers: 0,
        total_backup_servers: 0,
        active_backup_servers: 0,
        total_backup_jobs: 0,
        active_backup_jobs: 0,
        total_backups: 0,
        successful_backups: 0,
        failed_backups: 0,
        running_backups: 0,
    });
    
    const [connectionSummary, setConnectionSummary] = useState<ConnectionSummary>({
        total_servers: 0,
        healthy_servers: 0,
        warning_servers: 0,
        critical_servers: 0,
        unknown_servers: 0,
        active_tests: 0,
    });
    
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchDashboardData();
        
        // Refresh data every 30 seconds
        const interval = setInterval(fetchDashboardData, 30000);
        
        return () => clearInterval(interval);
    }, []);

    const fetchDashboardData = async () => {
        try {
            setError(null);
            
            // Fetch connection summary
            const connectionResponse = await axios.get('/api/connection-test/summary');
            if (connectionResponse.data.success) {
                setConnectionSummary(connectionResponse.data.summary);
            }

            // For now, we'll use mock data for other stats since we don't have those API endpoints yet
            // In a real implementation, you would fetch these from actual API endpoints
            setStats({
                total_source_servers: 5,
                active_source_servers: 4,
                total_backup_servers: 3,
                active_backup_servers: 3,
                total_backup_jobs: 8,
                active_backup_jobs: 6,
                total_backups: 156,
                successful_backups: 142,
                failed_backups: 8,
                running_backups: 2,
                recent_jobs: [
                    {
                        id: 1,
                        name: 'Daily Web Server Backup',
                        status: 'completed',
                        started_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                        source_server: { name: 'Web Server 01' }
                    },
                    {
                        id: 2,
                        name: 'Database Backup',
                        status: 'running',
                        started_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                        source_server: { name: 'DB Server 01' }
                    },
                    {
                        id: 3,
                        name: 'File Server Backup',
                        status: 'failed',
                        started_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
                        source_server: { name: 'File Server 01' }
                    }
                ]
            });
            
        } catch (err: any) {
            console.error('Failed to fetch dashboard data:', err);
            setError(err.response?.data?.message || 'Failed to fetch dashboard data');
        } finally {
            setIsLoading(false);
        }
    };

    const refresh = () => {
        setIsLoading(true);
        fetchDashboardData();
    };

    return {
        stats,
        connectionSummary,
        isLoading,
        error,
        refresh
    };
}
