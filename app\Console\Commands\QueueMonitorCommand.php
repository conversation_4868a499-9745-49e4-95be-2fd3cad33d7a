<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class QueueMonitorCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:monitor-devops {--refresh=5 : Refresh interval in seconds}';

    /**
     * The console command description.
     */
    protected $description = 'Monitor DevOps backup system queue status';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $refreshInterval = (int) $this->option('refresh');
        
        $this->info('DevOps Backup System - Queue Monitor');
        $this->info('Press Ctrl+C to exit');
        $this->newLine();

        while (true) {
            $this->clearScreen();
            $this->displayQueueStatus();
            
            if ($refreshInterval > 0) {
                sleep($refreshInterval);
            } else {
                break;
            }
        }

        return 0;
    }

    /**
     * Display current queue status.
     */
    private function displayQueueStatus(): void
    {
        $this->info('=== DevOps Queue Monitor ===');
        $this->info('Time: ' . now()->format('Y-m-d H:i:s'));
        $this->newLine();

        // Get queue counts by queue name
        $queueCounts = DB::table('jobs')
            ->select('queue', DB::raw('count(*) as count'))
            ->groupBy('queue')
            ->get();

        if ($queueCounts->isEmpty()) {
            $this->info('✅ No jobs in queue');
        } else {
            $this->info('📊 Jobs in Queue:');
            foreach ($queueCounts as $queue) {
                $this->line("   {$queue->queue}: {$queue->count} jobs");
            }
        }

        $this->newLine();

        // Get failed jobs count
        $failedCount = DB::table('failed_jobs')->count();
        if ($failedCount > 0) {
            $this->warn("⚠️  Failed jobs: {$failedCount}");
        } else {
            $this->info('✅ No failed jobs');
        }

        $this->newLine();

        // Show recent jobs
        $recentJobs = DB::table('jobs')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get(['queue', 'payload', 'created_at']);

        if ($recentJobs->isNotEmpty()) {
            $this->info('🕒 Recent Jobs:');
            foreach ($recentJobs as $job) {
                $payload = json_decode($job->payload, true);
                $jobClass = $payload['displayName'] ?? 'Unknown';
                $createdAt = \Carbon\Carbon::parse($job->created_at)->diffForHumans();
                $this->line("   [{$job->queue}] {$jobClass} - {$createdAt}");
            }
        }

        $this->newLine();
        $this->info('Refreshing every ' . $this->option('refresh') . ' seconds...');
    }

    /**
     * Clear the screen.
     */
    private function clearScreen(): void
    {
        if (PHP_OS_FAMILY === 'Windows') {
            system('cls');
        } else {
            system('clear');
        }
    }
}
