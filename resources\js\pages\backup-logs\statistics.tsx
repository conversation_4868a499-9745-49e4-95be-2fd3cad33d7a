import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { 
    ArrowLeft, 
    BarChart3, 
    CheckCircle, 
    XCircle, 
    Clock, 
    Archive, 
    TrendingUp,
    Activity,
    Server,
    Calendar
} from 'lucide-react';

interface BackupStats {
    total_backups: number;
    successful_backups: number;
    failed_backups: number;
    running_backups: number;
    total_size: number;
    average_duration: number;
}

interface RecentActivity {
    id: number;
    status: 'running' | 'completed' | 'failed' | 'cancelled';
    started_at: string;
    duration_seconds?: number;
    backup_size_bytes?: number;
    backup_job: {
        id: number;
        name: string;
        source_server: {
            name: string;
            ip_address: string;
        };
    };
}

interface Props {
    stats: BackupStats;
    recentActivity: RecentActivity[];
}

export default function BackupLogStatistics({ stats, recentActivity }: Props) {
    const formatBytes = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const formatDuration = (seconds: number) => {
        if (seconds === 0) return '0s';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    };

    const getSuccessRate = () => {
        if (stats.total_backups === 0) return 0;
        return Math.round((stats.successful_backups / stats.total_backups) * 100);
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return CheckCircle;
            case 'failed':
                return XCircle;
            case 'running':
                return Clock;
            default:
                return Clock;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'failed':
                return 'bg-red-100 text-red-800';
            case 'running':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <AppLayout>
            <Head title="Backup Statistics" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('backup-logs.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Backup Logs
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold">Backup Statistics</h1>
                            <p className="text-muted-foreground">Overview of backup system performance</p>
                        </div>
                    </div>
                </div>

                {/* Key Metrics */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <BarChart3 className="h-5 w-5" />
                                <span>Total Backups</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-3xl font-bold">{stats.total_backups.toLocaleString()}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <CheckCircle className="h-5 w-5 text-green-600" />
                                <span>Success Rate</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-3xl font-bold text-green-600">{getSuccessRate()}%</p>
                            <p className="text-sm text-muted-foreground">
                                {stats.successful_backups} of {stats.total_backups} successful
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <Archive className="h-5 w-5" />
                                <span>Total Size</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-3xl font-bold">{formatBytes(stats.total_size)}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <Clock className="h-5 w-5" />
                                <span>Avg Duration</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-3xl font-bold">{formatDuration(stats.average_duration)}</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Status Breakdown */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <CheckCircle className="h-5 w-5 text-green-600" />
                                <span>Successful</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold text-green-600">{stats.successful_backups}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <XCircle className="h-5 w-5 text-red-600" />
                                <span>Failed</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold text-red-600">{stats.failed_backups}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <Clock className="h-5 w-5 text-blue-600" />
                                <span>Running</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold text-blue-600">{stats.running_backups}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <TrendingUp className="h-5 w-5" />
                                <span>Efficiency</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-1">
                                <p className="text-lg font-bold">
                                    {stats.total_backups > 0 ? formatBytes(stats.total_size / stats.total_backups) : '0 Bytes'}
                                </p>
                                <p className="text-xs text-muted-foreground">Avg backup size</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activity */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Activity className="h-5 w-5" />
                            <span>Recent Activity</span>
                        </CardTitle>
                        <CardDescription>Last 10 backup executions (past 30 days)</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {recentActivity.length > 0 ? (
                            <div className="space-y-4">
                                {recentActivity.map((activity) => {
                                    const StatusIcon = getStatusIcon(activity.status);
                                    return (
                                        <div key={activity.id} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex items-center space-x-4">
                                                <StatusIcon className="h-5 w-5" />
                                                <div>
                                                    <div className="flex items-center space-x-2">
                                                        <Link 
                                                            href={route('backup-jobs.show', activity.backup_job.id)}
                                                            className="font-medium hover:underline"
                                                        >
                                                            {activity.backup_job.name}
                                                        </Link>
                                                        <Badge className={getStatusColor(activity.status)}>
                                                            {activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}
                                                        </Badge>
                                                    </div>
                                                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                                                        <div className="flex items-center space-x-1">
                                                            <Server className="h-3 w-3" />
                                                            <span>{activity.backup_job.source_server.name}</span>
                                                        </div>
                                                        <div className="flex items-center space-x-1">
                                                            <Calendar className="h-3 w-3" />
                                                            <span>{new Date(activity.started_at).toLocaleString()}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="text-right text-sm text-muted-foreground">
                                                <div>Size: {formatBytes(activity.backup_size_bytes || 0)}</div>
                                                <div>Duration: {formatDuration(activity.duration_seconds || 0)}</div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No recent activity</h3>
                                <p className="text-muted-foreground">
                                    No backup executions found in the past 30 days.
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>Common backup management tasks</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex space-x-4">
                            <Link href={route('backup-logs.index')}>
                                <Button variant="outline">
                                    <BarChart3 className="mr-2 h-4 w-4" />
                                    View All Logs
                                </Button>
                            </Link>
                            <Link href={route('backup-jobs.index')}>
                                <Button variant="outline">
                                    <Calendar className="mr-2 h-4 w-4" />
                                    Manage Jobs
                                </Button>
                            </Link>
                            <Link href={route('backup-jobs.create')}>
                                <Button>
                                    <TrendingUp className="mr-2 h-4 w-4" />
                                    Create New Job
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
