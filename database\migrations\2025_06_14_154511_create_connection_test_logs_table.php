<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('connection_test_logs', function (Blueprint $table) {
            $table->id();
            $table->string('test_id')->unique()->index();
            $table->enum('server_type', ['source_server', 'backup_server']);
            $table->unsignedBigInteger('server_id');
            $table->string('server_name');
            $table->enum('test_type', ['manual', 'health_check', 'scheduled'])->default('manual');
            $table->enum('status', ['pending', 'running', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->boolean('success')->nullable();
            $table->timestamp('test_started_at');
            $table->timestamp('test_completed_at')->nullable();
            $table->integer('duration_ms')->nullable();
            $table->integer('retry_count')->default(0);
            $table->text('error_message')->nullable();
            $table->json('diagnostics')->nullable();
            $table->json('retry_history')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['server_type', 'server_id']);
            $table->index(['test_started_at']);
            $table->index(['status']);
            $table->index(['success']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('connection_test_logs');
    }
};
