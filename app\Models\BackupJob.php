<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class BackupJob extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'source_server_id',
        'backup_server_id',
        'source_path',
        'destination_path',
        'schedule',
        'status',
        'last_run',
        'next_run',
        'retention_policy_days',
        'compression_enabled',
        'encryption_enabled',
        'description',
        'backup_options',
    ];

    protected $casts = [
        'last_run' => 'datetime',
        'next_run' => 'datetime',
        'compression_enabled' => 'boolean',
        'encryption_enabled' => 'boolean',
        'backup_options' => 'array',
    ];

    /**
     * Get the source server for this backup job.
     */
    public function sourceServer(): BelongsTo
    {
        return $this->belongsTo(SourceServer::class);
    }

    /**
     * Get the backup server for this backup job.
     */
    public function backupServer(): BelongsTo
    {
        return $this->belongsTo(BackupServer::class);
    }

    /**
     * Get the backup logs for this job.
     */
    public function backupLogs(): HasMany
    {
        return $this->hasMany(BackupLog::class);
    }

    /**
     * Get the latest backup log.
     */
    public function latestLog(): HasMany
    {
        return $this->hasMany(BackupLog::class)->latest();
    }

    /**
     * Check if the job is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the job is paused.
     */
    public function isPaused(): bool
    {
        return $this->status === 'paused';
    }

    /**
     * Check if the job is disabled.
     */
    public function isDisabled(): bool
    {
        return $this->status === 'disabled';
    }

    /**
     * Get the next run time based on cron schedule.
     */
    public function calculateNextRun(): ?Carbon
    {
        if (!$this->isActive() || !$this->schedule) {
            return null;
        }

        try {
            $cron = new \Cron\CronExpression($this->schedule);
            return Carbon::instance($cron->getNextRunDate());
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get the job status with color coding.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'active' => 'green',
            'paused' => 'yellow',
            'disabled' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get the full destination path.
     */
    public function getFullDestinationPath(): string
    {
        return $this->backupServer->getFullBackupPath($this->destination_path);
    }
}
