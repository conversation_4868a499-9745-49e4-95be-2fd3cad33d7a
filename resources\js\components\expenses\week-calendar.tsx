import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, DollarSign, Plus } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface Expense {
    id: number;
    name: string;
    expense_date: string;
    value: string;
    description: string | null;
}

interface WeekCalendarProps {
    expenses: Expense[];
    currentWeek: Date; // Start of the week
    onWeekChange: (weekStart: Date) => void;
}

const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

export default function WeekCalendar({ 
    expenses, 
    currentWeek, 
    onWeekChange 
}: WeekCalendarProps) {
    const formatCurrency = (value: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(parseFloat(value));
    };

    // Group expenses by date (extract just the date part)
    const expensesByDate = expenses.reduce((acc, expense) => {
        // Extract date part directly from the string to avoid timezone conversion
        let date = expense.expense_date;
        if (date.includes('T')) {
            date = date.split('T')[0]; // Handle datetime format
        } else if (date.includes(' ')) {
            date = date.split(' ')[0]; // Handle datetime with space
        }
        // Ensure we have YYYY-MM-DD format
        if (date.length === 10 && date.includes('-')) {
            if (!acc[date]) {
                acc[date] = [];
            }
            acc[date].push(expense);
        }
        return acc;
    }, {} as Record<string, Expense[]>);

    // Get the 7 days of the current week
    const getWeekDays = () => {
        const days = [];
        const startDate = new Date(currentWeek);
        
        for (let i = 0; i < 7; i++) {
            const day = new Date(startDate);
            day.setDate(startDate.getDate() + i);
            days.push(day);
        }
        
        return days;
    };

    const handlePrevWeek = () => {
        const prevWeek = new Date(currentWeek);
        prevWeek.setDate(currentWeek.getDate() - 7);
        onWeekChange(prevWeek);
    };

    const handleNextWeek = () => {
        const nextWeek = new Date(currentWeek);
        nextWeek.setDate(currentWeek.getDate() + 7);
        onWeekChange(nextWeek);
    };

    const isToday = (date: Date) => {
        const today = new Date();
        return date.toDateString() === today.toDateString();
    };

    const getDayExpenses = (date: Date) => {
        const dateString = date.toISOString().split('T')[0];
        return expensesByDate[dateString] || [];
    };

    const getDayTotal = (date: Date) => {
        const dayExpenses = getDayExpenses(date);
        return dayExpenses.reduce((total, expense) => total + parseFloat(expense.value), 0);
    };

    const getWeekTotal = () => {
        const weekDays = getWeekDays();
        return weekDays.reduce((total, day) => total + getDayTotal(day), 0);
    };

    const formatWeekRange = () => {
        const weekDays = getWeekDays();
        const startDay = weekDays[0];
        const endDay = weekDays[6];
        
        const startMonth = startDay.toLocaleDateString('en-US', { month: 'short' });
        const endMonth = endDay.toLocaleDateString('en-US', { month: 'short' });
        const year = startDay.getFullYear();
        
        if (startMonth === endMonth) {
            return `${startMonth} ${startDay.getDate()}-${endDay.getDate()}, ${year}`;
        } else {
            return `${startMonth} ${startDay.getDate()} - ${endMonth} ${endDay.getDate()}, ${year}`;
        }
    };

    const weekDays = getWeekDays();
    const weekTotal = getWeekTotal();

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="flex items-center">
                            <DollarSign className="w-5 h-5 mr-2" />
                            Week of {formatWeekRange()}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                            Total: {formatCurrency(weekTotal.toString())}
                        </p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" onClick={handlePrevWeek}>
                            <ChevronLeft className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={handleNextWeek}>
                            <ChevronRight className="w-4 h-4" />
                        </Button>
                        <Button size="sm" asChild>
                            <Link href="/expenses/create">
                                <Plus className="w-4 h-4 mr-1" />
                                Add
                            </Link>
                        </Button>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                {/* Week Grid */}
                <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
                    {weekDays.map((date, index) => {
                        const dayExpenses = getDayExpenses(date);
                        const dayTotal = getDayTotal(date);
                        const isTodayDate = isToday(date);
                        
                        return (
                            <div
                                key={index}
                                className={`min-h-[200px] p-3 border rounded-lg ${
                                    isTodayDate 
                                        ? 'border-primary bg-primary/5' 
                                        : 'border-border bg-background'
                                } hover:bg-muted/50 transition-colors`}
                            >
                                {/* Day header */}
                                <div className="flex items-center justify-between mb-3">
                                    <div>
                                        <div className={`text-sm font-medium ${
                                            isTodayDate ? 'text-primary' : 'text-foreground'
                                        }`}>
                                            {dayNames[index]}
                                        </div>
                                        <div className={`text-lg font-bold ${
                                            isTodayDate ? 'text-primary' : 'text-foreground'
                                        }`}>
                                            {date.getDate()}
                                        </div>
                                    </div>
                                    {dayTotal > 0 && (
                                        <Badge variant={isTodayDate ? "default" : "secondary"} className="text-xs">
                                            {formatCurrency(dayTotal.toString())}
                                        </Badge>
                                    )}
                                </div>
                                
                                {/* Expenses for this day */}
                                <div className="space-y-2 max-h-40 overflow-y-auto">
                                    {dayExpenses.map((expense) => (
                                        <Link
                                            key={expense.id}
                                            href={`/expenses/${expense.id}`}
                                            className="block"
                                        >
                                            <div className="p-2 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-all duration-200 hover:scale-[1.02] border border-transparent hover:border-primary/20 hover:shadow-sm">
                                                <div className="font-medium text-sm truncate">
                                                    {expense.name}
                                                </div>
                                                <div className="text-xs opacity-75 mt-1 font-mono">
                                                    {formatCurrency(expense.value)}
                                                </div>
                                                {expense.description && (
                                                    <div className="text-xs opacity-60 mt-1 truncate">
                                                        {expense.description}
                                                    </div>
                                                )}
                                            </div>
                                        </Link>
                                    ))}

                                    {dayExpenses.length === 0 && (
                                        <div className="text-center py-4">
                                            <div className="text-muted-foreground text-sm mb-2">
                                                No expenses
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-xs hover:bg-primary/10 hover:text-primary"
                                                asChild
                                            >
                                                <Link href={`/expenses/create?date=${date.toISOString().split('T')[0]}`}>
                                                    <Plus className="w-3 h-3 mr-1" />
                                                    Add expense
                                                </Link>
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                </div>
                
                {/* Week summary */}
                <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-medium">Week Summary</h3>
                            <p className="text-sm text-muted-foreground">
                                {expenses.length} expenses • Average: {
                                    expenses.length > 0 
                                        ? formatCurrency((weekTotal / expenses.length).toString())
                                        : formatCurrency('0')
                                }
                            </p>
                        </div>
                        <div className="text-right">
                            <div className="text-2xl font-bold text-primary">
                                {formatCurrency(weekTotal.toString())}
                            </div>
                            <div className="text-sm text-muted-foreground">
                                Total spent
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
