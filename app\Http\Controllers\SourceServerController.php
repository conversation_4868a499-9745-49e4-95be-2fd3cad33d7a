<?php

namespace App\Http\Controllers;

use App\Models\SourceServer;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class SourceServerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $query = SourceServer::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $query->where('is_active', $request->get('status') === 'active');
        }

        $sourceServers = $query->with('backupJobs')->orderBy('name')->paginate(10);

        return Inertia::render('source-servers/index', [
            'sourceServers' => $sourceServers,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        return Inertia::render('source-servers/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:source_servers',
            'ip_address' => 'required|string|max:255|unique:source_servers',
            'port' => 'required|integer|min:1|max:65535',
            'username' => 'required|string|max:255',
            'authentication_method' => 'required|in:password,private_key',
            'password' => 'required_if:authentication_method,password|nullable|string',
            'private_key' => 'required_if:authentication_method,private_key|nullable|string',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        // Validate that ip_address is either a valid IP or a valid domain name
        $isValidIP = filter_var($validated['ip_address'], FILTER_VALIDATE_IP);
        $isValidDomain = filter_var($validated['ip_address'], FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) &&
                        strpos($validated['ip_address'], '.') !== false; // Must contain at least one dot

        if (!$isValidIP && !$isValidDomain) {
            return redirect()->back()
                ->withErrors(['ip_address' => 'The server address must be a valid IP address or domain name (e.g., ************* or server.example.com).'])
                ->withInput();
        }

        // Set default port if not provided
        if (!$validated['port']) {
            $validated['port'] = 22;
        }

        $sourceServer = SourceServer::create($validated);

        return redirect('/source-servers')
            ->with('success', 'Source server created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(SourceServer $sourceServer): Response
    {
        $sourceServer->load('backupJobs.backupServer');

        return Inertia::render('source-servers/show', [
            'sourceServer' => $sourceServer,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SourceServer $sourceServer): Response
    {
        return Inertia::render('source-servers/edit', [
            'sourceServer' => $sourceServer,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SourceServer $sourceServer): RedirectResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('source_servers')->ignore($sourceServer)],
            'ip_address' => ['required', 'string', 'max:255', Rule::unique('source_servers')->ignore($sourceServer)],
            'port' => 'required|integer|min:1|max:65535',
            'username' => 'required|string|max:255',
            'authentication_method' => 'required|in:password,private_key',
            'password' => 'nullable|string',
            'private_key' => 'nullable|string',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        // Validate that ip_address is either a valid IP or a valid domain name
        $isValidIP = filter_var($validated['ip_address'], FILTER_VALIDATE_IP);
        $isValidDomain = filter_var($validated['ip_address'], FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) &&
                        strpos($validated['ip_address'], '.') !== false; // Must contain at least one dot

        if (!$isValidIP && !$isValidDomain) {
            return redirect()->back()
                ->withErrors(['ip_address' => 'The server address must be a valid IP address or domain name (e.g., ************* or server.example.com).'])
                ->withInput();
        }

        // Only update password/private_key if provided
        if (empty($validated['password'])) {
            unset($validated['password']);
        }
        if (empty($validated['private_key'])) {
            unset($validated['private_key']);
        }

        $sourceServer->update($validated);

        return redirect()->route('source-servers.index')
            ->with('success', 'Source server updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SourceServer $sourceServer): RedirectResponse
    {
        \Log::info('Delete request received for source server: ' . $sourceServer->id);

        // Check if server has active backup jobs
        if ($sourceServer->backupJobs()->where('status', 'active')->exists()) {
            \Log::info('Cannot delete - has active backup jobs');
            return redirect()->route('source-servers.index')
                ->with('error', 'Cannot delete source server with active backup jobs.');
        }

        try {
            $sourceServer->delete();
            \Log::info('Source server deleted successfully: ' . $sourceServer->id);

            return redirect()->route('source-servers.index')
                ->with('success', 'Source server deleted successfully.');
        } catch (\Exception $e) {
            \Log::error('Failed to delete source server: ' . $e->getMessage());
            return redirect()->route('source-servers.index')
                ->with('error', 'Failed to delete source server: ' . $e->getMessage());
        }
    }

    /**
     * Test connection to the source server.
     */
    public function testConnection(SourceServer $sourceServer): RedirectResponse
    {
        try {
            $statusService = app(\App\Services\ConnectionStatusService::class);
            $testId = $statusService->startConnectionTest($sourceServer);

            return redirect()->back()
                ->with('success', 'Connection test started successfully')
                ->with('test_id', $testId)
                ->with('status_url', route('connection-test.status', ['testId' => $testId]));

        } catch (\Exception $e) {
            \Log::error("Failed to start connection test for source server {$sourceServer->name}: " . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Failed to start connection test: ' . $e->getMessage());
        }
    }
}
