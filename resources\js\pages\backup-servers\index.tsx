import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ConnectionTestButton } from '@/components/ConnectionTestButton';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Search, Plus, MoreHorizontal, Eye, Edit, Trash2, Wifi, WifiOff, HardDrive } from 'lucide-react';
import axios from 'axios';

interface BackupServer {
    id: number;
    name: string;
    protocol: 'ftp' | 'sftp';
    ip_address: string;
    port: number;
    username: string;
    base_directory: string;
    description?: string;
    is_active: boolean;
    last_connection_test?: string;
    created_at: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface BackupServersData {
    data: BackupServer[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: PaginationLink[];
}

interface Props {
    backupServers: BackupServersData;
}

export default function BackupServersIndex({ backupServers }: Props) {
    const { data, setData, get, processing } = useForm({
        search: '',
        protocol: '',
        status: '',
    });

    const handleSearch = () => {
        get('/backup-servers', {
            preserveState: true,
            replace: true,
        });
    };

    const handleDelete = (id: number, serverName: string, server: any) => {
        // Count backup jobs for this server
        const backupJobsCount = server.backup_jobs?.length || 0;
        const activeJobsCount = server.backup_jobs?.filter((job: any) => job.status === 'active').length || 0;

        let message = `Are you sure you want to delete "${serverName}"?\n\n`;

        if (backupJobsCount === 0) {
            message += `✅ This backup server has no backup jobs and can be safely deleted.`;
        } else {
            message += `⚠️  WARNING: This backup server has ${backupJobsCount} backup job(s):\n`;
            message += `   • ${activeJobsCount} active job(s)\n`;
            message += `   • ${backupJobsCount - activeJobsCount} inactive job(s)\n\n`;

            if (activeJobsCount > 0) {
                message += `❌ Cannot delete backup server with active backup jobs.\n\n`;
                message += `REQUIRED ACTIONS:\n`;
                message += `1. Go to Backup Jobs and disable/delete the active jobs first\n`;
                message += `2. Then return here to delete this backup server\n\n`;
                message += `Active backup jobs using this server:\n`;
                server.backup_jobs?.filter((job: any) => job.status === 'active').forEach((job: any, index: number) => {
                    message += `   ${index + 1}. ${job.name}\n`;
                });
                message += `\nClick OK to see the error message, or Cancel to keep the server.`;
            } else {
                message += `✅ All backup jobs are inactive, deletion should work.`;
            }
        }

        const choice = confirm(message);

        if (choice) {
            console.log('Attempting to delete backup server:', id);
            router.delete(`/backup-servers/${id}`, {
                onStart: () => console.log('Delete request started'),
                onSuccess: (page) => console.log('Delete successful:', page),
                onError: (errors) => {
                    console.error('Delete failed:', errors);
                    alert(
                        `❌ DELETE FAILED\n\n` +
                        `Backup server "${serverName}" cannot be deleted because it has ${activeJobsCount} active backup jobs.\n\n` +
                        `🔧 TO DELETE THIS SERVER:\n` +
                        `1. Go to "Backup Jobs" in the sidebar\n` +
                        `2. Find and disable/delete these active jobs:\n` +
                        server.backup_jobs?.filter((job: any) => job.status === 'active').map((job: any, index: number) =>
                            `   ${index + 1}. ${job.name}`
                        ).join('\n') +
                        `\n\n3. Return here and try deleting the server again\n\n` +
                        `💡 TIP: You can filter backup jobs by this server to find them quickly.`
                    );
                },
                onFinish: () => console.log('Delete request finished')
            });
        }
    };

    const handleTestConnection = async (id: number, serverName: string) => {
        try {
            const response = await axios.post(`/api/connection-test/backup-servers/${id}`);
            if (response.data.success) {
                // Show success notification
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';
                notification.innerHTML = `
                    <div class="flex items-start space-x-2">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium">Connection Test Started</h4>
                            <p class="text-sm mt-1">Testing connection to ${serverName}. View progress in server details.</p>
                            <p class="text-xs mt-1 text-green-600">Test ID: ${response.data.test_id}</p>
                        </div>
                    </div>
                `;
                document.body.appendChild(notification);
                setTimeout(() => notification.remove(), 5000);
            } else {
                throw new Error(response.data.message);
            }
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Failed to start connection test';
            // Show error notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';
            notification.innerHTML = `
                <div class="flex items-start space-x-2">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium">Connection Test Failed</h4>
                        <p class="text-sm mt-1">${errorMessage}</p>
                    </div>
                </div>
            `;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 5000);
        }
    };

    return (
        <AppLayout>
            <Head title="Backup Servers" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Backup Servers</h1>
                        <p className="text-muted-foreground">
                            Manage FTP/SFTP servers where backups are stored
                        </p>
                    </div>
                    <Link href="/backup-servers/create">
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Backup Server
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                        <CardDescription>Search and filter backup servers</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search by name, IP, or username..."
                                    value={data.search}
                                    onChange={(e) => setData('search', e.target.value)}
                                    className="max-w-sm"
                                />
                            </div>
                            <Select value={data.protocol} onValueChange={(value) => setData('protocol', value)}>
                                <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="All Protocols" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Protocols</SelectItem>
                                    <SelectItem value="ftp">FTP</SelectItem>
                                    <SelectItem value="sftp">SFTP</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="All Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="inactive">Inactive</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button onClick={handleSearch} disabled={processing}>
                                <Search className="mr-2 h-4 w-4" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Backup Servers Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {backupServers.data.map((server) => (
                        <Card key={server.id} className="relative">
                            <CardHeader className="pb-3">
                                <div className="flex items-start justify-between">
                                    <div className="flex items-center space-x-2">
                                        <HardDrive className="h-5 w-5 text-muted-foreground" />
                                        <CardTitle className="text-lg">{server.name}</CardTitle>
                                    </div>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="sm">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem asChild>
                                                <Link href={`/backup-servers/${server.id}`}>
                                                    <Eye className="mr-2 h-4 w-4" />
                                                    View
                                                </Link>
                                            </DropdownMenuItem>
                                            <DropdownMenuItem asChild>
                                                <Link href={`/backup-servers/${server.id}/edit`}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit
                                                </Link>
                                            </DropdownMenuItem>

                                            <DropdownMenuItem
                                                onClick={() => handleDelete(server.id, server.name, server)}
                                                className="text-destructive"
                                            >
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                                <CardDescription>{server.description || 'No description'}</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">Status</span>
                                    <Badge variant={server.is_active ? 'default' : 'secondary'}>
                                        {server.is_active ? (
                                            <>
                                                <Wifi className="mr-1 h-3 w-3" />
                                                Active
                                            </>
                                        ) : (
                                            <>
                                                <WifiOff className="mr-1 h-3 w-3" />
                                                Inactive
                                            </>
                                        )}
                                    </Badge>
                                </div>
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">Connection Test</span>
                                    </div>
                                    <ConnectionTestButton
                                        serverType="backup"
                                        serverId={server.id}
                                        serverName={server.name}
                                        variant="outline"
                                        size="sm"
                                        showProgress={true}
                                        className="w-full"
                                        onTestComplete={(success, result) => {
                                            // Optionally refresh the page or show notification
                                            console.log(`Connection test for ${server.name}:`, success ? 'Success' : 'Failed', result);
                                        }}
                                    />
                                </div>
                                <div className="space-y-1">
                                    <div className="text-sm">
                                        <span className="text-muted-foreground">Protocol:</span>
                                        <Badge variant="outline" className="ml-2 text-xs">
                                            {server.protocol.toUpperCase()}
                                        </Badge>
                                    </div>
                                    <div className="text-sm">
                                        <span className="text-muted-foreground">Connection:</span>
                                        <span className="ml-2 font-mono text-xs">
                                            {server.protocol}://{server.ip_address}:{server.port}
                                        </span>
                                    </div>
                                    <div className="text-sm">
                                        <span className="text-muted-foreground">Directory:</span>
                                        <span className="ml-2 font-mono text-xs">{server.base_directory}</span>
                                    </div>
                                    {server.last_connection_test && (
                                        <div className="text-sm">
                                            <span className="text-muted-foreground">Last Test:</span>
                                            <span className="ml-2">{new Date(server.last_connection_test).toLocaleDateString()}</span>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Empty State */}
                {backupServers.data.length === 0 && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <HardDrive className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No backup servers found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Get started by adding your first backup destination server.
                            </p>
                            <Link href="/backup-servers/create">
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Backup Server
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}

                {/* Pagination */}
                {backupServers.data.length > 0 && (
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                            Showing {((backupServers.current_page - 1) * backupServers.per_page) + 1} to{' '}
                            {Math.min(backupServers.current_page * backupServers.per_page, backupServers.total)} of {backupServers.total} results
                        </div>
                        <div className="flex space-x-2">
                            {backupServers.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
