#!/bin/bash

# DevOps Backup System - Queue Worker Startup Script
# This script starts the Laravel queue worker with proper configuration

echo "🚀 Starting DevOps Backup System Queue Worker..."

# Check if we're in the correct directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: artisan file not found. Please run this script from the Laravel project root."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

# Clear any existing failed jobs (optional)
echo "🧹 Clearing failed jobs..."
php artisan queue:clear --force

# Show current queue status
echo "📊 Current queue status:"
php artisan queue:monitor

# Start the queue worker with proper configuration
echo "⚡ Starting queue worker..."
echo "   - Queues: testing, backup-execution, maintenance, default"
echo "   - Max tries: 3"
echo "   - Timeout: 300 seconds (5 minutes)"
echo "   - Memory limit: 512MB"
echo ""
echo "Press Ctrl+C to stop the queue worker"
echo "----------------------------------------"

# Start the queue worker
php artisan queue:work \
    --queue=testing,backup-execution,maintenance,default \
    --tries=3 \
    --timeout=300 \
    --memory=512 \
    --sleep=3 \
    --max-jobs=1000 \
    --max-time=3600
