<?php

namespace App\Services\Connections;

use App\Models\SourceServer;
use App\Models\BackupServer;
use phpseclib3\Net\SSH2;
use phpseclib3\Crypt\PublicKeyLoader;
use Illuminate\Support\Facades\Log;
use Exception;

class SshConnection implements ConnectionInterface
{
    protected ?SSH2 $ssh = null;
    protected string $lastError = '';
    protected int $timeout = 30;
    protected SourceServer|BackupServer|null $server = null;

    /**
     * Connect to the specified server via SSH.
     */
    public function connect(SourceServer|BackupServer $server): bool
    {
        try {
            $this->server = $server;
            $this->lastError = '';

            // Create SSH connection
            $this->ssh = new SSH2($server->ip_address, $server->port ?: 22);
            $this->ssh->setTimeout($this->timeout);

            // Authenticate based on method
            if ($server instanceof SourceServer) {
                $authenticated = $this->authenticateSourceServer($server);
            } else {
                $authenticated = $this->authenticateBackupServer($server);
            }

            if (!$authenticated) {
                $this->lastError = "SSH authentication failed for server {$server->name}";
                Log::warning($this->lastError);
                return false;
            }

            Log::info("Successfully connected to server via SSH: {$server->name}");
            return true;

        } catch (Exception $e) {
            $this->lastError = "SSH connection failed: " . $e->getMessage();
            Log::error("Failed to connect to server {$server->name} via SSH: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Authenticate to a source server.
     */
    protected function authenticateSourceServer(SourceServer $server): bool
    {
        if ($server->authentication_method === 'password') {
            return $this->ssh->login($server->username, $server->password);
        } elseif ($server->authentication_method === 'key') {
            return $this->authenticateWithPrivateKey($server);
        }

        $this->lastError = "Unsupported authentication method: {$server->authentication_method}";
        return false;
    }

    /**
     * Authenticate to a backup server (assuming password for now).
     */
    protected function authenticateBackupServer(BackupServer $server): bool
    {
        // For backup servers, we'll use password authentication
        // This can be extended to support key authentication if needed
        return $this->ssh->login($server->username, $server->password);
    }

    /**
     * Authenticate using private key.
     */
    protected function authenticateWithPrivateKey(SourceServer $server): bool
    {
        try {
            if (empty($server->private_key)) {
                $this->lastError = "Private key is empty for server {$server->name}";
                return false;
            }

            // Load the private key
            $key = PublicKeyLoader::load($server->private_key, $server->private_key_passphrase ?? '');
            
            return $this->ssh->login($server->username, $key);

        } catch (Exception $e) {
            $this->lastError = "Private key authentication failed: " . $e->getMessage();
            Log::error("Private key authentication failed for server {$server->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Execute a command on the remote server.
     */
    public function execute(string $command): array
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            $output = $this->ssh->exec($command);
            $exitCode = $this->ssh->getExitStatus();

            return [
                'output' => $output,
                'exit_code' => $exitCode,
                'success' => $exitCode === 0
            ];

        } catch (Exception $e) {
            $this->lastError = "Command execution failed: " . $e->getMessage();
            throw $e;
        }
    }

    /**
     * Disconnect from the server.
     */
    public function disconnect(): void
    {
        if ($this->ssh) {
            $this->ssh->disconnect();
            $this->ssh = null;
        }
        $this->server = null;
        Log::debug("SSH connection disconnected");
    }

    /**
     * Check if currently connected to the server.
     */
    public function isConnected(): bool
    {
        return $this->ssh !== null && $this->ssh->isConnected();
    }

    /**
     * Test the connection to verify it's working.
     */
    public function testConnection(): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            $result = $this->execute('echo "connection_test"');
            return $result['success'] && trim($result['output']) === 'connection_test';
        } catch (Exception $e) {
            $this->lastError = "Connection test failed: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Get the last error message if any.
     */
    public function getLastError(): string
    {
        return $this->lastError;
    }

    /**
     * Get connection timeout in seconds.
     */
    public function getTimeout(): int
    {
        return $this->timeout;
    }

    /**
     * Set connection timeout in seconds.
     */
    public function setTimeout(int $timeout): void
    {
        $this->timeout = $timeout;
        if ($this->ssh) {
            $this->ssh->setTimeout($timeout);
        }
    }

    /**
     * Get connection information for debugging.
     */
    public function getConnectionInfo(): array
    {
        if (!$this->server) {
            return [];
        }

        return [
            'type' => 'ssh',
            'host' => $this->server->ip_address,
            'port' => $this->server->port ?: 22,
            'username' => $this->server->username,
            'connected' => $this->isConnected(),
            'timeout' => $this->timeout,
            'auth_method' => $this->server instanceof SourceServer ? $this->server->authentication_method : 'password'
        ];
    }

    /**
     * Get the underlying SSH2 connection for advanced operations.
     */
    public function getSsh2Connection(): ?SSH2
    {
        return $this->ssh;
    }
}
