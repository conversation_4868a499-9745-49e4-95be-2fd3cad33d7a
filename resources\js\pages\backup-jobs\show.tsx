import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { 
    ArrowLeft, 
    MoreHorizontal, 
    Edit, 
    Trash2, 
    Play, 
    Pause, 
    Calendar, 
    Clock, 
    Server, 
    HardDrive, 
    Shield, 
    Archive,
    FileText,
    CheckCircle,
    XCircle,
    AlertCircle
} from 'lucide-react';

interface BackupLog {
    id: number;
    status: 'running' | 'completed' | 'failed' | 'cancelled';
    started_at: string;
    completed_at?: string;
    duration_seconds?: number;
    backup_size_bytes?: number;
    error_message?: string;
}

interface BackupJob {
    id: number;
    name: string;
    source_server: {
        id: number;
        name: string;
        ip_address: string;
    };
    backup_server: {
        id: number;
        name: string;
        protocol: string;
        ip_address: string;
    };
    source_path: string;
    destination_path: string;
    schedule: string;
    status: 'active' | 'paused' | 'disabled';
    last_run?: string;
    next_run?: string;
    retention_policy_days?: number;
    compression_enabled: boolean;
    encryption_enabled: boolean;
    description?: string;
    created_at: string;
    backup_logs: BackupLog[];
}

interface Props {
    backupJob: BackupJob;
}

export default function BackupJobShow({ backupJob }: Props) {
    const handleDelete = () => {
        if (confirm('Are you sure you want to delete this backup job?')) {
            router.delete(route('backup-jobs.destroy', backupJob.id));
        }
    };

    const handleTrigger = () => {
        if (confirm('Are you sure you want to manually trigger this backup job?')) {
            router.post(route('backup-jobs.trigger', backupJob.id));
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active':
                return CheckCircle;
            case 'paused':
                return Pause;
            case 'disabled':
                return XCircle;
            default:
                return AlertCircle;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active':
                return 'bg-green-100 text-green-800';
            case 'paused':
                return 'bg-yellow-100 text-yellow-800';
            case 'disabled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getLogStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return CheckCircle;
            case 'failed':
                return XCircle;
            case 'running':
                return Clock;
            case 'cancelled':
                return AlertCircle;
            default:
                return AlertCircle;
        }
    };

    const getLogStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'failed':
                return 'bg-red-100 text-red-800';
            case 'running':
                return 'bg-blue-100 text-blue-800';
            case 'cancelled':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatBytes = (bytes?: number) => {
        if (!bytes) return 'N/A';
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const formatDuration = (seconds?: number) => {
        if (!seconds) return 'N/A';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours}h ${minutes}m ${secs}s`;
    };

    const StatusIcon = getStatusIcon(backupJob.status);

    return (
        <AppLayout>
            <Head title={`Backup Job - ${backupJob.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('backup-jobs.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Backup Jobs
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold">{backupJob.name}</h1>
                            <p className="text-muted-foreground">Backup job details and recent activity</p>
                        </div>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline">
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                                <Link href={route('backup-jobs.edit', backupJob.id)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                </Link>
                            </DropdownMenuItem>
                            {backupJob.status === 'active' && (
                                <DropdownMenuItem onClick={handleTrigger}>
                                    <Play className="mr-2 h-4 w-4" />
                                    Trigger Now
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                {/* Job Overview */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <StatusIcon className="h-5 w-5" />
                                <span>Status</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Badge className={getStatusColor(backupJob.status)}>
                                {backupJob.status.charAt(0).toUpperCase() + backupJob.status.slice(1)}
                            </Badge>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <Clock className="h-5 w-5" />
                                <span>Schedule</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="font-mono text-sm">{backupJob.schedule}</p>
                            {backupJob.next_run && (
                                <p className="text-sm text-muted-foreground mt-1">
                                    Next: {new Date(backupJob.next_run).toLocaleString()}
                                </p>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <Calendar className="h-5 w-5" />
                                <span>Last Run</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {backupJob.last_run ? (
                                <p className="text-sm">{new Date(backupJob.last_run).toLocaleString()}</p>
                            ) : (
                                <p className="text-sm text-muted-foreground">Never</p>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Configuration Details */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <Server className="h-5 w-5" />
                                <span>Source Server</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <div>
                                <p className="font-medium">{backupJob.source_server.name}</p>
                                <p className="text-sm text-muted-foreground">{backupJob.source_server.ip_address}</p>
                            </div>
                            <div>
                                <p className="text-sm font-medium">Source Path:</p>
                                <p className="text-sm font-mono bg-muted p-2 rounded">{backupJob.source_path}</p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <HardDrive className="h-5 w-5" />
                                <span>Backup Server</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <div>
                                <p className="font-medium">{backupJob.backup_server.name}</p>
                                <p className="text-sm text-muted-foreground">
                                    {backupJob.backup_server.protocol} - {backupJob.backup_server.ip_address}
                                </p>
                            </div>
                            <div>
                                <p className="text-sm font-medium">Destination Path:</p>
                                <p className="text-sm font-mono bg-muted p-2 rounded">{backupJob.destination_path}</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Options and Settings */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Shield className="h-5 w-5" />
                            <span>Backup Options</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div className="flex items-center space-x-2">
                                <Archive className="h-4 w-4" />
                                <span className="text-sm">Compression:</span>
                                <Badge variant={backupJob.compression_enabled ? 'default' : 'secondary'}>
                                    {backupJob.compression_enabled ? 'Enabled' : 'Disabled'}
                                </Badge>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Shield className="h-4 w-4" />
                                <span className="text-sm">Encryption:</span>
                                <Badge variant={backupJob.encryption_enabled ? 'default' : 'secondary'}>
                                    {backupJob.encryption_enabled ? 'Enabled' : 'Disabled'}
                                </Badge>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Clock className="h-4 w-4" />
                                <span className="text-sm">Retention:</span>
                                <Badge variant="outline">
                                    {backupJob.retention_policy_days || 'N/A'} days
                                </Badge>
                            </div>
                        </div>
                        {backupJob.description && (
                            <div className="mt-4">
                                <p className="text-sm font-medium mb-2">Description:</p>
                                <p className="text-sm text-muted-foreground">{backupJob.description}</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Recent Backup Logs */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <FileText className="h-5 w-5" />
                            <span>Recent Backup Activity</span>
                        </CardTitle>
                        <CardDescription>Last 10 backup executions</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {backupJob.backup_logs.length > 0 ? (
                            <div className="space-y-4">
                                {backupJob.backup_logs.map((log) => {
                                    const LogStatusIcon = getLogStatusIcon(log.status);
                                    return (
                                        <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex items-center space-x-4">
                                                <LogStatusIcon className="h-5 w-5" />
                                                <div>
                                                    <div className="flex items-center space-x-2">
                                                        <Badge className={getLogStatusColor(log.status)}>
                                                            {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                                                        </Badge>
                                                        <span className="text-sm text-muted-foreground">
                                                            {new Date(log.started_at).toLocaleString()}
                                                        </span>
                                                    </div>
                                                    {log.error_message && (
                                                        <p className="text-sm text-red-600 mt-1">{log.error_message}</p>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="text-right text-sm text-muted-foreground">
                                                <div>Size: {formatBytes(log.backup_size_bytes)}</div>
                                                <div>Duration: {formatDuration(log.duration_seconds)}</div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No backup logs yet</h3>
                                <p className="text-muted-foreground">
                                    This backup job hasn't been executed yet.
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
