import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useState } from 'react';
import { Play, Plus, MoreHorizontal, Eye, Edit, Trash2, Terminal, Server, Code } from 'lucide-react';
import TerminalModal from '@/components/TerminalModal';

interface Action {
    id: number;
    name: string;
    command: string;
    template: string | null;
    source_server: { id: number; name: string };
    creator: { id: number; name: string };
}

export default function ActionsIndex({ actions = [] }: { actions: Action[] }) {
    const [deleting, setDeleting] = useState<number | null>(null);
    const [selectedAction, setSelectedAction] = useState<Action | null>(null);
    const [isTerminalOpen, setIsTerminalOpen] = useState(false);

    const handleDelete = (id: number) => {
        if (confirm('Are you sure you want to delete this action?')) {
            setDeleting(id);
            router.delete(`/actions/${id}`, {
                onFinish: () => setDeleting(null),
            });
        }
    };

    const handleRun = (action: Action) => {
        setSelectedAction(action);
        setIsTerminalOpen(true);
    };

    return (
        <AppLayout>
            <Head title="Actions" />
            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Actions</h1>
                        <p className="text-muted-foreground">
                            Manage and run custom actions on your source servers
                        </p>
                    </div>
                    <Link href="/actions/create">
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Create Action
                        </Button>
                    </Link>
                </div>

                {/* Actions Grid */}
                {actions.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Terminal className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No actions found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Get started by creating your first action to run commands on your servers.
                            </p>
                            <Link href="/actions/create">
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Create Action
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {actions.map((action) => (
                            <Card key={action.id} className="relative">
                                <CardHeader className="pb-3">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center space-x-2">
                                            <Terminal className="h-5 w-5 text-muted-foreground" />
                                            <CardTitle className="text-lg">{action.name}</CardTitle>
                                        </div>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem asChild>
                                                    <Link href={`/actions/${action.id}`}>
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        View
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem asChild>
                                                    <Link href={`/actions/${action.id}/edit`}>
                                                        <Edit className="mr-2 h-4 w-4" />
                                                        Edit
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem 
                                                    onClick={() => handleDelete(action.id)}
                                                    disabled={deleting === action.id}
                                                    className="text-destructive focus:text-destructive"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                    {action.template && (
                                        <Badge variant="secondary" className="w-fit">
                                            {action.template}
                                        </Badge>
                                    )}
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                        <Server className="h-4 w-4" />
                                        <span>{action.source_server?.name}</span>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-2 text-sm">
                                            <Code className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-mono text-xs">Command:</span>
                                        </div>
                                        <div className="bg-muted p-3 rounded-md">
                                            <code className="text-xs break-all">
                                                {action.command.length > 100 
                                                    ? `${action.command.substring(0, 100)}...` 
                                                    : action.command
                                                }
                                            </code>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between pt-2">
                                        <span className="text-xs text-muted-foreground">
                                            By {action.creator?.name}
                                        </span>
                                        <Button 
                                            size="sm" 
                                            onClick={() => handleRun(action)}
                                            className="bg-green-600 hover:bg-green-700"
                                        >
                                            <Play className="mr-2 h-3 w-3" />
                                            Run
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}
            </div>

            <TerminalModal
                isOpen={isTerminalOpen}
                onClose={() => setIsTerminalOpen(false)}
                action={selectedAction}
            />
        </AppLayout>
    );
} 