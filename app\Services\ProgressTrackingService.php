<?php

namespace App\Services;

use App\Models\BackupLog;
use App\Models\BackupProgress;
use App\Events\BackupProgressUpdated;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class ProgressTrackingService
{
    protected array $stages = [
        'validation' => 0,
        'discovery' => 10,
        'transfer' => 20,
        'compression' => 60,
        'encryption' => 80,
        'finalization' => 90,
        'completed' => 100,
    ];

    protected bool $broadcastingEnabled = false;

    /**
     * Start tracking progress for a backup job.
     */
    public function startTracking(int $backupLogId, string $operation = 'backup', bool $enableBroadcasting = false): void
    {
        try {
            $this->broadcastingEnabled = $enableBroadcasting;
            
            // Clear any existing current stage markers for this backup log
            BackupProgress::where('backup_log_id', $backupLogId)
                ->update(['is_current_stage' => false]);

            // Create initial progress record
            $progress = BackupProgress::create([
                'backup_log_id' => $backupLogId,
                'stage' => 'validation',
                'percentage' => 0,
                'message' => 'Starting ' . $operation . '...',
                'stage_started_at' => now(),
                'is_current_stage' => true,
            ]);

            // Cache the current progress for quick access
            $this->cacheProgress($backupLogId, $progress);

            if ($this->broadcastingEnabled) {
                event(new BackupProgressUpdated($progress));
            }

            Log::info("Started progress tracking for backup log: {$backupLogId}");

        } catch (Exception $e) {
            Log::error("Failed to start progress tracking: " . $e->getMessage());
        }
    }

    /**
     * Update the current stage of the backup.
     */
    public function updateStage(int $backupLogId, string $stage, float $percentage = null): void
    {
        try {
            // Complete the current stage
            $currentProgress = BackupProgress::where('backup_log_id', $backupLogId)
                ->where('is_current_stage', true)
                ->first();

            if ($currentProgress) {
                $currentProgress->update([
                    'stage_completed_at' => now(),
                    'is_current_stage' => false,
                ]);
            }

            // Calculate percentage if not provided
            if ($percentage === null) {
                $percentage = $this->stages[$stage] ?? 0;
            }

            // Create new stage progress record
            $progress = BackupProgress::create([
                'backup_log_id' => $backupLogId,
                'stage' => $stage,
                'percentage' => $percentage,
                'message' => 'Starting ' . $stage . ' stage...',
                'stage_started_at' => now(),
                'is_current_stage' => true,
            ]);

            $this->cacheProgress($backupLogId, $progress);

            if ($this->broadcastingEnabled) {
                event(new BackupProgressUpdated($progress));
            }

            Log::debug("Updated stage to {$stage} for backup log: {$backupLogId}");

        } catch (Exception $e) {
            Log::error("Failed to update stage: " . $e->getMessage());
        }
    }

    /**
     * Update progress within the current stage.
     */
    public function updateProgress(int $backupLogId, float $percentage, string $message = null): void
    {
        try {
            $progress = BackupProgress::where('backup_log_id', $backupLogId)
                ->where('is_current_stage', true)
                ->first();

            if (!$progress) {
                Log::warning("No current progress record found for backup log: {$backupLogId}");
                return;
            }

            $updateData = ['percentage' => $percentage];
            if ($message) {
                $updateData['message'] = $message;
            }

            $progress->update($updateData);
            $this->cacheProgress($backupLogId, $progress);

            if ($this->broadcastingEnabled) {
                event(new BackupProgressUpdated($progress));
            }

        } catch (Exception $e) {
            Log::error("Failed to update progress: " . $e->getMessage());
        }
    }

    /**
     * Complete the tracking for a backup job.
     */
    public function completeTracking(int $backupLogId, float $percentage = 100, string $message = 'Operation completed successfully'): void
    {
        try {
            $progress = BackupProgress::where('backup_log_id', $backupLogId)
                ->where('is_current_stage', true)
                ->first();

            if ($progress) {
                $progress->update([
                    'percentage' => $percentage,
                    'message' => $message,
                    'stage_completed_at' => now(),
                    'is_current_stage' => false,
                ]);

                $this->cacheProgress($backupLogId, $progress);

                if ($this->broadcastingEnabled) {
                    event(new BackupProgressUpdated($progress));
                }
            }

            // Clear cache
            Cache::forget("backup_progress_{$backupLogId}");

            Log::info("Completed progress tracking for backup log: {$backupLogId}");

        } catch (Exception $e) {
            Log::error("Failed to complete progress tracking: " . $e->getMessage());
        }
    }

    /**
     * Get current progress for a backup job.
     */
    public function getProgress(int $backupLogId): ?array
    {
        try {
            // Try cache first
            $cached = Cache::get("backup_progress_{$backupLogId}");
            if ($cached) {
                return $cached;
            }

            // Get from database
            $progress = BackupProgress::where('backup_log_id', $backupLogId)
                ->where('is_current_stage', true)
                ->first();

            if (!$progress) {
                return null;
            }

            $progressData = [
                'stage' => $progress->stage,
                'percentage' => $progress->percentage,
                'message' => $progress->message,
                'files_processed' => $progress->files_processed,
                'total_files' => $progress->total_files,
                'bytes_processed' => $progress->bytes_processed,
                'total_bytes' => $progress->total_bytes,
                'transfer_speed_mbps' => $progress->transfer_speed_mbps,
                'stage_started_at' => $progress->stage_started_at,
                'updated_at' => $progress->updated_at,
            ];

            $this->cacheProgress($backupLogId, $progressData);
            return $progressData;

        } catch (Exception $e) {
            Log::error("Failed to get progress: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Save progress to persistent storage.
     */
    public function saveProgress(int $backupLogId): bool
    {
        try {
            $progress = $this->getProgress($backupLogId);
            if (!$progress) {
                return false;
            }

            // Progress is already saved in database, just ensure cache is updated
            $this->cacheProgress($backupLogId, $progress);
            return true;

        } catch (Exception $e) {
            Log::error("Failed to save progress: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Restore progress from persistent storage.
     */
    public function restoreProgress(int $backupLogId): ?array
    {
        try {
            return $this->getProgress($backupLogId);
        } catch (Exception $e) {
            Log::error("Failed to restore progress: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Start tracking files for a backup job.
     */
    public function startFileTracking(int $backupLogId, array $files): void
    {
        try {
            $totalFiles = count($files);
            $totalBytes = array_sum($files);

            $progress = BackupProgress::where('backup_log_id', $backupLogId)
                ->where('is_current_stage', true)
                ->first();

            if ($progress) {
                $progress->update([
                    'total_files' => $totalFiles,
                    'total_bytes' => $totalBytes,
                    'files_processed' => 0,
                    'bytes_processed' => 0,
                ]);

                $this->cacheProgress($backupLogId, $progress);
            }

            // Cache file list for tracking
            Cache::put("backup_files_{$backupLogId}", $files, 3600);

            Log::debug("Started file tracking for {$totalFiles} files, {$totalBytes} bytes");

        } catch (Exception $e) {
            Log::error("Failed to start file tracking: " . $e->getMessage());
        }
    }

    /**
     * Start tracking transfer of a specific file.
     */
    public function startFileTransfer(int $backupLogId, string $filePath, int $fileSize): void
    {
        try {
            $fileData = Cache::get("backup_file_progress_{$backupLogId}", []);
            $fileData[$filePath] = [
                'size' => $fileSize,
                'transferred' => 0,
                'started_at' => now(),
                'status' => 'transferring',
            ];

            Cache::put("backup_file_progress_{$backupLogId}", $fileData, 3600);

            Log::debug("Started transfer for file: {$filePath} ({$fileSize} bytes)");

        } catch (Exception $e) {
            Log::error("Failed to start file transfer tracking: " . $e->getMessage());
        }
    }

    /**
     * Update progress for a specific file transfer.
     */
    public function updateFileProgress(int $backupLogId, string $filePath, int $bytesTransferred): void
    {
        try {
            $fileData = Cache::get("backup_file_progress_{$backupLogId}", []);

            if (isset($fileData[$filePath])) {
                $fileData[$filePath]['transferred'] = $bytesTransferred;
                Cache::put("backup_file_progress_{$backupLogId}", $fileData, 3600);

                // Update overall progress
                $this->updateOverallFileProgress($backupLogId);
            }

        } catch (Exception $e) {
            Log::error("Failed to update file progress: " . $e->getMessage());
        }
    }

    /**
     * Complete transfer of a specific file.
     */
    public function completeFileTransfer(int $backupLogId, string $filePath): void
    {
        try {
            $fileData = Cache::get("backup_file_progress_{$backupLogId}", []);

            if (isset($fileData[$filePath])) {
                $fileData[$filePath]['status'] = 'completed';
                $fileData[$filePath]['completed_at'] = now();
                $fileData[$filePath]['transferred'] = $fileData[$filePath]['size'];

                Cache::put("backup_file_progress_{$backupLogId}", $fileData, 3600);

                // Update overall progress
                $this->updateOverallFileProgress($backupLogId);

                Log::debug("Completed transfer for file: {$filePath}");
            }

        } catch (Exception $e) {
            Log::error("Failed to complete file transfer: " . $e->getMessage());
        }
    }

    /**
     * Get file transfer report.
     */
    public function getFileReport(int $backupLogId): array
    {
        try {
            $fileData = Cache::get("backup_file_progress_{$backupLogId}", []);

            $report = [
                'total_files' => count($fileData),
                'completed_files' => 0,
                'failed_files' => 0,
                'total_bytes' => 0,
                'transferred_bytes' => 0,
                'files' => [],
            ];

            foreach ($fileData as $filePath => $data) {
                $report['total_bytes'] += $data['size'];
                $report['transferred_bytes'] += $data['transferred'];

                if ($data['status'] === 'completed') {
                    $report['completed_files']++;
                } elseif ($data['status'] === 'failed') {
                    $report['failed_files']++;
                }

                $report['files'][$filePath] = $data;
            }

            return $report;

        } catch (Exception $e) {
            Log::error("Failed to get file report: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update overall file progress based on individual file progress.
     */
    protected function updateOverallFileProgress(int $backupLogId): void
    {
        try {
            $fileData = Cache::get("backup_file_progress_{$backupLogId}", []);

            $totalFiles = count($fileData);
            $completedFiles = 0;
            $totalBytes = 0;
            $transferredBytes = 0;

            foreach ($fileData as $data) {
                $totalBytes += $data['size'];
                $transferredBytes += $data['transferred'];

                if ($data['status'] === 'completed') {
                    $completedFiles++;
                }
            }

            $progress = BackupProgress::where('backup_log_id', $backupLogId)
                ->where('is_current_stage', true)
                ->first();

            if ($progress) {
                $progress->update([
                    'files_processed' => $completedFiles,
                    'bytes_processed' => $transferredBytes,
                ]);

                $this->cacheProgress($backupLogId, $progress);
            }

        } catch (Exception $e) {
            Log::error("Failed to update overall file progress: " . $e->getMessage());
        }
    }

    /**
     * Cache progress data for quick access.
     */
    protected function cacheProgress(int $backupLogId, $progress): void
    {
        try {
            $data = is_array($progress) ? $progress : [
                'stage' => $progress->stage,
                'percentage' => $progress->percentage,
                'message' => $progress->message,
                'files_processed' => $progress->files_processed,
                'total_files' => $progress->total_files,
                'bytes_processed' => $progress->bytes_processed,
                'total_bytes' => $progress->total_bytes,
                'transfer_speed_mbps' => $progress->transfer_speed_mbps,
                'stage_started_at' => $progress->stage_started_at,
                'updated_at' => $progress->updated_at,
            ];

            Cache::put("backup_progress_{$backupLogId}", $data, 3600); // Cache for 1 hour
        } catch (Exception $e) {
            Log::warning("Failed to cache progress: " . $e->getMessage());
        }
    }
}
