import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Calendar } from 'lucide-react';

interface SourceServer {
    id: number;
    name: string;
    ip_address: string;
}

interface BackupServer {
    id: number;
    name: string;
    protocol: string;
    ip_address: string;
}

interface BackupJob {
    id: number;
    name: string;
    source_server_id: number;
    backup_server_id: number;
    source_path: string;
    destination_path: string;
    schedule: string;
    status: 'active' | 'paused' | 'disabled';
    retention_policy_days?: number;
    compression_enabled: boolean;
    encryption_enabled: boolean;
    description?: string;
    backup_options?: any;
}

interface Props {
    backupJob: BackupJob;
    sourceServers: SourceServer[];
    backupServers: BackupServer[];
}

export default function BackupJobEdit({ backupJob, sourceServers, backupServers }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: backupJob.name,
        source_server_id: backupJob.source_server_id.toString(),
        backup_server_id: backupJob.backup_server_id.toString(),
        source_path: backupJob.source_path,
        destination_path: backupJob.destination_path,
        schedule: backupJob.schedule,
        status: backupJob.status,
        retention_policy_days: backupJob.retention_policy_days || 30,
        compression_enabled: backupJob.compression_enabled,
        encryption_enabled: backupJob.encryption_enabled,
        description: backupJob.description || '',
        backup_options: backupJob.backup_options || {},
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('backup-jobs.update', backupJob.id));
    };

    const commonSchedules = [
        { value: '0 2 * * *', label: 'Daily at 2:00 AM' },
        { value: '0 2 * * 0', label: 'Weekly (Sunday at 2:00 AM)' },
        { value: '0 2 1 * *', label: 'Monthly (1st at 2:00 AM)' },
        { value: '0 */6 * * *', label: 'Every 6 hours' },
        { value: '0 */12 * * *', label: 'Every 12 hours' },
    ];

    return (
        <AppLayout>
            <Head title={`Edit Backup Job - ${backupJob.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('backup-jobs.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Backup Jobs
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold">Edit Backup Job</h1>
                            <p className="text-muted-foreground">Update backup job configuration</p>
                        </div>
                    </div>
                </div>

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Calendar className="h-5 w-5" />
                            <span>Backup Job Details</span>
                        </CardTitle>
                        <CardDescription>
                            Configure the backup job settings and schedule
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Basic Information */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Job Name</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Enter backup job name"
                                        className={errors.name ? 'border-red-500' : ''}
                                    />
                                    {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="status">Status</Label>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value as any)}>
                                        <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="paused">Paused</SelectItem>
                                            <SelectItem value="disabled">Disabled</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && <p className="text-sm text-red-500">{errors.status}</p>}
                                </div>
                            </div>

                            {/* Server Configuration */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="source_server_id">Source Server</Label>
                                    <Select value={data.source_server_id} onValueChange={(value) => setData('source_server_id', value)}>
                                        <SelectTrigger className={errors.source_server_id ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select source server" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {sourceServers.map((server) => (
                                                <SelectItem key={server.id} value={server.id.toString()}>
                                                    {server.name} ({server.ip_address})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.source_server_id && <p className="text-sm text-red-500">{errors.source_server_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="backup_server_id">Backup Server</Label>
                                    <Select value={data.backup_server_id} onValueChange={(value) => setData('backup_server_id', value)}>
                                        <SelectTrigger className={errors.backup_server_id ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select backup server" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {backupServers.map((server) => (
                                                <SelectItem key={server.id} value={server.id.toString()}>
                                                    {server.name} ({server.protocol})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.backup_server_id && <p className="text-sm text-red-500">{errors.backup_server_id}</p>}
                                </div>
                            </div>

                            {/* Path Configuration */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="source_path">Source Path</Label>
                                    <Input
                                        id="source_path"
                                        value={data.source_path}
                                        onChange={(e) => setData('source_path', e.target.value)}
                                        placeholder="/path/to/backup"
                                        className={errors.source_path ? 'border-red-500' : ''}
                                    />
                                    {errors.source_path && <p className="text-sm text-red-500">{errors.source_path}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="destination_path">Destination Path</Label>
                                    <Input
                                        id="destination_path"
                                        value={data.destination_path}
                                        onChange={(e) => setData('destination_path', e.target.value)}
                                        placeholder="/backup/destination"
                                        className={errors.destination_path ? 'border-red-500' : ''}
                                    />
                                    {errors.destination_path && <p className="text-sm text-red-500">{errors.destination_path}</p>}
                                </div>
                            </div>

                            {/* Schedule Configuration */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="schedule">Schedule (CRON)</Label>
                                    <Select value={data.schedule} onValueChange={(value) => setData('schedule', value)}>
                                        <SelectTrigger className={errors.schedule ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select schedule" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {commonSchedules.map((schedule) => (
                                                <SelectItem key={schedule.value} value={schedule.value}>
                                                    {schedule.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <Input
                                        value={data.schedule}
                                        onChange={(e) => setData('schedule', e.target.value)}
                                        placeholder="Custom CRON expression"
                                        className="mt-2"
                                    />
                                    {errors.schedule && <p className="text-sm text-red-500">{errors.schedule}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="retention_policy_days">Retention Policy (Days)</Label>
                                    <Input
                                        id="retention_policy_days"
                                        type="number"
                                        min="1"
                                        max="3650"
                                        value={data.retention_policy_days}
                                        onChange={(e) => setData('retention_policy_days', parseInt(e.target.value))}
                                        className={errors.retention_policy_days ? 'border-red-500' : ''}
                                    />
                                    {errors.retention_policy_days && <p className="text-sm text-red-500">{errors.retention_policy_days}</p>}
                                </div>
                            </div>

                            {/* Options */}
                            <div className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="compression_enabled"
                                        checked={data.compression_enabled}
                                        onCheckedChange={(checked) => setData('compression_enabled', !!checked)}
                                    />
                                    <Label htmlFor="compression_enabled">Enable Compression</Label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="encryption_enabled"
                                        checked={data.encryption_enabled}
                                        onCheckedChange={(checked) => setData('encryption_enabled', !!checked)}
                                    />
                                    <Label htmlFor="encryption_enabled">Enable Encryption</Label>
                                </div>
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Optional description for this backup job"
                                    rows={3}
                                    className={errors.description ? 'border-red-500' : ''}
                                />
                                {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                            </div>

                            {/* Submit Button */}
                            <div className="flex justify-end space-x-4">
                                <Link href={route('backup-jobs.index')}>
                                    <Button type="button" variant="outline">
                                        Cancel
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    <Save className="mr-2 h-4 w-4" />
                                    {processing ? 'Updating...' : 'Update Backup Job'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
