<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('source_servers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('ip_address');
            $table->integer('port')->default(22);
            $table->string('username');
            $table->enum('authentication_method', ['password', 'private_key'])->default('password');
            $table->text('password')->nullable(); // Encrypted
            $table->text('private_key')->nullable(); // Encrypted
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_connection_test')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['name', 'ip_address']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('source_servers');
    }
};
