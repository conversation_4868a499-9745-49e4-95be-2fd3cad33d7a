import { useState, useCallback, useRef, useEffect } from 'react';
import axios from 'axios';

interface ConnectionTestStatus {
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    message: string;
    started_at?: string;
    completed_at?: string;
    duration_ms?: number;
    error_message?: string;
    success?: boolean;
}

interface ConnectionTestResult {
    success: boolean;
    message: string;
    test_id?: string;
    status_url?: string;
}

interface UseConnectionTestOptions {
    serverType: 'source' | 'backup';
    serverId: number;
}

export function useConnectionTest(options?: UseConnectionTestOptions) {
    const [isLoading, setIsLoading] = useState(false);
    const [status, setStatus] = useState<ConnectionTestStatus | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [testId, setTestId] = useState<string | null>(null);
    const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);

    // Create a unique key for this instance
    const instanceKey = options ? `${options.serverType}-${options.serverId}` : `global-${Math.random()}`;

    // Reset state when server changes
    useEffect(() => {
        if (options) {
            setIsLoading(false);
            setStatus(null);
            setError(null);
            setTestId(null);
            if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current);
                pollIntervalRef.current = null;
            }
        }
    }, [options?.serverType, options?.serverId]);

    const clearPolling = useCallback(() => {
        if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current);
            pollIntervalRef.current = null;
        }
    }, []);

    const pollStatus = useCallback(async (testId: string) => {
        try {
            const response = await axios.get(`/api/connection-test/status/${testId}`);
            const statusData = response.data.status as ConnectionTestStatus;
            
            setStatus(statusData);

            // Stop polling if test is complete
            if (['completed', 'failed', 'cancelled'].includes(statusData.status)) {
                clearPolling();
                setIsLoading(false);
            }
        } catch (err) {
            console.error('Failed to poll connection test status:', err);
            setError('Failed to get test status');
            clearPolling();
            setIsLoading(false);
        }
    }, [clearPolling]);

    const startTest = useCallback(async (serverType: 'source' | 'backup', serverId: number) => {
        setIsLoading(true);
        setError(null);
        setStatus(null);
        setTestId(null);
        clearPolling();

        try {
            const endpoint = serverType === 'source'
                ? `/api/connection-test/source-servers/${serverId}`
                : `/api/connection-test/backup-servers/${serverId}`;

            const response = await axios.post(endpoint);
            const result = response.data as ConnectionTestResult;

            if (result.success && result.test_id) {
                setTestId(result.test_id);

                // Start polling for status updates
                pollIntervalRef.current = setInterval(() => {
                    pollStatus(result.test_id!);
                }, 1000); // Poll every second

                // Initial status poll
                pollStatus(result.test_id);
            } else {
                setError(result.message || 'Failed to start connection test');
                setIsLoading(false);
            }
        } catch (err: any) {
            const errorMessage = err.response?.data?.message || 'Failed to start connection test';
            setError(errorMessage);
            setIsLoading(false);
        }
    }, [pollStatus, clearPolling]);

    const cancelTest = useCallback(async (testId: string) => {
        try {
            await axios.post(`/api/connection-test/cancel/${testId}`);
            clearPolling();
            setIsLoading(false);
            setStatus(prev => prev ? { ...prev, status: 'cancelled' } : null);
        } catch (err) {
            console.error('Failed to cancel connection test:', err);
        }
    }, [clearPolling]);

    const reset = useCallback(() => {
        clearPolling();
        setIsLoading(false);
        setStatus(null);
        setError(null);
        setTestId(null);
    }, [clearPolling]);

    // Cleanup on unmount
    const cleanup = useCallback(() => {
        clearPolling();
    }, [clearPolling]);

    return {
        isLoading,
        status,
        error,
        testId,
        startTest,
        cancelTest,
        reset,
        cleanup
    };
}
