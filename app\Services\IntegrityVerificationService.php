<?php

namespace App\Services;

use App\Models\SourceServer;
use App\Models\BackupServer;
use App\Services\Connections\ConnectionInterface;
use Illuminate\Support\Facades\Log;
use Exception;

class IntegrityVerificationService
{
    protected ConnectionService $connectionService;
    protected array $supportedAlgorithms = ['md5', 'sha1', 'sha256'];
    protected string $defaultAlgorithm = 'sha256';

    public function __construct(ConnectionService $connectionService)
    {
        $this->connectionService = $connectionService;
    }

    /**
     * Calculate checksum for a local file.
     */
    public function calculateChecksum(string $filePath, string $algorithm = 'sha256'): string
    {
        if (!in_array($algorithm, $this->supportedAlgorithms)) {
            throw new Exception("Unsupported checksum algorithm: {$algorithm}");
        }

        if (!file_exists($filePath)) {
            throw new Exception("File not found: {$filePath}");
        }

        $checksum = hash_file($algorithm, $filePath);
        
        if ($checksum === false) {
            throw new Exception("Failed to calculate {$algorithm} checksum for: {$filePath}");
        }

        return $checksum;
    }

    /**
     * Calculate checksum for a remote file.
     */
    public function calculateRemoteChecksum(
        SourceServer|BackupServer $server,
        string $filePath,
        string $algorithm = 'sha256'
    ): string {
        try {
            $connection = $server instanceof SourceServer
                ? $this->connectionService->connectToSourceServer($server, 'ssh')
                : $this->connectionService->connectToBackupServer($server);

            // Try to use remote checksum command
            $command = $this->getChecksumCommand($algorithm, $filePath);
            $result = $connection->exec($command);

            if ($result) {
                // Parse checksum from command output
                $checksum = $this->parseChecksumOutput($result, $algorithm);
                if ($checksum) {
                    return $checksum;
                }
            }

            // Fallback: download file and calculate locally
            return $this->calculateRemoteChecksumByDownload($connection, $filePath, $algorithm);

        } catch (Exception $e) {
            Log::error("Failed to calculate remote checksum: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Verify file integrity between source and backup.
     */
    public function verifyFileIntegrity(
        SourceServer $sourceServer,
        string $sourcePath,
        BackupServer $backupServer,
        string $backupPath,
        string $algorithm = 'sha256'
    ): bool {
        try {
            $sourceChecksum = $this->calculateRemoteChecksum($sourceServer, $sourcePath, $algorithm);
            $backupChecksum = $this->calculateRemoteChecksum($backupServer, $backupPath, $algorithm);

            $isValid = $sourceChecksum === $backupChecksum;

            if ($isValid) {
                Log::info("File integrity verified: {$sourcePath}");
            } else {
                Log::warning("File integrity check failed: {$sourcePath} (source: {$sourceChecksum}, backup: {$backupChecksum})");
            }

            return $isValid;

        } catch (Exception $e) {
            Log::error("File integrity verification failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify integrity of multiple files.
     */
    public function verifyMultipleFiles(
        SourceServer $sourceServer,
        BackupServer $backupServer,
        array $filePairs,
        string $algorithm = 'sha256'
    ): array {
        $results = [
            'verified' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($filePairs as $pair) {
            try {
                $sourcePath = $pair['source'];
                $backupPath = $pair['backup'];

                $isValid = $this->verifyFileIntegrity(
                    $sourceServer,
                    $sourcePath,
                    $backupServer,
                    $backupPath,
                    $algorithm
                );

                if ($isValid) {
                    $results['verified']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Integrity check failed: {$sourcePath}";
                }

            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Error verifying {$pair['source']}: " . $e->getMessage();
            }
        }

        Log::info("Integrity verification summary: {$results['verified']} verified, {$results['failed']} failed");

        return $results;
    }

    /**
     * Detect file corruption by comparing checksums.
     */
    public function detectCorruption(
        array $expectedChecksums,
        SourceServer|BackupServer $server,
        string $algorithm = 'sha256'
    ): array {
        $corrupted = [];

        foreach ($expectedChecksums as $filePath => $expectedChecksum) {
            try {
                $actualChecksum = $this->calculateRemoteChecksum($server, $filePath, $algorithm);

                if ($actualChecksum !== $expectedChecksum) {
                    $corrupted[] = [
                        'path' => $filePath,
                        'expected' => $expectedChecksum,
                        'actual' => $actualChecksum
                    ];
                }

            } catch (Exception $e) {
                $corrupted[] = [
                    'path' => $filePath,
                    'expected' => $expectedChecksum,
                    'actual' => null,
                    'error' => $e->getMessage()
                ];
            }
        }

        if (!empty($corrupted)) {
            Log::warning("Detected " . count($corrupted) . " corrupted files");
        }

        return $corrupted;
    }

    /**
     * Create integrity report for a backup.
     */
    public function createIntegrityReport(
        SourceServer $sourceServer,
        BackupServer $backupServer,
        array $files,
        string $algorithm = 'sha256'
    ): array {
        $report = [
            'algorithm' => $algorithm,
            'total_files' => count($files),
            'verified_files' => 0,
            'failed_files' => 0,
            'corrupted_files' => [],
            'errors' => [],
            'created_at' => now()->toISOString()
        ];

        foreach ($files as $file) {
            try {
                $sourcePath = $file['source_path'];
                $backupPath = $file['backup_path'];

                $isValid = $this->verifyFileIntegrity(
                    $sourceServer,
                    $sourcePath,
                    $backupServer,
                    $backupPath,
                    $algorithm
                );

                if ($isValid) {
                    $report['verified_files']++;
                } else {
                    $report['failed_files']++;
                    $report['corrupted_files'][] = $sourcePath;
                }

            } catch (Exception $e) {
                $report['failed_files']++;
                $report['errors'][] = "Error verifying {$file['source_path']}: " . $e->getMessage();
            }
        }

        $report['integrity_score'] = $report['total_files'] > 0
            ? ($report['verified_files'] / $report['total_files']) * 100
            : 0;

        return $report;
    }

    /**
     * Get checksum command for different algorithms.
     */
    protected function getChecksumCommand(string $algorithm, string $filePath): string
    {
        $escapedPath = escapeshellarg($filePath);

        return match ($algorithm) {
            'md5' => "md5sum {$escapedPath} 2>/dev/null || md5 {$escapedPath} 2>/dev/null",
            'sha1' => "sha1sum {$escapedPath} 2>/dev/null || shasum -a 1 {$escapedPath} 2>/dev/null",
            'sha256' => "sha256sum {$escapedPath} 2>/dev/null || shasum -a 256 {$escapedPath} 2>/dev/null",
            default => throw new Exception("Unsupported algorithm: {$algorithm}")
        };
    }

    /**
     * Parse checksum from command output.
     */
    protected function parseChecksumOutput(string $output, string $algorithm): ?string
    {
        $lines = explode("\n", trim($output));
        
        foreach ($lines as $line) {
            if (empty($line)) continue;
            
            // Most checksum commands output: "checksum filename"
            $parts = preg_split('/\s+/', $line, 2);
            
            if (count($parts) >= 1) {
                $checksum = $parts[0];
                
                // Validate checksum format
                if ($this->isValidChecksumFormat($checksum, $algorithm)) {
                    return $checksum;
                }
            }
        }

        return null;
    }

    /**
     * Validate checksum format.
     */
    protected function isValidChecksumFormat(string $checksum, string $algorithm): bool
    {
        $expectedLength = match ($algorithm) {
            'md5' => 32,
            'sha1' => 40,
            'sha256' => 64,
            default => 0
        };

        return strlen($checksum) === $expectedLength && ctype_xdigit($checksum);
    }

    /**
     * Calculate remote checksum by downloading file.
     */
    protected function calculateRemoteChecksumByDownload(
        ConnectionInterface $connection,
        string $filePath,
        string $algorithm
    ): string {
        $tempFile = tempnam(sys_get_temp_dir(), 'checksum_');
        
        try {
            // Download file to temporary location
            if (!$connection->get($filePath, $tempFile)) {
                throw new Exception("Failed to download file for checksum calculation");
            }

            // Calculate checksum locally
            $checksum = $this->calculateChecksum($tempFile, $algorithm);

            return $checksum;

        } finally {
            // Clean up temporary file
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    /**
     * Set default checksum algorithm.
     */
    public function setDefaultAlgorithm(string $algorithm): void
    {
        if (!in_array($algorithm, $this->supportedAlgorithms)) {
            throw new Exception("Unsupported algorithm: {$algorithm}");
        }

        $this->defaultAlgorithm = $algorithm;
    }

    /**
     * Get supported algorithms.
     */
    public function getSupportedAlgorithms(): array
    {
        return $this->supportedAlgorithms;
    }
}
