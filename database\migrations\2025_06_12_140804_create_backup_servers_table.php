<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_servers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('protocol', ['ftp', 'sftp'])->default('sftp');
            $table->string('ip_address');
            $table->integer('port')->nullable(); // Will default based on protocol
            $table->string('username');
            $table->text('password'); // Encrypted
            $table->string('base_directory')->default('/backups');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_connection_test')->nullable();
            $table->json('connection_options')->nullable(); // For additional FTP/SFTP options
            $table->timestamps();

            // Indexes
            $table->index(['name', 'protocol']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_servers');
    }
};
