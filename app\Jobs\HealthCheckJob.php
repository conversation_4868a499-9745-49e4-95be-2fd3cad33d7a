<?php

namespace App\Jobs;

use App\Models\SourceServer;
use App\Models\BackupServer;
use App\Services\ConnectionService;
use App\Services\ConnectionStatusService;
use App\Services\ConnectionRetryService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class HealthCheckJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 2;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * The server to check (optional - if null, check all servers).
     */
    public SourceServer|BackupServer|null $server;

    /**
     * Health check configuration.
     */
    public array $config;

    /**
     * Create a new job instance.
     */
    public function __construct(SourceServer|BackupServer $server = null, array $config = [])
    {
        $this->server = $server;
        $this->config = array_merge([
            'use_retry' => true,
            'update_server_status' => true,
            'log_results' => true,
            'alert_on_failure' => true
        ], $config);

        $this->onQueue('maintenance');
    }

    /**
     * Execute the job.
     */
    public function handle(
        ConnectionService $connectionService,
        ConnectionStatusService $statusService,
        ConnectionRetryService $retryService
    ): void {
        if ($this->server) {
            $this->checkSingleServer($this->server, $connectionService, $statusService, $retryService);
        } else {
            $this->checkAllServers($connectionService, $statusService, $retryService);
        }
    }

    /**
     * Check health of a single server.
     */
    protected function checkSingleServer(
        SourceServer|BackupServer $server,
        ConnectionService $connectionService,
        ConnectionStatusService $statusService,
        ConnectionRetryService $retryService
    ): void {
        $serverType = $server instanceof SourceServer ? 'source' : 'backup';
        Log::info("Starting health check for {$serverType} server: {$server->name}");

        try {
            $startTime = microtime(true);

            // Perform connection test
            if ($this->config['use_retry']) {
                $success = $retryService->testConnectionWithRetry($server);
                $retryCount = $retryService->getRetryCount();
            } else {
                $success = $connectionService->testConnection($server);
                $retryCount = 0;
            }

            $duration = round((microtime(true) - $startTime) * 1000);

            // Update server's last connection test timestamp
            if ($this->config['update_server_status']) {
                $server->update([
                    'last_connection_test' => now(),
                ]);
            }

            // Store health check result
            $this->storeHealthCheckResult($server, $success, $duration, $retryCount);

            // Log results
            if ($this->config['log_results']) {
                if ($success) {
                    Log::info("Health check successful for {$serverType} server {$server->name} ({$duration}ms, {$retryCount} retries)");
                } else {
                    Log::warning("Health check failed for {$serverType} server {$server->name} ({$duration}ms, {$retryCount} retries)");
                }
            }

            // Handle failure alerts
            if (!$success && $this->config['alert_on_failure']) {
                $this->handleHealthCheckFailure($server, $retryService->getRetryHistory());
            }

        } catch (Exception $e) {
            Log::error("Health check error for {$serverType} server {$server->name}: " . $e->getMessage());

            if ($this->config['update_server_status']) {
                $server->update([
                    'last_connection_test' => now(),
                ]);
            }

            $this->storeHealthCheckResult($server, false, 0, 0, $e->getMessage());

            if ($this->config['alert_on_failure']) {
                $this->handleHealthCheckFailure($server, [], $e->getMessage());
            }
        }
    }

    /**
     * Check health of all active servers.
     */
    protected function checkAllServers(
        ConnectionService $connectionService,
        ConnectionStatusService $statusService,
        ConnectionRetryService $retryService
    ): void {
        Log::info("Starting health check for all active servers");

        $sourceServers = SourceServer::where('is_active', true)->get();
        $backupServers = BackupServer::where('is_active', true)->get();

        $totalServers = $sourceServers->count() + $backupServers->count();
        $successCount = 0;
        $failureCount = 0;

        // Check source servers
        foreach ($sourceServers as $server) {
            try {
                $this->checkSingleServer($server, $connectionService, $statusService, $retryService);
                $successCount++;
            } catch (Exception $e) {
                $failureCount++;
                Log::error("Failed to check source server {$server->name}: " . $e->getMessage());
            }
        }

        // Check backup servers
        foreach ($backupServers as $server) {
            try {
                $this->checkSingleServer($server, $connectionService, $statusService, $retryService);
                $successCount++;
            } catch (Exception $e) {
                $failureCount++;
                Log::error("Failed to check backup server {$server->name}: " . $e->getMessage());
            }
        }

        Log::info("Health check completed: {$successCount} successful, {$failureCount} failed out of {$totalServers} total servers");
    }

    /**
     * Store health check result in server metadata.
     */
    protected function storeHealthCheckResult(
        SourceServer|BackupServer $server,
        bool $success,
        int $duration,
        int $retryCount,
        ?string $error = null
    ): void {
        $connectionOptions = $server->connection_options ?? [];

        $result = [
            'success' => $success,
            'duration_ms' => $duration,
            'retry_count' => $retryCount,
            'tested_at' => now()->toISOString(),
            'error' => $error,
            'type' => 'health_check'
        ];

        // Store latest result
        $connectionOptions['last_health_check'] = $result;

        // Keep history of last 20 health checks
        if (!isset($connectionOptions['health_check_history'])) {
            $connectionOptions['health_check_history'] = [];
        }

        $connectionOptions['health_check_history'][] = $result;
        $connectionOptions['health_check_history'] = array_slice(
            $connectionOptions['health_check_history'],
            -20
        );

        $server->update(['connection_options' => $connectionOptions]);
    }

    /**
     * Handle health check failure.
     */
    protected function handleHealthCheckFailure(
        SourceServer|BackupServer $server,
        array $retryHistory = [],
        ?string $error = null
    ): void {
        $serverType = $server instanceof SourceServer ? 'source' : 'backup';

        // This would typically send notifications, create alerts, etc.
        // For now, just log the failure
        Log::error("Health check failure alert for {$serverType} server: {$server->name}");

        if ($error) {
            Log::error("Error details: {$error}");
        }

        if (!empty($retryHistory)) {
            Log::error("Retry history: " . json_encode($retryHistory));
        }

        // TODO: Implement notification system
        // - Send email alerts
        // - Create dashboard notifications
        // - Update server status indicators
        // - Trigger escalation procedures
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        $serverInfo = $this->server ?
            ($this->server instanceof SourceServer ? 'source' : 'backup') . " server {$this->server->name}" :
            'all servers';

        Log::error("Health check job failed permanently for {$serverInfo}: " . $exception->getMessage());
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $tags = ['health-check'];

        if ($this->server) {
            $serverType = $this->server instanceof SourceServer ? 'source' : 'backup';
            $tags[] = "server:{$serverType}:{$this->server->id}";
        } else {
            $tags[] = 'all-servers';
        }

        return $tags;
    }
}
