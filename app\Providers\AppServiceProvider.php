<?php

namespace App\Providers;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register backup system services as singletons
        $this->app->singleton(\App\Services\BackupService::class);
        $this->app->singleton(\App\Services\ConnectionService::class);
        $this->app->singleton(\App\Services\CompressionService::class);
        $this->app->singleton(\App\Services\RetentionService::class);
        $this->app->singleton(\App\Services\ScheduleService::class);

        // Register connection factory as singleton
        $this->app->singleton(\App\Services\Connections\ConnectionFactory::class);

        // Register connection testing services as singletons
        $this->app->singleton(\App\Services\ConnectionStatusService::class);
        $this->app->singleton(\App\Services\ConnectionRetryService::class);
        $this->app->singleton(\App\Services\ConnectionDiagnosticsService::class);

        // Register Phase 3 backup logic services as singletons
        $this->app->singleton(\App\Services\FileDiscoveryService::class);
        $this->app->singleton(\App\Services\BackupTransferService::class);
        $this->app->singleton(\App\Services\IncrementalBackupService::class);
        $this->app->singleton(\App\Services\IntegrityVerificationService::class);
        $this->app->singleton(\App\Services\BackupValidationService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Force HTTPS in production environment to prevent mixed content issues
        if (app()->environment('production')) {
            \URL::forceScheme('https');
        }
    }
}
