# Task 1.3: Create Job Classes ✅ COMPLETED

## Overview
Create Laravel queue job classes to handle background processing of backup operations, retention cleanup, and scheduled backup dispatching. These jobs will run asynchronously to prevent blocking the web interface.

## Subtasks

### Subtask 1.3.1: Create ExecuteBackupJob

**Description:** Main job class that executes individual backup operations in the background.

**Implementation Steps:**
1. Create the job class:
   ```bash
   php artisan make:job ExecuteBackupJob
   ```

2. Implement the job with these features:
   - Accept `BackupJob` model in constructor
   - Handle backup execution with progress tracking
   - Update backup log with status and results
   - Handle failures and retries
   - Implement timeout handling

**Manual Testing:**
- Dispatch the job manually:
  ```php
  php artisan tinker
  $job = App\Models\BackupJob::first();
  App\Jobs\ExecuteBackupJob::dispatch($job);
  ```
- Check job was queued: `php artisan queue:work --once`
- Monitor job execution in backup logs UI

### Subtask 1.3.2: Create RetentionCleanupJob

**Description:** Background job that handles cleanup of old backups based on retention policies.

**Implementation Steps:**
1. Create the job class:
   ```bash
   php artisan make:job RetentionCleanupJob
   ```

2. Implement features:
   - Accept `BackupJob` or `BackupServer` in constructor
   - Apply retention policies
   - Safe deletion with verification
   - Log cleanup operations
   - Handle cleanup failures gracefully

**Manual Testing:**
- Create old backup logs in database
- Dispatch cleanup job:
  ```php
  php artisan tinker
  $job = App\Models\BackupJob::first();
  App\Jobs\RetentionCleanupJob::dispatch($job);
  ```
- Verify old backups are marked for deletion
- Check cleanup logs

### Subtask 1.3.3: Create ScheduledBackupDispatcher

**Description:** Job that checks for due backup jobs and dispatches them for execution.

**Implementation Steps:**
1. Create the job class:
   ```bash
   php artisan make:job ScheduledBackupDispatcher
   ```

2. Implement features:
   - Check all active backup jobs
   - Identify jobs due for execution
   - Dispatch ExecuteBackupJob for due jobs
   - Update next_run timestamps
   - Handle scheduling conflicts

**Manual Testing:**
- Set a backup job to be due now:
  ```php
  php artisan tinker
  $job = App\Models\BackupJob::first();
  $job->update(['next_run' => now()->subMinute()]);
  App\Jobs\ScheduledBackupDispatcher::dispatch();
  ```
- Check that ExecuteBackupJob was dispatched
- Verify next_run was updated

### Subtask 1.3.4: Create ConnectionTestJob

**Description:** Background job for testing server connections without blocking the UI.

**Implementation Steps:**
1. Create the job class:
   ```bash
   php artisan make:job ConnectionTestJob
   ```

2. Implement features:
   - Test connection to source or backup server
   - Update server's last_connection_test timestamp
   - Store connection test results
   - Handle connection timeouts

**Manual Testing:**
- Test server connection:
  ```php
  php artisan tinker
  $server = App\Models\SourceServer::first();
  App\Jobs\ConnectionTestJob::dispatch($server);
  ```
- Check server's last_connection_test was updated
- Verify connection status in UI

## Job Configuration

### Subtask 1.3.5: Configure Job Queues and Priorities

**Implementation Steps:**
1. Update `config/queue.php` to add backup-specific queues:
   ```php
   'connections' => [
       'database' => [
           // ... existing config
           'queue' => env('DB_QUEUE', 'default'),
       ],
   ],
   ```

2. Create queue configuration for different job types:
   - `backup-execution` - High priority for backup jobs
   - `maintenance` - Lower priority for cleanup jobs
   - `testing` - Medium priority for connection tests

**Manual Testing:**
- Dispatch jobs to different queues:
  ```php
  php artisan tinker
  App\Jobs\ExecuteBackupJob::dispatch($job)->onQueue('backup-execution');
  App\Jobs\RetentionCleanupJob::dispatch($job)->onQueue('maintenance');
  ```
- Run queue workers for specific queues:
  ```bash
  php artisan queue:work --queue=backup-execution
  php artisan queue:work --queue=maintenance
  ```

### Subtask 1.3.6: Implement Job Middleware

**Implementation Steps:**
1. Create rate limiting middleware for backup jobs:
   ```bash
   php artisan make:middleware RateLimitBackupJobs
   ```

2. Create job failure handling:
   - Automatic retry logic
   - Exponential backoff
   - Maximum retry limits
   - Failure notifications

**Manual Testing:**
- Test rate limiting by dispatching multiple jobs quickly
- Force a job to fail and verify retry beh   avior:
  ```php
  php artisan tinker
  // Dispatch job with invalid server
  $job = App\Models\BackupJob::first();
  $job->source_server_id = 999; // Non-existent server
  App\Jobs\ExecuteBackupJob::dispatch($job);
  ```

## Queue Worker Configuration

### Subtask 1.3.7: Create Queue Worker Scripts

**Implementation Steps:**
1. Create supervisor configuration for queue workers
2. Create systemd service files for production
3. Add queue monitoring commands

**Manual Testing:**
- Start queue worker: `php artisan queue:work --tries=3 --timeout=3600`
- Monitor queue status: `php artisan queue:monitor`
- Check failed jobs: `php artisan queue:failed`

## Verification Checklist

After completing all subtasks, verify:

- [ ] All job classes are created and properly structured
- [ ] Jobs can be dispatched without errors
- [ ] Queue workers can process jobs successfully
- [ ] Job failures are handled gracefully
- [ ] Different queue priorities work correctly
- [ ] Job progress can be tracked
- [ ] Failed jobs can be retried

## Expected Files Created

- `app/Jobs/ExecuteBackupJob.php`
- `app/Jobs/RetentionCleanupJob.php`
- `app/Jobs/ScheduledBackupDispatcher.php`
- `app/Jobs/ConnectionTestJob.php`
- `app/Http/Middleware/RateLimitBackupJobs.php`

## Expected Files Modified

- `config/queue.php` - Queue configuration updates
- Database - Job tables populated with queued jobs

## Job Architecture Benefits

1. **Asynchronous Processing:** Long-running backups don't block the web interface
2. **Reliability:** Jobs can be retried on failure
3. **Scalability:** Multiple workers can process jobs in parallel
4. **Monitoring:** Job status and progress can be tracked
5. **Resource Management:** Jobs can be prioritized and rate-limited

## Troubleshooting

**Common Issues:**
1. **Jobs not processing:** Check queue worker is running
2. **Memory issues:** Increase PHP memory limit for workers
3. **Timeout errors:** Adjust job timeout settings
4. **Database locks:** Use proper transaction handling

## Next Steps

After completing this task, proceed to [Phase 2: Connection Management](../phase-2/task-2.1-connection-handlers.md).

---

## ✅ TASK COMPLETED

**Completion Date:** 2025-06-14
**Status:** All job classes successfully created and tested

**Created Job Classes:**
- ✅ `ExecuteBackupJob` - Main backup execution job with retry logic and timeout handling
- ✅ `RetentionCleanupJob` - Cleanup old backups based on retention policies
- ✅ `ScheduledBackupDispatcher` - Dispatch due backup jobs automatically
- ✅ `ConnectionTestJob` - Test server connections asynchronously
- ✅ `RateLimitBackupJobs` - Middleware for rate limiting backup job dispatch

**Queue Configuration:**
- ✅ Multiple queue support (backup-execution, maintenance, testing, default)
- ✅ Proper job prioritization and timeout settings
- ✅ Retry logic with exponential backoff
- ✅ Job tagging for monitoring and debugging

**Manual Testing Results:**
- ✅ All job classes instantiate without errors
- ✅ Jobs can be dispatched to appropriate queues
- ✅ Queue workers process jobs successfully
- ✅ ConnectionTestJob updates server timestamps correctly
- ✅ Failed job handling works properly
- ✅ Rate limiting middleware functions correctly

**Architecture Benefits Achieved:**
- ✅ Asynchronous processing prevents UI blocking
- ✅ Reliable job execution with retry mechanisms
- ✅ Scalable queue-based architecture
- ✅ Comprehensive job monitoring and logging
- ✅ Resource management through rate limiting

**Queue Commands for Production:**
```bash
# Process all queues
php artisan queue:work --tries=3 --timeout=3600

# Process specific queues with priority
php artisan queue:work --queue=backup-execution,maintenance,testing,default

# Monitor queue status
php artisan queue:monitor

# View failed jobs
php artisan queue:failed
```
