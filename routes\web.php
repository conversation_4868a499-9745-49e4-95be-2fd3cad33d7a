<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Backup Dashboard
    Route::get('backup-dashboard', function () {
        return Inertia::render('backup-dashboard');
    })->name('backup-dashboard');

    // Users CRUD routes
    Route::resource('users', \App\Http\Controllers\UserController::class);

    // Backup Module Routes

    // Source Servers
    Route::resource('source-servers', \App\Http\Controllers\SourceServerController::class);
    Route::post('source-servers/{sourceServer}/test-connection', [\App\Http\Controllers\SourceServerController::class, 'testConnection'])
        ->name('source-servers.test-connection');

    // Debug route for delete
    Route::post('source-servers/{sourceServer}/delete', function(\App\Models\SourceServer $sourceServer) {
        \Log::info('Debug delete route called for server: ' . $sourceServer->id);

        // Check backup jobs
        $activeJobs = $sourceServer->backupJobs()->where('status', 'active')->count();
        $allJobs = $sourceServer->backupJobs()->count();

        \Log::info("Server {$sourceServer->id} has {$activeJobs} active jobs and {$allJobs} total jobs");

        // Check if server has active backup jobs
        if ($activeJobs > 0) {
            \Log::info('Cannot delete - has active backup jobs');
            return redirect()->route('source-servers.index')
                ->with('error', "Cannot delete source server '{$sourceServer->name}' - it has {$activeJobs} active backup jobs. Please disable or delete the backup jobs first.");
        }

        try {
            \Log::info('Attempting to delete server: ' . $sourceServer->id);
            $serverName = $sourceServer->name;
            $sourceServer->delete();
            \Log::info('Server deleted successfully: ' . $sourceServer->id);

            return redirect()->route('source-servers.index')
                ->with('success', "Source server '{$serverName}' deleted successfully.");
        } catch (\Exception $e) {
            \Log::error('Failed to delete source server: ' . $e->getMessage());
            return redirect()->route('source-servers.index')
                ->with('error', 'Failed to delete source server: ' . $e->getMessage());
        }
    })->name('source-servers.delete-debug');

    // Force delete route (deletes backup jobs first)
    Route::post('source-servers/{sourceServer}/force-delete', function(\App\Models\SourceServer $sourceServer) {
        \Log::info('Force delete route called for server: ' . $sourceServer->id);

        try {
            $serverName = $sourceServer->name;

            // Delete all backup jobs first
            $jobsDeleted = $sourceServer->backupJobs()->count();
            $sourceServer->backupJobs()->delete();
            \Log::info("Deleted {$jobsDeleted} backup jobs for server {$sourceServer->id}");

            // Now delete the server
            $sourceServer->delete();
            \Log::info('Server force deleted successfully: ' . $sourceServer->id);

            return redirect()->route('source-servers.index')
                ->with('success', "Source server '{$serverName}' and {$jobsDeleted} backup jobs deleted successfully.");
        } catch (\Exception $e) {
            \Log::error('Failed to force delete source server: ' . $e->getMessage());
            return redirect()->route('source-servers.index')
                ->with('error', 'Failed to delete source server: ' . $e->getMessage());
        }
    })->name('source-servers.force-delete');

    // Backup Servers
    Route::resource('backup-servers', \App\Http\Controllers\BackupServerController::class);
    Route::post('backup-servers/{backupServer}/test-connection', [\App\Http\Controllers\BackupServerController::class, 'testConnection'])
        ->name('backup-servers.test-connection');

    // Backup Jobs
    Route::resource('backup-jobs', \App\Http\Controllers\BackupJobController::class);
    Route::post('backup-jobs/{backupJob}/trigger', [\App\Http\Controllers\BackupJobController::class, 'trigger'])
        ->name('backup-jobs.trigger');

    // Backup Logs
    Route::resource('backup-logs', \App\Http\Controllers\BackupLogController::class)
        ->except(['create', 'store', 'edit', 'update']);
    Route::post('backup-logs/{backupLog}/cancel', [\App\Http\Controllers\BackupLogController::class, 'cancel'])
        ->name('backup-logs.cancel');
    Route::get('backup-logs/{backupLog}/download', [\App\Http\Controllers\BackupLogController::class, 'download'])
        ->name('backup-logs.download');
    Route::post('backup-logs/{backupLog}/retry', [\App\Http\Controllers\BackupLogController::class, 'retry'])
        ->name('backup-logs.retry');
    Route::delete('backup-logs/bulk-delete', [\App\Http\Controllers\BackupLogController::class, 'bulkDelete'])
        ->name('backup-logs.bulk-delete');
    Route::get('backup-statistics', [\App\Http\Controllers\BackupLogController::class, 'statistics'])
        ->name('backup-logs.statistics');

    // Connection Testing Routes (API endpoints for AJAX)
    Route::prefix('api/connection-test')->name('api.connection-test.')->group(function () {
        Route::post('source-servers/{sourceServer}', [\App\Http\Controllers\ConnectionTestController::class, 'testSourceServer'])
            ->name('source-server');
        Route::post('backup-servers/{backupServer}', [\App\Http\Controllers\ConnectionTestController::class, 'testBackupServer'])
            ->name('backup-server');
        Route::get('status/{testId}', [\App\Http\Controllers\ConnectionTestController::class, 'getStatus'])
            ->name('status');
        Route::post('cancel/{testId}', [\App\Http\Controllers\ConnectionTestController::class, 'cancelTest'])
            ->name('cancel');
        Route::get('health', [\App\Http\Controllers\ConnectionTestController::class, 'getServerHealth'])
            ->name('health');
        Route::post('diagnostics', [\App\Http\Controllers\ConnectionTestController::class, 'runDiagnostics'])
            ->name('diagnostics');
        Route::get('summary', [\App\Http\Controllers\ConnectionTestController::class, 'getSummary'])
            ->name('summary');
        Route::get('history', [\App\Http\Controllers\ConnectionTestController::class, 'getTestHistory'])
            ->name('history');
    });

    // Legacy connection testing routes (for backward compatibility)
    Route::prefix('connection-test')->name('connection-test.')->group(function () {
        Route::get('status/{testId}', [\App\Http\Controllers\ConnectionTestController::class, 'getStatus'])
            ->name('status');
        Route::post('cancel/{testId}', [\App\Http\Controllers\ConnectionTestController::class, 'cancelTest'])
            ->name('cancel');
        Route::get('health', [\App\Http\Controllers\ConnectionTestController::class, 'getServerHealth'])
            ->name('health');
        Route::post('diagnostics', [\App\Http\Controllers\ConnectionTestController::class, 'runDiagnostics'])
            ->name('diagnostics');
        Route::get('summary', [\App\Http\Controllers\ConnectionTestController::class, 'getSummary'])
            ->name('summary');
        Route::get('history', [\App\Http\Controllers\ConnectionTestController::class, 'getTestHistory'])
            ->name('history');
    });

    // Actions CRUD
    Route::resource('actions', \App\Http\Controllers\ActionController::class);
    Route::post('actions/{action}/run', [\App\Http\Controllers\ActionController::class, 'run'])
        ->name('actions.run')
        ->withoutMiddleware([\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class]);
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
