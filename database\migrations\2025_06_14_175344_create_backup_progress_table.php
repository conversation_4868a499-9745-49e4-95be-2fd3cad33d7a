<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('backup_log_id')->constrained('backup_logs')->onDelete('cascade');
            $table->string('stage'); // discovery, transfer, compression, encryption, finalization
            $table->decimal('percentage', 5, 2)->default(0); // 0.00 to 100.00
            $table->text('message')->nullable();
            $table->json('stage_data')->nullable(); // Additional stage-specific data
            $table->integer('files_processed')->default(0);
            $table->integer('total_files')->default(0);
            $table->bigInteger('bytes_processed')->default(0);
            $table->bigInteger('total_bytes')->default(0);
            $table->decimal('transfer_speed_mbps', 10, 2)->nullable(); // MB/s
            $table->timestamp('stage_started_at')->nullable();
            $table->timestamp('stage_completed_at')->nullable();
            $table->boolean('is_current_stage')->default(false);
            $table->timestamps();

            // Indexes for performance
            $table->index(['backup_log_id', 'is_current_stage']);
            $table->index(['backup_log_id', 'stage']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_progress');
    }
};
