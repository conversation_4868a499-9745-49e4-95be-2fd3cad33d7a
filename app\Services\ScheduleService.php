<?php

namespace App\Services;

use App\Models\BackupJob;
use Carbon\Carbon;
use Cron\CronExpression;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Exception;

class ScheduleService
{
    /**
     * Calculate the next run time for a CRON expression.
     */
    public function calculateNextRun(string $cronExpression): Carbon
    {
        try {
            $cron = new CronExpression($cronExpression);
            return Carbon::instance($cron->getNextRunDate());
        } catch (Exception $e) {
            Log::error("Failed to calculate next run for CRON expression '{$cronExpression}': " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Validate a CRON expression.
     */
    public function validateCronExpression(string $expression): bool
    {
        try {
            $cron = new CronExpression($expression);
            // Try to get the next run date to validate the expression
            $cron->getNextRunDate();
            return true;
        } catch (Exception $e) {
            Log::debug("Invalid CRON expression '{$expression}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all backup jobs that are due for execution.
     */
    public function getJobsDueForExecution(): Collection
    {
        $now = Carbon::now();
        
        return BackupJob::where('status', 'active')
            ->where(function ($query) use ($now) {
                $query->whereNull('next_run')
                    ->orWhere('next_run', '<=', $now);
            })
            ->get()
            ->filter(function ($job) {
                // Additional validation to ensure the job is truly due
                return $this->isJobDue($job);
            });
    }

    /**
     * Update the schedule for a backup job.
     */
    public function updateJobSchedule(BackupJob $job): void
    {
        try {
            if (!$job->schedule || !$this->validateCronExpression($job->schedule)) {
                Log::warning("Invalid or missing schedule for job {$job->name}");
                return;
            }

            $nextRun = $this->calculateNextRun($job->schedule);
            
            $job->update([
                'next_run' => $nextRun,
            ]);

            Log::info("Updated schedule for job {$job->name}: next run at {$nextRun->format('Y-m-d H:i:s')}");

        } catch (Exception $e) {
            Log::error("Failed to update schedule for job {$job->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if a backup job is due for execution.
     */
    public function isJobDue(BackupJob $job): bool
    {
        if (!$job->isActive()) {
            return false;
        }

        if (!$job->schedule || !$this->validateCronExpression($job->schedule)) {
            return false;
        }

        $now = Carbon::now();
        
        // If next_run is null or in the past, the job is due
        if (!$job->next_run || $job->next_run <= $now) {
            return true;
        }

        return false;
    }

    /**
     * Get the previous run time for a CRON expression.
     */
    public function calculatePreviousRun(string $cronExpression): Carbon
    {
        try {
            $cron = new CronExpression($cronExpression);
            return Carbon::instance($cron->getPreviousRunDate());
        } catch (Exception $e) {
            Log::error("Failed to calculate previous run for CRON expression '{$cronExpression}': " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get multiple next run dates for a CRON expression.
     */
    public function getNextRunDates(string $cronExpression, int $count = 5): array
    {
        try {
            $cron = new CronExpression($cronExpression);
            $dates = [];
            $currentDate = new \DateTime();

            for ($i = 0; $i < $count; $i++) {
                $nextDate = $cron->getNextRunDate($currentDate);
                $dates[] = Carbon::instance($nextDate);
                $currentDate = $nextDate;
            }

            return $dates;
        } catch (Exception $e) {
            Log::error("Failed to get next run dates for CRON expression '{$cronExpression}': " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get human-readable description of a CRON expression.
     */
    public function describeCronExpression(string $cronExpression): string
    {
        try {
            // Basic CRON expression descriptions
            $descriptions = [
                '0 0 * * *' => 'Daily at midnight',
                '0 2 * * *' => 'Daily at 2:00 AM',
                '0 0 * * 0' => 'Weekly on Sunday at midnight',
                '0 0 1 * *' => 'Monthly on the 1st at midnight',
                '0 0 1 1 *' => 'Yearly on January 1st at midnight',
                '*/15 * * * *' => 'Every 15 minutes',
                '0 */6 * * *' => 'Every 6 hours',
                '0 9-17 * * 1-5' => 'Every hour from 9 AM to 5 PM, Monday to Friday',
            ];

            if (isset($descriptions[$cronExpression])) {
                return $descriptions[$cronExpression];
            }

            // Parse the expression parts
            $parts = explode(' ', $cronExpression);
            if (count($parts) !== 5) {
                return 'Invalid CRON expression';
            }

            [$minute, $hour, $day, $month, $dayOfWeek] = $parts;

            $description = 'Runs ';

            // Frequency
            if ($minute === '*' && $hour === '*') {
                $description .= 'every minute';
            } elseif ($minute !== '*' && $hour === '*') {
                $description .= "at minute {$minute} of every hour";
            } elseif ($minute === '0' && $hour !== '*') {
                $description .= "daily at {$hour}:00";
            } else {
                $description .= "at {$hour}:{$minute}";
            }

            // Day constraints
            if ($day !== '*') {
                $description .= " on day {$day} of the month";
            }

            if ($month !== '*') {
                $description .= " in month {$month}";
            }

            if ($dayOfWeek !== '*') {
                $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                if (is_numeric($dayOfWeek) && isset($days[$dayOfWeek])) {
                    $description .= " on {$days[$dayOfWeek]}";
                } else {
                    $description .= " on day of week {$dayOfWeek}";
                }
            }

            return $description;

        } catch (Exception $e) {
            return 'Unable to describe CRON expression';
        }
    }

    /**
     * Update all active job schedules.
     */
    public function updateAllJobSchedules(): int
    {
        $jobs = BackupJob::where('status', 'active')->get();
        $updatedCount = 0;

        foreach ($jobs as $job) {
            try {
                $this->updateJobSchedule($job);
                $updatedCount++;
            } catch (Exception $e) {
                Log::error("Failed to update schedule for job {$job->name}: " . $e->getMessage());
            }
        }

        Log::info("Updated schedules for {$updatedCount} backup jobs");
        return $updatedCount;
    }

    /**
     * Get schedule statistics.
     */
    public function getScheduleStatistics(): array
    {
        $activeJobs = BackupJob::where('status', 'active')->get();
        $now = Carbon::now();
        
        $dueNow = 0;
        $dueToday = 0;
        $dueThisWeek = 0;
        $scheduleTypes = [];

        foreach ($activeJobs as $job) {
            if ($this->isJobDue($job)) {
                $dueNow++;
            }

            if ($job->next_run && $job->next_run->isToday()) {
                $dueToday++;
            }

            if ($job->next_run && $job->next_run->isBetween($now, $now->copy()->addWeek())) {
                $dueThisWeek++;
            }

            // Categorize schedule types
            if ($job->schedule) {
                $description = $this->describeCronExpression($job->schedule);
                $scheduleTypes[$description] = ($scheduleTypes[$description] ?? 0) + 1;
            }
        }

        return [
            'total_active_jobs' => $activeJobs->count(),
            'due_now' => $dueNow,
            'due_today' => $dueToday,
            'due_this_week' => $dueThisWeek,
            'schedule_types' => $scheduleTypes,
            'calculated_at' => $now->toISOString(),
        ];
    }
}
