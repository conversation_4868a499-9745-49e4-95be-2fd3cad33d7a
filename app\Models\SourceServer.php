<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SourceServer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'ip_address',
        'port',
        'username',
        'authentication_method',
        'password',
        'private_key',
        'description',
        'is_active',
        'last_connection_test',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_connection_test' => 'datetime',
        'password' => 'encrypted',
        'private_key' => 'encrypted',
    ];

    protected $hidden = [
        'password',
        'private_key',
    ];

    /**
     * Get the backup jobs for this source server.
     */
    public function backupJobs(): HasMany
    {
        return $this->hasMany(BackupJob::class);
    }

    /**
     * Get the default port based on authentication method.
     */
    public function getDefaultPort(): int
    {
        return 22; // SSH default port
    }

    /**
     * Check if the server uses password authentication.
     */
    public function usesPasswordAuth(): bool
    {
        return $this->authentication_method === 'password';
    }

    /**
     * Check if the server uses private key authentication.
     */
    public function usesPrivateKeyAuth(): bool
    {
        return $this->authentication_method === 'private_key';
    }

    /**
     * Get the connection string for display.
     */
    public function getConnectionStringAttribute(): string
    {
        return "{$this->username}@{$this->ip_address}:{$this->port}";
    }
}
