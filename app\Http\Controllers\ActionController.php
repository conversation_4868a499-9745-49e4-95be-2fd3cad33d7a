<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Action;
use App\Models\SourceServer;
use Inertia\Inertia;
use Spatie\Ssh\Ssh;

class ActionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $actions = Action::with(['sourceServer', 'creator'])->get();
        return Inertia::render('actions/index', [
            'actions' => $actions,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $sourceServers = SourceServer::select('id', 'name')->get();
        $templates = [
            [
                'label' => 'Deploy App',
                'value' => 'deploy',
                'command' => 'cd /var/www/app && git pull origin main && composer install --no-interaction && php artisan migrate --force',
            ],
            [
                'label' => 'Build Frontend',
                'value' => 'build',
                'command' => 'cd /var/www/app && npm install && npm run build',
            ],
            [
                'label' => 'Restart Services',
                'value' => 'restart',
                'command' => 'sudo systemctl restart nginx && sudo systemctl restart php-fpm',
            ],
        ];
        return Inertia::render('actions/create', [
            'sourceServers' => $sourceServers,
            'templates' => $templates,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'source_server_id' => 'required|exists:source_servers,id',
            'command' => 'required|string',
            'template' => 'nullable|string|max:255',
        ]);

        $validated['created_by'] = auth()->id();

        Action::create($validated);

        return redirect()->route('actions.index')->with('success', 'Action created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $action = Action::with('sourceServer')->findOrFail($id);
        $sourceServers = SourceServer::select('id', 'name')->get();
        $templates = [
            [
                'label' => 'Deploy App',
                'value' => 'deploy',
                'command' => 'cd /var/www/app && git pull origin main && composer install --no-interaction && php artisan migrate --force',
            ],
            [
                'label' => 'Build Frontend',
                'value' => 'build',
                'command' => 'cd /var/www/app && npm install && npm run build',
            ],
            [
                'label' => 'Restart Services',
                'value' => 'restart',
                'command' => 'sudo systemctl restart nginx && sudo systemctl restart php-fpm',
            ],
        ];
        return Inertia::render('actions/edit', [
            'action' => $action,
            'sourceServers' => $sourceServers,
            'templates' => $templates,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $action = Action::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'source_server_id' => 'required|exists:source_servers,id',
            'command' => 'required|string',
            'template' => 'nullable|string|max:255',
        ]);

        $action->update($validated);

        return redirect()->route('actions.index')->with('success', 'Action updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $action = Action::findOrFail($id);
        $action->delete();
        return redirect()->route('actions.index')->with('success', 'Action deleted successfully.');
    }

    /**
     * Execute the action on the source server via SSH.
     */
    public function run(Action $action)
    {
        // Debug logging
        \Log::info('Action run method called', [
            'action_id' => $action->id,
            'user_id' => auth()->id(),
            'has_source_server' => $action->sourceServer !== null,
        ]);

        // Load the source server relationship
        $action->load('sourceServer');

        if (!$action->sourceServer) {
            \Log::error('Source server not found for action', ['action_id' => $action->id]);
            return response()->json(['error' => 'Source server not found'], 404);
        }

        // Stream the response with proper headers
        return response()->stream(function () use ($action) {
            // Set headers for streaming
            header('Content-Type: text/plain');
            header('Cache-Control: no-cache');
            header('Connection: keep-alive');
            header('X-Accel-Buffering: no'); // Disable nginx buffering
            
            echo "data: " . json_encode([
                'type' => 'info',
                'message' => "Starting execution on server: {$action->sourceServer->name}"
            ]) . "\n\n";
            
            echo "data: " . json_encode([
                'type' => 'info',
                'message' => "Command: {$action->command}"
            ]) . "\n\n";
            
            echo "data: " . json_encode([
                'type' => 'info',
                'message' => "--- Execution Output ---"
            ]) . "\n\n";
            
            if (ob_get_level()) {
                ob_end_flush();
            }
            flush();

            try {
                // Check configuration for testing mode
                if (config('app.debug') && request()->has('simulate')) {
                    // Force simulation mode for testing
                    $this->simulateExecution($action);
                } else {
                    // Use Spatie SSH package - works on all systems
                    $this->executeViaSpatieSSH($action);
                }

                // Success message is handled within the SSH execution methods

            } catch (\Exception $e) {
                // Error message is already handled within the SSH execution methods
                // This is just a fallback in case of unexpected errors
            }
        }, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
        ]);
    }

    private function executeViaSpatieSSH($action)
    {
        $server = $action->sourceServer;
        
        echo "data: " . json_encode([
            'type' => 'info',
            'message' => "Connecting to {$server->ip_address}:{$server->port} as {$server->username}..."
        ]) . "\n\n";
        flush();

        try {
            // Debug: Show all server details
            echo "data: " . json_encode([
                'type' => 'info',
                'message' => "Server ID: {$server->id}, Name: {$server->name}"
            ]) . "\n\n";
            flush();

            echo "data: " . json_encode([
                'type' => 'info',
                'message' => "IP: '{$server->ip_address}', Port: {$server->port}, Username: '{$server->username}'"
            ]) . "\n\n";
            flush();

            echo "data: " . json_encode([
                'type' => 'info',
                'message' => "Auth method: {$server->authentication_method}, Has password: " . ($server->password ? 'yes' : 'no')
            ]) . "\n\n";
            flush();

            // Create SSH connection using Spatie package
            $ssh = Ssh::create($server->username, $server->ip_address);

            // Configure port
            if ($server->port && $server->port != 22) {
                $ssh->usePort($server->port);
            }

            // Track temporary file for cleanup
            $tempKeyFile = null;

            // Configure authentication
            if ($server->authentication_method === 'password' && $server->password) {
                echo "data: " . json_encode([
                    'type' => 'info',
                    'message' => 'Using password authentication'
                ]) . "\n\n";
                flush();
                $ssh->usePassword($server->password); // Model handles decryption automatically
            } elseif ($server->authentication_method === 'private_key' && $server->private_key) {
                echo "data: " . json_encode([
                    'type' => 'info',
                    'message' => 'Using private key authentication'
                ]) . "\n\n";
                flush();
                // For private key, we need to write it to a temporary file
                $tempKeyFile = tempnam(sys_get_temp_dir(), 'ssh_key_');
                file_put_contents($tempKeyFile, $server->private_key);
                chmod($tempKeyFile, 0600);
                $ssh->usePrivateKey($tempKeyFile);
            } else {
                throw new \Exception('No valid authentication method configured (method: ' . $server->authentication_method . ')');
            }

            // Disable strict host key checking for easier setup
            $ssh->disableStrictHostKeyChecking();

            // Set timeout for connection
            $ssh->setTimeout(30);

            // Test the connection first - this will throw an exception if connection fails
            echo "data: " . json_encode([
                'type' => 'info',
                'message' => 'Testing SSH connection...'
            ]) . "\n\n";
            flush();

            $testProcess = $ssh->execute('echo "Connection test successful"');
            
            // Check if the test command was successful
            if (!$testProcess->isSuccessful()) {
                $errorOutput = trim($testProcess->getErrorOutput());
                if (empty($errorOutput)) {
                    $errorOutput = trim($testProcess->getOutput());
                }
                if (empty($errorOutput)) {
                    $errorOutput = 'Unknown SSH connection error (exit code: ' . $testProcess->getExitCode() . ')';
                }
                
                throw new \Exception('SSH connection failed: ' . $errorOutput);
            }

            // Connection successful
            echo "data: " . json_encode([
                'type' => 'success',
                'message' => 'SSH connection established successfully!'
            ]) . "\n\n";
            flush();

            echo "data: " . json_encode([
                'type' => 'info',
                'message' => 'Executing command: ' . $action->command
            ]) . "\n\n";
            flush();

            // Execute the actual command with real-time output
            $output = '';
            $ssh->onOutput(function($type, $line) use (&$output) {
                $output .= $line;
                if (trim($line)) {
                    $outputType = ($type === \Symfony\Component\Process\Process::ERR) ? 'error' : 'output';
                    echo "data: " . json_encode([
                        'type' => $outputType,
                        'message' => rtrim($line)
                    ]) . "\n\n";
                    flush();
                }
            });

            $process = $ssh->execute($action->command);

            if ($process->isSuccessful()) {
                echo "data: " . json_encode([
                    'type' => 'success',
                    'message' => 'Command executed successfully (exit code: 0)'
                ]) . "\n\n";
                flush();
                
                // Log successful execution
                $action->logs()->create([
                    'user_id' => auth()->id(),
                    'status' => 'completed',
                    'output' => $output ?: $process->getOutput(),
                    'started_at' => now()->subSeconds(10),
                    'finished_at' => now(),
                ]);
            } else {
                $errorOutput = trim($process->getErrorOutput());
                if (empty($errorOutput)) {
                    $errorOutput = 'Command failed with exit code: ' . $process->getExitCode();
                }
                
                echo "data: " . json_encode([
                    'type' => 'error',
                    'message' => $errorOutput
                ]) . "\n\n";
                flush();
                
                // Log failed execution
                $action->logs()->create([
                    'user_id' => auth()->id(),
                    'status' => 'failed',
                    'output' => $output ?: $process->getOutput(),
                    'started_at' => now()->subSeconds(10),
                    'finished_at' => now(),
                ]);
                
                throw new \Exception($errorOutput);
            }

        } catch (\Exception $e) {
            // This will catch SSH connection errors, authentication failures, etc.
            echo "data: " . json_encode([
                'type' => 'error',
                'message' => 'Error: ' . $e->getMessage()
            ]) . "\n\n";
            flush();
            
            // Log the failure
            $action->logs()->create([
                'user_id' => auth()->id(),
                'status' => 'failed',
                'output' => 'Error: ' . $e->getMessage(),
                'started_at' => now()->subSeconds(2),
                'finished_at' => now(),
            ]);
            
            throw $e;
        } finally {
            // Clean up temporary key file if it was created
            if (isset($tempKeyFile) && $tempKeyFile && file_exists($tempKeyFile)) {
                unlink($tempKeyFile);
            }
        }
    }

    private function simulateExecution($action)
    {
        // Simulate command execution for demo purposes
        echo "data: " . json_encode([
            'type' => 'info',
            'message' => '[SIMULATION MODE - Testing purposes only]'
        ]) . "\n\n";
        flush();

        // Simulate some output based on the command
        $outputs = [
            'Reading package lists...',
            'Building dependency tree...',
            'Reading state information...',
        ];

        if (str_contains($action->command, 'git')) {
            $outputs = [
                'Fetching origin',
                'From https://github.com/user/repo',
                'Already up to date.',
            ];
        } elseif (str_contains($action->command, 'npm')) {
            $outputs = [
                'npm install started...',
                'Installing dependencies...',
                'Build successful!',
            ];
        } elseif (str_contains($action->command, 'systemctl')) {
            $outputs = [
                'Stopping service...',
                'Starting service...',
                'Service restarted successfully.',
            ];
        }

        foreach ($outputs as $output) {
            sleep(1);
            echo "data: " . json_encode([
                'type' => 'output',
                'message' => $output
            ]) . "\n\n";
            flush();
        }

        echo "data: " . json_encode([
            'type' => 'success',
            'message' => 'Command completed successfully (simulated)'
        ]) . "\n\n";
        flush();
    }
}
