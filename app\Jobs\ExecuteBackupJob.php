<?php

namespace App\Jobs;

use App\Models\BackupJob;
use App\Models\BackupLog;
use App\Services\BackupService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ExecuteBackupJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 3600; // 1 hour

    /**
     * The backup job to execute.
     */
    public BackupJob $backupJob;

    /**
     * Create a new job instance.
     */
    public function __construct(BackupJob $backupJob)
    {
        $this->backupJob = $backupJob;
        $this->onQueue('backup-execution');
    }

    /**
     * Execute the job.
     */
    public function handle(BackupService $backupService): void
    {
        Log::info("Starting backup execution for job: {$this->backupJob->name}");

        try {
            // Update the job's last_run timestamp
            $this->backupJob->update(['last_run' => now()]);

            // Execute the backup
            $backupLog = $backupService->executeBackup($this->backupJob);

            // Update the job's next_run timestamp
            $nextRun = $this->backupJob->calculateNextRun();
            if ($nextRun) {
                $this->backupJob->update(['next_run' => $nextRun]);
            }

            Log::info("Backup execution completed successfully for job: {$this->backupJob->name}");

        } catch (Exception $e) {
            Log::error("Backup execution failed for job {$this->backupJob->name}: " . $e->getMessage());

            // If this is the final attempt, mark the job as failed
            if ($this->attempts() >= $this->tries) {
                Log::error("Backup job {$this->backupJob->name} failed after {$this->tries} attempts");
            }

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("Backup job {$this->backupJob->name} failed permanently: " . $exception->getMessage());

        // Create a failed backup log entry if one doesn't exist
        $existingLog = BackupLog::where('backup_job_id', $this->backupJob->id)
            ->where('started_at', '>=', now()->subHour())
            ->first();

        if (!$existingLog) {
            BackupLog::create([
                'backup_job_id' => $this->backupJob->id,
                'status' => 'failed',
                'started_at' => now(),
                'completed_at' => now(),
                'error_message' => $exception->getMessage(),
            ]);
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'backup',
            'job:' . $this->backupJob->id,
            'server:' . $this->backupJob->source_server_id,
        ];
    }
}
