import { Link } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { DollarSign, Edit, Eye, Trash2 } from 'lucide-react';

interface Expense {
    id: number;
    name: string;
    expense_date: string;
    value: string;
    description: string | null;
    user_id: number;
    created_at: string;
    updated_at: string;
}

interface ExpenseCardProps {
    expense: Expense;
    onDelete: (expense: Expense) => void;
}

export default function ExpenseCard({ expense, onDelete }: ExpenseCardProps) {
    const formatCurrency = (value: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(parseFloat(value));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    return (
        <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
            <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <DollarSign className="w-5 h-5 text-primary" />
                </div>
                <div>
                    <h3 className="font-medium">{expense.name}</h3>
                    <p className="text-sm text-muted-foreground">
                        {formatDate(expense.expense_date)} • {formatCurrency(expense.value)}
                    </p>
                    {expense.description && (
                        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                            {expense.description}
                        </p>
                    )}
                </div>
            </div>
            <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" asChild>
                    <Link href={`/expenses/${expense.id}`}>
                        <Eye className="w-4 h-4" />
                    </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                    <Link href={`/expenses/${expense.id}/edit`}>
                        <Edit className="w-4 h-4" />
                    </Link>
                </Button>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDelete(expense)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                    <Trash2 className="w-4 h-4" />
                </Button>
            </div>
        </div>
    );
}
