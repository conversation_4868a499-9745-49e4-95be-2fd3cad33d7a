<?php

namespace Database\Seeders;

use App\Models\SourceServer;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SourceServerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sourceServers = [
            [
                'name' => 'Production Web Server',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'root',
                'authentication_method' => 'password',
                'password' => 'secure_password_123',
                'description' => 'Main production web server hosting the application',
                'is_active' => true,
                'last_connection_test' => now()->subHours(2),
            ],
            [
                'name' => 'Database Server',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'ubuntu',
                'authentication_method' => 'private_key',
                'private_key' => '-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wQNnFOKXLsstGxTkRhN2ALBp691XxdHLOoU2VNpLAMBaENRuVX6xrsMoCtP/zrH9
nQ4uRCM9topA2myHZg8aAU3t88CjZQZVBw4Axq/9SI/cYNWtLfBL2+XBjXApuBwn
bjtdFLDvbdI4BjghC5oDlkqBw98XvkAkXWRHo1+f264yLDtO7A0VcMu5+Rc8dc6+
jpyynYSx03LOELJGnf6wfBp7uWPiRJ7lqFRJEam+ycDNjDOl9Q1PvbE5NYePb4TW
13NxDAIVH0T19LlS295J8sVDoBsa/iBqlOkcxDtxwkedN7rLdFDMkdB2kVxb3U69
eR2eMB0fAgMBAAECggEBALdwxaBH4Vy9uuPiLbsHqXrLs1f8plLJrQ4LEDwht7vK
qJWvFQhyQoJcz6uQArXDAcskAXlvCuLtDqJmI2/SQ2xGsa4Wo6h/4Z1uyvhc8aPP
ac7f8IvnA6Z6VBfbPjyOE5MXxi5+WjH1ELVrT1Ljbn1cPi4W2CL2AZLdOcCcFbkb
4isyDFubbKxHQs/cEuMH2Pn7q05pIdpLwuLbqcyubRu6qyrgHjmMcfbGluDmLJXJ
ql5asI/c75mOu5B5FSjyMRxopxnwBh5Hce8RWjQjx2CHdDr1daK9jmwuNNKRZBHi
6rnlP0+AT2A4vdTQ8nl+oBcjPWJd7w/bWxrJxXyGGaECgYEA5ry7rUyEnuBONfcR
dUiJhOOelPy9VehXbme0pTbz5t1r5c8k/mxVTwlOqwFLWf5BTdA4FPg+xSxb1sol
3yDRrWqWlgDcfNgqh+HdWtBLABRTAhFyDuLSMxs8dwSoOlYKrHkVNnfn1XeSRBzL
rBYh9BC6l7wAdkxfaIRT4/7wSrECgYEA0fzrOa2y1aoUWBLsBQNgWhFh43S5SMbz
OOC8iFfMg16pGPWtuhmoLwVNFkdHgvcfgqMpGMIuEhpajjcvzQuDz5gusAirN6O+
gsiuU5ZNsI/Ub/bvzfnwlxHwXn1ePG/LDMxmVDNmNm9S+rBbEDzedVH1Vx9zK+gC
XRXzrVi6Vr8CgYB3ZAspUkOA5egqQhsDoHsb5liwrXJxkrwOZ7+6s5Qb5uJteqPi
MSAz0eaEL9aIW7OQ8LSWqJyDlq/bgn+dU/r2rVV5DbN1/s160JN4ESKipBOKY8zp
+DB3ZV9A4E9sOD1HLbTU+/FyzTrBcwXMWbrUk+ttXGwjAgMBAAECgYAn5WZMgGoC
BrXvNhVT3qMYXDbKViFgw5w3ZKAXbYDgHdCibfQkjz1s6/ubVfAiXpf7A1FtJ4S0
iyZKKM+r9lqnfFNr2u3k0DjgHSRVzGVaVw==
-----END PRIVATE KEY-----',
                'description' => 'MySQL database server with daily backups',
                'is_active' => true,
                'last_connection_test' => now()->subMinutes(30),
            ],
            [
                'name' => 'File Server',
                'ip_address' => '*************',
                'port' => 2222,
                'username' => 'fileadmin',
                'authentication_method' => 'password',
                'password' => 'files_secure_2023',
                'description' => 'Network attached storage for file backups',
                'is_active' => true,
                'last_connection_test' => now()->subHours(1),
            ],
            [
                'name' => 'Development Server',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'developer',
                'authentication_method' => 'password',
                'password' => 'dev_password_456',
                'description' => 'Development environment server',
                'is_active' => false,
                'last_connection_test' => now()->subDays(3),
            ],
            [
                'name' => 'Staging Server',
                'ip_address' => '*************',
                'port' => 22,
                'username' => 'staging',
                'authentication_method' => 'private_key',
                'private_key' => '-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDNh0QA3FkKaKs6
+b3WZYvzJ4uuEiKDVxLLjvx+WEgCjQrORNidQpg5WuEBV3Firwd8Pq9pPK8jlTAZ
-----END PRIVATE KEY-----',
                'description' => 'Pre-production staging environment',
                'is_active' => true,
                'last_connection_test' => now()->subHours(6),
            ],
        ];

        foreach ($sourceServers as $serverData) {
            SourceServer::updateOrCreate(
                ['ip_address' => $serverData['ip_address']],
                $serverData
            );
        }
    }
}
