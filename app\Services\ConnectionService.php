<?php

namespace App\Services;

use App\Models\SourceServer;
use App\Models\BackupServer;
use App\Services\Connections\ConnectionFactory;
use App\Services\Connections\ConnectionInterface;
use App\Services\Connections\SshConnection;
use App\Services\Connections\SftpConnection;
use App\Services\Connections\FtpConnection;
use Illuminate\Support\Facades\Log;
use Exception;

class ConnectionService
{
    protected ConnectionFactory $connectionFactory;

    public function __construct(ConnectionFactory $connectionFactory = null)
    {
        $this->connectionFactory = $connectionFactory ?: new ConnectionFactory();
    }

    /**
     * Connect to a source server.
     */
    public function connectToSourceServer(SourceServer $server, string $preferredType = 'ssh'): ConnectionInterface
    {
        try {
            return $this->connectionFactory->createAndConnect($server, $preferredType);
        } catch (Exception $e) {
            Log::error("Failed to connect to source server {$server->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Connect to a backup server.
     */
    public function connectToBackupServer(BackupServer $server): ConnectionInterface
    {
        try {
            return $this->connectionFactory->createAndConnect($server);
        } catch (Exception $e) {
            Log::error("Failed to connect to backup server {$server->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Test connection to a server.
     */
    public function testConnection(SourceServer|BackupServer $server): bool
    {
        try {
            return $this->connectionFactory->testConnection($server);
        } catch (Exception $e) {
            Log::warning("Connection test failed for server {$server->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Close a connection.
     */
    public function closeConnection(ConnectionInterface $connection): void
    {
        try {
            $connection->disconnect();
            Log::debug("Connection closed successfully");
        } catch (Exception $e) {
            Log::warning("Error closing connection: " . $e->getMessage());
        }
    }

    /**
     * Close all connections for a specific server.
     */
    public function closeServerConnections(SourceServer|BackupServer $server): void
    {
        $this->connectionFactory->closeServerConnections($server);
    }

    /**
     * Close all cached connections.
     */
    public function closeAllConnections(): void
    {
        $this->connectionFactory->closeAllConnections();
    }

    /**
     * Get connection statistics.
     */
    public function getConnectionStats(): array
    {
        return $this->connectionFactory->getConnectionStats();
    }

    /**
     * Clean up stale connections.
     */
    public function cleanupStaleConnections(): int
    {
        return $this->connectionFactory->cleanupStaleConnections();
    }

    /**
     * Get connection info for debugging.
     */
    public function getConnectionInfo(SourceServer|BackupServer $server): array
    {
        if ($server instanceof SourceServer) {
            return [
                'type' => 'source',
                'protocol' => 'ssh',
                'host' => $server->ip_address,
                'port' => $server->port ?: 22,
                'username' => $server->username,
                'auth_method' => $server->authentication_method,
            ];
        } elseif ($server instanceof BackupServer) {
            return [
                'type' => 'backup',
                'protocol' => $server->protocol,
                'host' => $server->ip_address,
                'port' => $server->port ?: $server->getDefaultPort(),
                'username' => $server->username,
                'base_directory' => $server->base_directory,
            ];
        }

        return [];
    }
}
