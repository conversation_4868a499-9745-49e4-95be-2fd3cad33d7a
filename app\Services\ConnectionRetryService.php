<?php

namespace App\Services;

use App\Models\SourceServer;
use App\Models\BackupServer;
use App\Services\Connections\ConnectionFactory;
use Illuminate\Support\Facades\Log;
use Exception;

class ConnectionRetryService
{
    protected ConnectionFactory $connectionFactory;
    protected int $retryCount = 0;
    protected array $retryHistory = [];

    /**
     * Default retry configuration.
     */
    protected array $defaultConfig = [
        'max_retries' => 3,
        'base_delay' => 1, // seconds
        'max_delay' => 30, // seconds
        'backoff_multiplier' => 2,
        'jitter' => true
    ];

    public function __construct(ConnectionFactory $connectionFactory = null)
    {
        $this->connectionFactory = $connectionFactory ?: new ConnectionFactory();
    }

    /**
     * Test connection with retry logic.
     */
    public function testConnectionWithRetry(
        SourceServer|BackupServer $server, 
        array $config = null
    ): bool {
        $config = array_merge($this->defaultConfig, $config ?? []);
        $this->retryCount = 0;
        $this->retryHistory = [];

        $serverName = $server->name;
        Log::info("Starting connection test with retry for server: {$serverName}");

        for ($attempt = 1; $attempt <= $config['max_retries'] + 1; $attempt++) {
            $startTime = microtime(true);
            
            try {
                $success = $this->attemptConnection($server);
                $duration = round((microtime(true) - $startTime) * 1000);

                $this->recordAttempt($attempt, $success, $duration);

                if ($success) {
                    Log::info("Connection successful for {$serverName} on attempt {$attempt}");
                    return true;
                }

                // If this was the last attempt, don't retry
                if ($attempt > $config['max_retries']) {
                    break;
                }

                $this->retryCount++;
                $delay = $this->calculateDelay($attempt, $config);
                
                Log::warning("Connection failed for {$serverName} on attempt {$attempt}, retrying in {$delay}s");
                sleep($delay);

            } catch (Exception $e) {
                $duration = round((microtime(true) - $startTime) * 1000);
                $this->recordAttempt($attempt, false, $duration, $e->getMessage());

                Log::error("Connection attempt {$attempt} failed for {$serverName}: " . $e->getMessage());

                // If this was the last attempt, don't retry
                if ($attempt > $config['max_retries']) {
                    break;
                }

                $this->retryCount++;
                $delay = $this->calculateDelay($attempt, $config);
                sleep($delay);
            }
        }

        Log::error("All connection attempts failed for server: {$serverName}");
        return false;
    }

    /**
     * Attempt a single connection.
     */
    protected function attemptConnection(SourceServer|BackupServer $server): bool
    {
        return $this->connectionFactory->testConnection($server);
    }

    /**
     * Calculate delay for next retry attempt.
     */
    protected function calculateDelay(int $attempt, array $config): int
    {
        $delay = $config['base_delay'] * pow($config['backoff_multiplier'], $attempt - 1);
        
        // Apply maximum delay limit
        $delay = min($delay, $config['max_delay']);

        // Add jitter to prevent thundering herd
        if ($config['jitter']) {
            $jitter = $delay * 0.1; // 10% jitter
            $delay += mt_rand(-$jitter * 100, $jitter * 100) / 100;
        }

        return max(1, (int)$delay);
    }

    /**
     * Record a connection attempt.
     */
    protected function recordAttempt(int $attempt, bool $success, int $duration, ?string $error = null): void
    {
        $this->retryHistory[] = [
            'attempt' => $attempt,
            'success' => $success,
            'duration_ms' => $duration,
            'error' => $error,
            'timestamp' => now()->toISOString()
        ];
    }

    /**
     * Get the number of retry attempts made.
     */
    public function getRetryCount(): int
    {
        return $this->retryCount;
    }

    /**
     * Get the retry history.
     */
    public function getRetryHistory(): array
    {
        return $this->retryHistory;
    }

    /**
     * Test connection with custom retry strategy.
     */
    public function testConnectionWithStrategy(
        SourceServer|BackupServer $server,
        string $strategy = 'exponential'
    ): bool {
        switch ($strategy) {
            case 'linear':
                return $this->testConnectionWithRetry($server, [
                    'max_retries' => 3,
                    'base_delay' => 2,
                    'backoff_multiplier' => 1, // Linear backoff
                    'jitter' => false
                ]);

            case 'aggressive':
                return $this->testConnectionWithRetry($server, [
                    'max_retries' => 5,
                    'base_delay' => 0.5,
                    'max_delay' => 10,
                    'backoff_multiplier' => 1.5,
                    'jitter' => true
                ]);

            case 'conservative':
                return $this->testConnectionWithRetry($server, [
                    'max_retries' => 2,
                    'base_delay' => 5,
                    'max_delay' => 60,
                    'backoff_multiplier' => 3,
                    'jitter' => true
                ]);

            case 'exponential':
            default:
                return $this->testConnectionWithRetry($server);
        }
    }

    /**
     * Determine appropriate retry strategy based on server type and history.
     */
    public function getRecommendedStrategy(SourceServer|BackupServer $server): string
    {
        // Get server's connection history
        $connectionOptions = $server->connection_options ?? [];
        $testHistory = $connectionOptions['test_history'] ?? [];

        if (empty($testHistory)) {
            return 'exponential'; // Default strategy
        }

        // Analyze recent connection patterns
        $recentTests = array_slice($testHistory, -10);
        $successRate = 0;
        $avgDuration = 0;

        if (!empty($recentTests)) {
            $successCount = 0;
            $totalDuration = 0;
            $validDurations = 0;

            foreach ($recentTests as $test) {
                if ($test['success'] ?? false) {
                    $successCount++;
                }
                if (isset($test['duration_ms']) && $test['duration_ms'] > 0) {
                    $totalDuration += $test['duration_ms'];
                    $validDurations++;
                }
            }

            $successRate = $successCount / count($recentTests);
            $avgDuration = $validDurations > 0 ? $totalDuration / $validDurations : 0;
        }

        // Determine strategy based on patterns
        if ($successRate > 0.8 && $avgDuration < 2000) {
            return 'aggressive'; // Fast, reliable connection
        } elseif ($successRate < 0.5) {
            return 'conservative'; // Unreliable connection
        } elseif ($avgDuration > 5000) {
            return 'conservative'; // Slow connection
        } else {
            return 'exponential'; // Standard strategy
        }
    }

    /**
     * Test multiple servers with retry logic.
     */
    public function testMultipleServers(array $servers, array $config = null): array
    {
        $results = [];
        
        foreach ($servers as $server) {
            $startTime = microtime(true);
            $success = $this->testConnectionWithRetry($server, $config);
            $duration = round((microtime(true) - $startTime) * 1000);

            $results[] = [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'server_type' => $server instanceof SourceServer ? 'source' : 'backup',
                'success' => $success,
                'duration_ms' => $duration,
                'retry_count' => $this->getRetryCount(),
                'retry_history' => $this->getRetryHistory()
            ];

            // Reset for next server
            $this->retryCount = 0;
            $this->retryHistory = [];
        }

        return $results;
    }

    /**
     * Get retry statistics.
     */
    public function getRetryStats(): array
    {
        if (empty($this->retryHistory)) {
            return [
                'total_attempts' => 0,
                'successful_attempts' => 0,
                'failed_attempts' => 0,
                'success_rate' => 0,
                'avg_duration' => 0,
                'total_duration' => 0
            ];
        }

        $successfulAttempts = 0;
        $totalDuration = 0;

        foreach ($this->retryHistory as $attempt) {
            if ($attempt['success']) {
                $successfulAttempts++;
            }
            $totalDuration += $attempt['duration_ms'];
        }

        $totalAttempts = count($this->retryHistory);
        $successRate = $totalAttempts > 0 ? ($successfulAttempts / $totalAttempts) * 100 : 0;
        $avgDuration = $totalAttempts > 0 ? $totalDuration / $totalAttempts : 0;

        return [
            'total_attempts' => $totalAttempts,
            'successful_attempts' => $successfulAttempts,
            'failed_attempts' => $totalAttempts - $successfulAttempts,
            'success_rate' => round($successRate, 1),
            'avg_duration' => round($avgDuration),
            'total_duration' => $totalDuration
        ];
    }
}
