<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;
use Exception;

class BackupLoggerService
{
    protected Logger $logger;
    protected array $context = [];

    public function __construct()
    {
        $this->initializeLogger();
    }

    /**
     * Initialize the backup-specific logger.
     */
    protected function initializeLogger(): void
    {
        $this->logger = new Logger('backup');

        // Main backup log file with rotation
        $backupHandler = new RotatingFileHandler(
            storage_path('logs/backup.log'),
            30, // Keep 30 days of logs
            Logger::DEBUG
        );

        // Custom formatter for backup logs
        $formatter = new LineFormatter(
            "[%datetime%] %level_name%: %message% %context%\n",
            'Y-m-d H:i:s'
        );
        $backupHandler->setFormatter($formatter);

        $this->logger->pushHandler($backupHandler);

        // Error-only handler for critical issues
        $errorHandler = new StreamHandler(
            storage_path('logs/backup-errors.log'),
            Logger::ERROR
        );
        $errorHandler->setFormatter($formatter);
        $this->logger->pushHandler($errorHandler);
    }

    /**
     * Set context data for all subsequent log entries.
     */
    public function setContext(array $context): void
    {
        $this->context = array_merge($this->context, $context);
    }

    /**
     * Clear context data.
     */
    public function clearContext(): void
    {
        $this->context = [];
    }

    /**
     * Log an info message.
     */
    public function info(string $message, array $context = []): void
    {
        $this->log('info', $message, $context);
    }

    /**
     * Log a debug message.
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log('debug', $message, $context);
    }

    /**
     * Log a warning message.
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log('warning', $message, $context);
    }

    /**
     * Log an error message.
     */
    public function error(string $message, array $context = []): void
    {
        $this->log('error', $message, $context);
    }

    /**
     * Log a critical message.
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log('critical', $message, $context);
    }

    /**
     * Log performance metrics.
     */
    public function logPerformance(string $operation, int $durationMs, array $metrics = []): void
    {
        $context = array_merge([
            'operation' => $operation,
            'duration_ms' => $durationMs,
            'duration_seconds' => round($durationMs / 1000, 2),
        ], $metrics);

        $this->info("Performance: {$operation} completed in {$durationMs}ms", $context);
    }

    /**
     * Log backup job start.
     */
    public function logBackupStart(int $jobId, string $jobName, array $details = []): void
    {
        $context = array_merge([
            'job_id' => $jobId,
            'job_name' => $jobName,
            'event' => 'backup_start',
        ], $details);

        $this->info("Backup job started: {$jobName}", $context);
    }

    /**
     * Log backup job completion.
     */
    public function logBackupComplete(int $jobId, string $jobName, array $results = []): void
    {
        $context = array_merge([
            'job_id' => $jobId,
            'job_name' => $jobName,
            'event' => 'backup_complete',
        ], $results);

        $this->info("Backup job completed: {$jobName}", $context);
    }

    /**
     * Log backup job failure.
     */
    public function logBackupFailure(int $jobId, string $jobName, string $errorMessage, array $details = []): void
    {
        $context = array_merge([
            'job_id' => $jobId,
            'job_name' => $jobName,
            'event' => 'backup_failure',
            'error_message' => $errorMessage,
        ], $details);

        $this->error("Backup job failed: {$jobName} - {$errorMessage}", $context);
    }

    /**
     * Log file transfer events.
     */
    public function logFileTransfer(string $filePath, int $fileSize, string $status, array $details = []): void
    {
        $context = array_merge([
            'file_path' => $filePath,
            'file_size' => $fileSize,
            'status' => $status,
            'event' => 'file_transfer',
        ], $details);

        $level = $status === 'failed' ? 'warning' : 'debug';
        $this->log($level, "File transfer {$status}: {$filePath}", $context);
    }

    /**
     * Log connection events.
     */
    public function logConnection(string $serverType, string $host, string $status, array $details = []): void
    {
        $context = array_merge([
            'server_type' => $serverType,
            'host' => $host,
            'status' => $status,
            'event' => 'connection',
        ], $details);

        $level = $status === 'failed' ? 'error' : 'info';
        $this->log($level, "Connection {$status}: {$serverType} to {$host}", $context);
    }

    /**
     * Log compression events.
     */
    public function logCompression(string $algorithm, int $originalSize, int $compressedSize, int $durationMs): void
    {
        $ratio = $originalSize > 0 ? round((1 - $compressedSize / $originalSize) * 100, 2) : 0;
        
        $context = [
            'algorithm' => $algorithm,
            'original_size' => $originalSize,
            'compressed_size' => $compressedSize,
            'compression_ratio' => $ratio,
            'duration_ms' => $durationMs,
            'event' => 'compression',
        ];

        $this->info("Compression completed: {$algorithm}, {$ratio}% reduction", $context);
    }

    /**
     * Log encryption events.
     */
    public function logEncryption(string $algorithm, int $fileSize, int $durationMs): void
    {
        $context = [
            'algorithm' => $algorithm,
            'file_size' => $fileSize,
            'duration_ms' => $durationMs,
            'event' => 'encryption',
        ];

        $this->info("Encryption completed: {$algorithm}", $context);
    }

    /**
     * Log retention policy events.
     */
    public function logRetention(int $jobId, int $deletedCount, int $retainedCount, array $details = []): void
    {
        $context = array_merge([
            'job_id' => $jobId,
            'deleted_count' => $deletedCount,
            'retained_count' => $retainedCount,
            'event' => 'retention',
        ], $details);

        $this->info("Retention policy applied: deleted {$deletedCount}, retained {$retainedCount}", $context);
    }

    /**
     * Log system resource usage.
     */
    public function logResourceUsage(array $resources): void
    {
        $context = array_merge([
            'event' => 'resource_usage',
        ], $resources);

        $this->debug("System resource usage", $context);
    }

    /**
     * Core logging method.
     */
    protected function log(string $level, string $message, array $context = []): void
    {
        try {
            $fullContext = array_merge($this->context, $context, [
                'timestamp' => now()->toISOString(),
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
            ]);

            $this->logger->log($level, $message, $fullContext);

            // Also log to Laravel's default logger for critical errors
            if (in_array($level, ['error', 'critical'])) {
                Log::channel('single')->log($level, "[BACKUP] {$message}", $fullContext);
            }

        } catch (Exception $e) {
            // Fallback to Laravel's default logger if our custom logger fails
            Log::error("BackupLoggerService failed: " . $e->getMessage());
            Log::log($level, "[BACKUP] {$message}", $context);
        }
    }

    /**
     * Get recent log entries.
     */
    public function getRecentLogs(int $hours = 24): array
    {
        try {
            $logFile = storage_path('logs/backup.log');
            
            if (!file_exists($logFile)) {
                return [];
            }

            $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $recentLogs = [];
            $cutoffTime = now()->subHours($hours);

            foreach (array_reverse($lines) as $line) {
                // Parse timestamp from log line
                if (preg_match('/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
                    $logTime = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $matches[1]);
                    
                    if ($logTime->gte($cutoffTime)) {
                        $recentLogs[] = $line;
                    } else {
                        break; // Logs are in reverse chronological order
                    }
                }
            }

            return array_reverse($recentLogs); // Return in chronological order

        } catch (Exception $e) {
            Log::error("Failed to get recent logs: " . $e->getMessage());
            return [];
        }
    }
}
