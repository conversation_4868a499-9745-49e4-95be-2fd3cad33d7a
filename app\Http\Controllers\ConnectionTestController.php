<?php

namespace App\Http\Controllers;

use App\Services\ConnectionStatusService;
use App\Services\ConnectionDiagnosticsService;
use App\Models\SourceServer;
use App\Models\BackupServer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ConnectionTestController extends Controller
{
    protected ConnectionStatusService $statusService;
    protected ConnectionDiagnosticsService $diagnosticsService;

    public function __construct(
        ConnectionStatusService $statusService,
        ConnectionDiagnosticsService $diagnosticsService
    ) {
        $this->statusService = $statusService;
        $this->diagnosticsService = $diagnosticsService;
    }

    /**
     * Test connection to a source server (API endpoint).
     */
    public function testSourceServer(SourceServer $sourceServer): JsonResponse
    {
        try {
            $testId = $this->statusService->startConnectionTest($sourceServer);

            return response()->json([
                'success' => true,
                'message' => 'Connection test started',
                'test_id' => $testId,
                'status_url' => route('api.connection-test.status', ['testId' => $testId])
            ]);

        } catch (\Exception $e) {
            \Log::error("Failed to start connection test for source server {$sourceServer->name}: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to start connection test: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test connection to a backup server (API endpoint).
     */
    public function testBackupServer(BackupServer $backupServer): JsonResponse
    {
        try {
            $testId = $this->statusService->startConnectionTest($backupServer);

            return response()->json([
                'success' => true,
                'message' => 'Connection test started',
                'test_id' => $testId,
                'status_url' => route('api.connection-test.status', ['testId' => $testId])
            ]);

        } catch (\Exception $e) {
            \Log::error("Failed to start connection test for backup server {$backupServer->name}: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to start connection test: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the status of a connection test.
     */
    public function getStatus(string $testId): JsonResponse
    {
        $status = $this->statusService->getTestStatus($testId);

        // Add some debugging
        \Log::info("Connection test status request for {$testId}: " . json_encode($status));

        if (!$status) {
            return response()->json([
                'success' => false,
                'message' => 'Test not found or expired'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status' => $status
        ]);
    }

    /**
     * Cancel a connection test.
     */
    public function cancelTest(string $testId): JsonResponse
    {
        $cancelled = $this->statusService->cancelTest($testId);

        if (!$cancelled) {
            return response()->json([
                'success' => false,
                'message' => 'Test cannot be cancelled or not found'
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Test cancelled successfully'
        ]);
    }

    /**
     * Get connection health for a server.
     */
    public function getServerHealth(Request $request): JsonResponse
    {
        $request->validate([
            'server_type' => 'required|in:source,backup',
            'server_id' => 'required|integer'
        ]);

        $serverType = $request->get('server_type');
        $serverId = $request->get('server_id');

        if ($serverType === 'source') {
            $server = SourceServer::find($serverId);
        } else {
            $server = BackupServer::find($serverId);
        }

        if (!$server) {
            return response()->json([
                'success' => false,
                'message' => 'Server not found'
            ], 404);
        }

        $health = $this->statusService->getServerHealth($server);

        return response()->json([
            'success' => true,
            'health' => $health
        ]);
    }

    /**
     * Run diagnostics for a server.
     */
    public function runDiagnostics(Request $request): JsonResponse
    {
        $request->validate([
            'server_type' => 'required|in:source,backup',
            'server_id' => 'required|integer'
        ]);

        $serverType = $request->get('server_type');
        $serverId = $request->get('server_id');

        if ($serverType === 'source') {
            $server = SourceServer::find($serverId);
        } else {
            $server = BackupServer::find($serverId);
        }

        if (!$server) {
            return response()->json([
                'success' => false,
                'message' => 'Server not found'
            ], 404);
        }

        try {
            $diagnostics = $this->diagnosticsService->runFullDiagnostics($server);

            return response()->json([
                'success' => true,
                'diagnostics' => $diagnostics
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Diagnostics failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get connection summary for dashboard.
     */
    public function getSummary(): JsonResponse
    {
        $summary = $this->statusService->getConnectionSummary();

        return response()->json([
            'success' => true,
            'summary' => $summary
        ]);
    }

    /**
     * Get test history for a server.
     */
    public function getTestHistory(Request $request): JsonResponse
    {
        $request->validate([
            'server_type' => 'required|in:source,backup',
            'server_id' => 'required|integer',
            'limit' => 'nullable|integer|min:1|max:50'
        ]);

        $serverType = $request->get('server_type');
        $serverId = $request->get('server_id');
        $limit = $request->get('limit', 10);

        if ($serverType === 'source') {
            $server = SourceServer::find($serverId);
        } else {
            $server = BackupServer::find($serverId);
        }

        if (!$server) {
            return response()->json([
                'success' => false,
                'message' => 'Server not found'
            ], 404);
        }

        $history = $this->statusService->getServerTestHistory($server, $limit);

        return response()->json([
            'success' => true,
            'history' => $history
        ]);
    }
}
