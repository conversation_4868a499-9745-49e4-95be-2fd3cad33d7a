<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('backup_job_id')->constrained('backup_jobs')->onDelete('cascade');
            $table->enum('status', ['running', 'completed', 'failed', 'cancelled'])->default('running');
            $table->timestamp('started_at');
            $table->timestamp('completed_at')->nullable();
            $table->integer('duration_seconds')->nullable();
            $table->bigInteger('backup_size_bytes')->nullable();
            $table->integer('files_count')->nullable();
            $table->text('backup_path')->nullable(); // Full path to backup file
            $table->text('error_message')->nullable();
            $table->text('output_log')->nullable(); // Command output
            $table->json('metadata')->nullable(); // Additional metadata
            $table->timestamps();

            // Indexes
            $table->index(['backup_job_id', 'status']);
            $table->index(['status', 'started_at']);
            $table->index('started_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_logs');
    }
};
