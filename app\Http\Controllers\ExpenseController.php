<?php

namespace App\Http\Controllers;

use App\Models\Expense;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class ExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $search = $request->get('search');
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        $query = Expense::query()
            ->forUser(Auth::id())
            ->with('user');

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply month/year filter
        if ($month && $year) {
            $query->forMonth($year, $month);
        }

        $expenses = $query->orderBy('expense_date', 'desc')
            ->paginate(15)
            ->withQueryString();

        // Calculate monthly totals for the current year
        $monthlyTotals = [];
        for ($m = 1; $m <= 12; $m++) {
            $total = Expense::forUser(Auth::id())
                ->forMonth($year, $m)
                ->sum('value');
            $monthlyTotals[$m] = $total;
        }

        // Calculate current month total
        $currentMonthTotal = Expense::forUser(Auth::id())
            ->forMonth($year, $month)
            ->sum('value');

        return Inertia::render('expenses/index', [
            'expenses' => $expenses,
            'search' => $search,
            'month' => (int) $month,
            'year' => (int) $year,
            'monthlyTotals' => $monthlyTotals,
            'currentMonthTotal' => $currentMonthTotal,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        return Inertia::render('expenses/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'expense_date' => 'required|date',
            'value' => 'required|numeric|min:0|max:99999999.99',
            'description' => 'nullable|string|max:1000',
        ]);

        Expense::create([
            'name' => $request->name,
            'expense_date' => $request->expense_date,
            'value' => $request->value,
            'description' => $request->description,
            'user_id' => Auth::id(),
        ]);

        return redirect()->route('expenses.index')
            ->with('success', 'Expense created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Expense $expense): Response
    {
        // Ensure user can only view their own expenses
        if ($expense->user_id !== Auth::id()) {
            abort(403);
        }

        $expense->load('user');

        return Inertia::render('expenses/show', [
            'expense' => $expense,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Expense $expense): Response
    {
        // Ensure user can only edit their own expenses
        if ($expense->user_id !== Auth::id()) {
            abort(403);
        }

        return Inertia::render('expenses/edit', [
            'expense' => $expense,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expense $expense): RedirectResponse
    {
        // Ensure user can only update their own expenses
        if ($expense->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'expense_date' => 'required|date',
            'value' => 'required|numeric|min:0|max:99999999.99',
            'description' => 'nullable|string|max:1000',
        ]);

        $expense->update([
            'name' => $request->name,
            'expense_date' => $request->expense_date,
            'value' => $request->value,
            'description' => $request->description,
        ]);

        return redirect()->route('expenses.index')
            ->with('success', 'Expense updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Expense $expense): RedirectResponse
    {
        // Ensure user can only delete their own expenses
        if ($expense->user_id !== Auth::id()) {
            abort(403);
        }

        $expense->delete();

        return redirect()->route('expenses.index')
            ->with('success', 'Expense deleted successfully.');
    }
}
