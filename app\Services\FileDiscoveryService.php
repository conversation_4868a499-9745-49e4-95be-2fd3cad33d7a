<?php

namespace App\Services;

use App\Models\SourceServer;
use App\Services\Connections\ConnectionInterface;
use Illuminate\Support\Facades\Log;
use Exception;

class FileDiscoveryService
{
    protected ConnectionService $connectionService;

    public function __construct(ConnectionService $connectionService)
    {
        $this->connectionService = $connectionService;
    }

    /**
     * Discover files on a source server.
     */
    public function discoverFiles(SourceServer $server, string $path, array $options = []): array
    {
        try {
            $connection = $this->connectionService->connectToSourceServer($server, 'sftp');
            
            $files = $this->recursiveFileDiscovery($connection, $path, $options);
            
            Log::info("Discovered " . count($files) . " files on server {$server->name} at path {$path}");
            
            return $files;
        } catch (Exception $e) {
            Log::error("File discovery failed for server {$server->name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Apply exclude patterns to filter files.
     */
    public function applyExcludePatterns(array $files, array $excludePatterns): array
    {
        if (empty($excludePatterns)) {
            return $files;
        }

        $filteredFiles = [];
        
        foreach ($files as $file) {
            $shouldExclude = false;
            
            foreach ($excludePatterns as $pattern) {
                if ($this->matchesPattern($file['path'], $pattern)) {
                    $shouldExclude = true;
                    break;
                }
            }
            
            if (!$shouldExclude) {
                $filteredFiles[] = $file;
            }
        }

        Log::info("Filtered " . (count($files) - count($filteredFiles)) . " files using exclude patterns");
        
        return $filteredFiles;
    }

    /**
     * Apply include patterns to filter files.
     */
    public function applyIncludePatterns(array $files, array $includePatterns): array
    {
        if (empty($includePatterns)) {
            return $files;
        }

        $filteredFiles = [];
        
        foreach ($files as $file) {
            foreach ($includePatterns as $pattern) {
                if ($this->matchesPattern($file['path'], $pattern)) {
                    $filteredFiles[] = $file;
                    break;
                }
            }
        }

        Log::info("Included " . count($filteredFiles) . " files using include patterns");
        
        return $filteredFiles;
    }

    /**
     * Get file metadata for a specific file.
     */
    public function getFileMetadata(ConnectionInterface $connection, string $filePath): array
    {
        try {
            $stat = $connection->stat($filePath);
            
            return [
                'path' => $filePath,
                'size' => $stat['size'] ?? 0,
                'mtime' => $stat['mtime'] ?? 0,
                'permissions' => $stat['permissions'] ?? 0,
                'type' => $stat['type'] ?? 'file',
                'uid' => $stat['uid'] ?? 0,
                'gid' => $stat['gid'] ?? 0,
            ];
        } catch (Exception $e) {
            Log::warning("Failed to get metadata for file {$filePath}: " . $e->getMessage());
            return [
                'path' => $filePath,
                'size' => 0,
                'mtime' => 0,
                'permissions' => 0,
                'type' => 'file',
                'uid' => 0,
                'gid' => 0,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Recursively discover files in a directory.
     */
    protected function recursiveFileDiscovery(ConnectionInterface $connection, string $path, array $options = []): array
    {
        $files = [];
        $maxDepth = $options['max_depth'] ?? 100;
        $followSymlinks = $options['follow_symlinks'] ?? false;
        $currentDepth = $options['current_depth'] ?? 0;

        if ($currentDepth >= $maxDepth) {
            return $files;
        }

        try {
            $items = $connection->nlist($path);
            
            foreach ($items as $item) {
                if ($item === '.' || $item === '..') {
                    continue;
                }

                $fullPath = rtrim($path, '/') . '/' . $item;
                $metadata = $this->getFileMetadata($connection, $fullPath);

                if ($metadata['type'] === 'directory') {
                    // Add directory to list
                    $files[] = $metadata;
                    
                    // Recursively scan subdirectory
                    $subdirOptions = $options;
                    $subdirOptions['current_depth'] = $currentDepth + 1;
                    $subdirFiles = $this->recursiveFileDiscovery($connection, $fullPath, $subdirOptions);
                    $files = array_merge($files, $subdirFiles);
                } elseif ($metadata['type'] === 'file') {
                    $files[] = $metadata;
                } elseif ($metadata['type'] === 'link' && $followSymlinks) {
                    // Handle symlinks if enabled
                    $files[] = $metadata;
                }
            }
        } catch (Exception $e) {
            Log::warning("Failed to list directory {$path}: " . $e->getMessage());
        }

        return $files;
    }

    /**
     * Check if a file path matches a pattern.
     */
    protected function matchesPattern(string $path, string $pattern): bool
    {
        // Convert glob pattern to regex
        $regex = $this->globToRegex($pattern);
        
        return preg_match($regex, $path) === 1;
    }

    /**
     * Convert glob pattern to regex.
     */
    protected function globToRegex(string $pattern): string
    {
        $regex = preg_quote($pattern, '/');
        
        // Replace glob wildcards with regex equivalents
        $regex = str_replace(['\*', '\?'], ['.*', '.'], $regex);
        
        return '/^' . $regex . '$/i';
    }

    /**
     * Calculate total size of discovered files.
     */
    public function calculateTotalSize(array $files): int
    {
        $totalSize = 0;
        
        foreach ($files as $file) {
            if ($file['type'] === 'file') {
                $totalSize += $file['size'];
            }
        }
        
        return $totalSize;
    }

    /**
     * Group files by directory for optimized transfer.
     */
    public function groupFilesByDirectory(array $files): array
    {
        $groups = [];
        
        foreach ($files as $file) {
            $directory = dirname($file['path']);
            
            if (!isset($groups[$directory])) {
                $groups[$directory] = [];
            }
            
            $groups[$directory][] = $file;
        }
        
        return $groups;
    }

    /**
     * Filter files by size constraints.
     */
    public function filterBySize(array $files, int $minSize = 0, int $maxSize = PHP_INT_MAX): array
    {
        return array_filter($files, function ($file) use ($minSize, $maxSize) {
            return $file['size'] >= $minSize && $file['size'] <= $maxSize;
        });
    }

    /**
     * Filter files by modification time.
     */
    public function filterByModificationTime(array $files, int $since = 0, int $until = PHP_INT_MAX): array
    {
        return array_filter($files, function ($file) use ($since, $until) {
            return $file['mtime'] >= $since && $file['mtime'] <= $until;
        });
    }
}
