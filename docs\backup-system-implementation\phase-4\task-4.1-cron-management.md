# Task 4.1: CRON Schedule Management

## Overview
Implement comprehensive CRON schedule management to handle automated backup scheduling with proper expression parsing, validation, and next-run calculation. This enables users to set up complex backup schedules using standard CRON syntax.

## Subtasks

### Subtask 4.1.1: Enhance Schedule Service with CRON Parsing

**Description:** Update the ScheduleService created in Phase 1 with full CRON expression parsing capabilities.

**Implementation Steps:**
1. Update the existing ScheduleService:
   ```bash
   # Edit existing app/Services/ScheduleService.php
   ```

2. Implement comprehensive CRON features:
   - Full CRON expression parsing (minute, hour, day, month, weekday)
   - Support for special characters (*, /, -, ?, L, #)
   - Timezone handling
   - Next/previous run calculation
   - Schedule validation and error reporting

**Manual Testing:**
- Test basic CRON expressions:
  ```php
  php artisan tinker
  $schedule = new App\Services\ScheduleService();
  
  // Test daily at 2 AM
  $nextRun = $schedule->calculateNextRun('0 2 * * *');
  echo "Next daily backup: " . $nextRun->format('Y-m-d H:i:s');
  
  // Test weekly on Sunday at 3 AM
  $nextRun = $schedule->calculateNextRun('0 3 * * 0');
  echo "Next weekly backup: " . $nextRun->format('Y-m-d H:i:s');
  
  // Test monthly on 1st at midnight
  $nextRun = $schedule->calculateNextRun('0 0 1 * *');
  echo "Next monthly backup: " . $nextRun->format('Y-m-d H:i:s');
  ```

- Test complex expressions:
  ```php
  // Every 15 minutes during business hours
  $nextRun = $schedule->calculateNextRun('*/15 9-17 * * 1-5');
  echo "Next business hours backup: " . $nextRun->format('Y-m-d H:i:s');
  
  // Every 6 hours
  $nextRun = $schedule->calculateNextRun('0 */6 * * *');
  echo "Next 6-hour backup: " . $nextRun->format('Y-m-d H:i:s');
  ```

### Subtask 4.1.2: Add Schedule Validation

**Description:** Implement comprehensive validation for CRON expressions and schedule conflicts.

**Implementation Steps:**
1. Add validation methods to ScheduleService
2. Implement schedule conflict detection
3. Add human-readable schedule descriptions
4. Create schedule testing utilities

**Manual Testing:**
- Test schedule validation:
  ```php
  php artisan tinker
  $schedule = new App\Services\ScheduleService();
  
  // Test valid expressions
  $valid = $schedule->validateCronExpression('0 2 * * *');
  echo $valid ? 'Valid daily schedule' : 'Invalid';
  
  $valid = $schedule->validateCronExpression('*/15 * * * *');
  echo $valid ? 'Valid 15-minute schedule' : 'Invalid';
  
  // Test invalid expressions
  $valid = $schedule->validateCronExpression('60 2 * * *'); // Invalid minute
  echo $valid ? 'Should be invalid' : 'Correctly invalid';
  
  $valid = $schedule->validateCronExpression('0 25 * * *'); // Invalid hour
  echo $valid ? 'Should be invalid' : 'Correctly invalid';
  ```

- Test human-readable descriptions:
  ```php
  $description = $schedule->getScheduleDescription('0 2 * * *');
  echo "Description: " . $description; // "Daily at 2:00 AM"
  
  $description = $schedule->getScheduleDescription('0 3 * * 0');
  echo "Description: " . $description; // "Weekly on Sunday at 3:00 AM"
  
  $description = $schedule->getScheduleDescription('*/30 9-17 * * 1-5');
  echo "Description: " . $description; // "Every 30 minutes from 9:00 AM to 5:00 PM, Monday through Friday"
  ```

### Subtask 4.1.3: Implement Timezone Support

**Description:** Add comprehensive timezone support for global backup scheduling.

**Implementation Steps:**
1. Add timezone configuration to backup jobs
2. Implement timezone-aware schedule calculations
3. Add daylight saving time handling
4. Create timezone conversion utilities

**Manual Testing:**
- Test timezone-aware scheduling:
  ```php
  php artisan tinker
  $schedule = new App\Services\ScheduleService();
  
  // Test with different timezones
  $utcTime = $schedule->calculateNextRun('0 2 * * *', 'UTC');
  $estTime = $schedule->calculateNextRun('0 2 * * *', 'America/New_York');
  $pstTime = $schedule->calculateNextRun('0 2 * * *', 'America/Los_Angeles');
  
  echo "UTC: " . $utcTime->format('Y-m-d H:i:s T');
  echo "EST: " . $estTime->format('Y-m-d H:i:s T');
  echo "PST: " . $pstTime->format('Y-m-d H:i:s T');
  ```

- Test daylight saving time transitions:
  ```php
  // Test around DST transition dates
  $dstDate = Carbon::create(2024, 3, 10, 1, 0, 0, 'America/New_York'); // DST starts
  $nextRun = $schedule->calculateNextRun('0 2 * * *', 'America/New_York', $dstDate);
  echo "DST transition: " . $nextRun->format('Y-m-d H:i:s T');
  ```

### Subtask 4.1.4: Create Schedule Conflict Detection

**Description:** Implement detection and resolution of scheduling conflicts between backup jobs.

**Implementation Steps:**
1. Add conflict detection algorithms
2. Implement resource-based scheduling
3. Create schedule optimization suggestions
4. Add automatic conflict resolution options

**Manual Testing:**
- Test conflict detection:
  ```php
  php artisan tinker
  $schedule = new App\Services\ScheduleService();
  
  // Create test backup jobs with same schedule
  $job1 = App\Models\BackupJob::find(1);
  $job2 = App\Models\BackupJob::find(2);
  
  $job1->schedule = '0 2 * * *';
  $job2->schedule = '0 2 * * *';
  
  $conflicts = $schedule->detectScheduleConflicts([$job1, $job2]);
  echo "Conflicts found: " . count($conflicts);
  print_r($conflicts);
  ```

- Test resource-based conflicts:
  ```php
  // Test jobs using same source server
  $sameServerJobs = App\Models\BackupJob::where('source_server_id', 1)->get();
  $resourceConflicts = $schedule->detectResourceConflicts($sameServerJobs);
  echo "Resource conflicts: " . count($resourceConflicts);
  ```

### Subtask 4.1.5: Add Schedule History and Analytics

**Description:** Implement tracking and analysis of schedule execution history.

**Implementation Steps:**
1. Create schedule execution tracking
2. Implement schedule performance analytics
3. Add schedule reliability metrics
4. Create schedule optimization recommendations

**Manual Testing:**
- Test schedule history tracking:
  ```php
  php artisan tinker
  $schedule = new App\Services\ScheduleService();
  
  // Get schedule execution history
  $job = App\Models\BackupJob::first();
  $history = $schedule->getScheduleHistory($job, 30); // Last 30 days
  
  echo "Scheduled executions: " . count($history['scheduled']);
  echo "Actual executions: " . count($history['executed']);
  echo "Missed executions: " . count($history['missed']);
  
  // Calculate reliability
  $reliability = $schedule->calculateScheduleReliability($job, 30);
  echo "Schedule reliability: " . $reliability . "%";
  ```

### Subtask 4.1.6: Create Schedule Management UI

**Description:** Update the UI to provide comprehensive schedule management capabilities.

**Implementation Steps:**
1. Add CRON expression builder/validator
2. Create schedule preview and testing tools
3. Implement timezone selection interface
4. Add conflict detection warnings

**Manual Testing:**
- Test schedule management UI:
  1. Navigate to backup job creation/edit form
  2. Use CRON expression builder to create schedules
  3. Verify schedule preview shows correct next runs
  4. Test timezone selection and conversion
  5. Check conflict warnings appear when appropriate

- Test schedule validation in UI:
  1. Enter invalid CRON expressions
  2. Verify validation errors appear
  3. Test schedule description updates in real-time
  4. Confirm timezone changes update next run times

### Subtask 4.1.7: Implement Schedule Templates

**Description:** Create predefined schedule templates for common backup scenarios.

**Implementation Steps:**
1. Create schedule template system
2. Implement common backup patterns
3. Add custom template creation
4. Create template sharing and import/export

**Manual Testing:**
- Test schedule templates:
  ```php
  php artisan tinker
  $schedule = new App\Services\ScheduleService();
  
  // Get available templates
  $templates = $schedule->getScheduleTemplates();
  print_r($templates);
  
  // Apply template to backup job
  $job = App\Models\BackupJob::first();
  $schedule->applyTemplate($job, 'daily_business_hours');
  
  echo "Applied schedule: " . $job->schedule;
  echo "Description: " . $schedule->getScheduleDescription($job->schedule);
  ```

- Test custom templates:
  ```php
  // Create custom template
  $customTemplate = [
      'name' => 'Custom Weekly',
      'description' => 'Weekly backup on weekends',
      'cron_expression' => '0 1 * * 6,0',
      'timezone' => 'UTC'
  ];
  
  $schedule->createTemplate($customTemplate);
  ```

## Database Updates

### Subtask 4.1.8: Add Schedule Tracking Tables

**Implementation Steps:**
1. Create migration for schedule history:
   ```bash
   php artisan make:migration create_schedule_history_table
   ```

2. Create migration for schedule templates:
   ```bash
   php artisan make:migration create_schedule_templates_table
   ```

**Manual Testing:**
- Verify migrations:
  ```bash
  php artisan migrate
  ```

- Test schedule history tracking:
  ```php
  php artisan tinker
  DB::table('schedule_history')->insert([
      'backup_job_id' => 1,
      'scheduled_at' => now(),
      'executed_at' => now(),
      'status' => 'completed',
      'created_at' => now(),
      'updated_at' => now(),
  ]);
  ```

## Verification Checklist

After completing all subtasks, verify:

- [ ] CRON expressions parse correctly
- [ ] Schedule validation catches invalid expressions
- [ ] Timezone support works accurately
- [ ] Conflict detection identifies overlapping schedules
- [ ] Schedule history tracks execution properly
- [ ] UI provides intuitive schedule management
- [ ] Templates simplify common scheduling scenarios
- [ ] Performance analytics provide useful insights

## Expected Files Modified

- `app/Services/ScheduleService.php` - Enhanced with full CRON functionality
- `app/Models/BackupJob.php` - Timezone and schedule template support
- UI components for schedule management
- `database/migrations/` - New schedule tracking tables

## Expected Files Created

- `database/migrations/xxxx_create_schedule_history_table.php`
- `database/migrations/xxxx_create_schedule_templates_table.php`

## Schedule Management Benefits

1. **Flexibility:** Support for complex scheduling requirements
2. **Reliability:** Conflict detection prevents resource contention
3. **Usability:** Templates and UI tools simplify schedule creation
4. **Analytics:** Historical data helps optimize backup timing
5. **Global Support:** Timezone handling enables worldwide deployments

## Common CRON Patterns

- `0 2 * * *` - Daily at 2:00 AM
- `0 2 * * 0` - Weekly on Sunday at 2:00 AM
- `0 2 1 * *` - Monthly on the 1st at 2:00 AM
- `*/15 * * * *` - Every 15 minutes
- `0 */6 * * *` - Every 6 hours
- `0 9-17 * * 1-5` - Every hour during business hours, weekdays only

## Next Steps

After completing this task, proceed to [Task 4.2: Background Job Dispatcher](./task-4.2-job-dispatcher.md).
