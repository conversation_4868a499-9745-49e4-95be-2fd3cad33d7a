<?php

namespace App\Http\Controllers;

use App\Models\BackupLog;
use App\Models\BackupJob;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class BackupLogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $query = BackupLog::with(['backupJob.sourceServer', 'backupJob.backupServer']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('backupJob', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $query->where('status', $request->get('status'));
        }

        // Filter by backup job
        if ($request->filled('backup_job') && $request->get('backup_job') !== 'all') {
            $query->where('backup_job_id', $request->get('backup_job'));
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('started_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('started_at', '<=', $request->get('date_to'));
        }

        $backupLogs = $query->orderBy('started_at', 'desc')->paginate(15);

        // Get backup jobs for filter
        $backupJobs = BackupJob::select('id', 'name')->get();

        return Inertia::render('backup-logs/index', [
            'backupLogs' => $backupLogs,
            'backupJobs' => $backupJobs,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(BackupLog $backupLog): Response
    {
        $backupLog->load(['backupJob.sourceServer', 'backupJob.backupServer']);

        return Inertia::render('backup-logs/show', [
            'backupLog' => $backupLog,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BackupLog $backupLog): RedirectResponse
    {
        // Don't allow deletion of running backups
        if ($backupLog->isRunning()) {
            return redirect()->route('backup-logs.index')
                ->with('error', 'Cannot delete a running backup log.');
        }

        $backupLog->delete();

        return redirect()->route('backup-logs.index')
            ->with('success', 'Backup log deleted successfully.');
    }

    /**
     * Cancel a running backup.
     */
    public function cancel(BackupLog $backupLog): RedirectResponse
    {
        if (!$backupLog->isRunning()) {
            return redirect()->back()
                ->with('error', 'Cannot cancel a backup that is not running.');
        }

        $backupLog->update([
            'status' => 'cancelled',
            'completed_at' => now(),
            'error_message' => 'Backup cancelled by user.',
        ]);

        $backupLog->calculateDuration();

        return redirect()->back()
            ->with('success', 'Backup cancelled successfully.');
    }

    /**
     * Download backup file.
     */
    public function download(BackupLog $backupLog): RedirectResponse
    {
        if (!$backupLog->isCompleted() || !$backupLog->backup_path) {
            return redirect()->back()
                ->with('error', 'Backup file is not available for download.');
        }

        // This would implement actual file download
        // For now, we'll just redirect back with a message
        return redirect()->back()
            ->with('info', 'Download functionality would be implemented here.');
    }

    /**
     * Retry a failed backup.
     */
    public function retry(BackupLog $backupLog): RedirectResponse
    {
        if (!$backupLog->isFailed()) {
            return redirect()->back()
                ->with('error', 'Can only retry failed backups.');
        }

        $backupJob = $backupLog->backupJob;

        // Check if job is active
        if (!$backupJob->isActive()) {
            return redirect()->back()
                ->with('error', 'Cannot retry backup for inactive job.');
        }

        // Check if job is already running
        if ($backupJob->backupLogs()->where('status', 'running')->exists()) {
            return redirect()->back()
                ->with('error', 'Backup job is already running.');
        }

        // Create new backup log entry
        $backupJob->backupLogs()->create([
            'status' => 'running',
            'started_at' => now(),
        ]);

        return redirect()->back()
            ->with('success', 'Backup retry initiated successfully.');
    }

    /**
     * Bulk delete old backup logs.
     */
    public function bulkDelete(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'older_than_days' => 'required|integer|min:1|max:365',
            'status' => 'nullable|in:completed,failed,cancelled',
        ]);

        $query = BackupLog::where('started_at', '<', now()->subDays($validated['older_than_days']));

        if (isset($validated['status'])) {
            $query->where('status', $validated['status']);
        }

        // Don't delete running backups
        $query->where('status', '!=', 'running');

        $deletedCount = $query->delete();

        return redirect()->route('backup-logs.index')
            ->with('success', "Deleted {$deletedCount} backup logs successfully.");
    }

    /**
     * Get backup statistics.
     */
    public function statistics(): Response
    {
        $stats = [
            'total_backups' => BackupLog::count(),
            'successful_backups' => BackupLog::where('status', 'completed')->count(),
            'failed_backups' => BackupLog::where('status', 'failed')->count(),
            'running_backups' => BackupLog::where('status', 'running')->count(),
            'total_size' => BackupLog::where('status', 'completed')->sum('backup_size_bytes'),
            'average_duration' => BackupLog::where('status', 'completed')->avg('duration_seconds'),
        ];

        // Recent backup activity (last 30 days)
        $recentActivity = BackupLog::with(['backupJob.sourceServer'])
            ->where('started_at', '>=', now()->subDays(30))
            ->orderBy('started_at', 'desc')
            ->limit(10)
            ->get();

        return Inertia::render('backup-logs/statistics', [
            'stats' => $stats,
            'recentActivity' => $recentActivity,
        ]);
    }
}
