<?php

namespace App\Services;

use App\Models\SourceServer;
use App\Models\BackupServer;
use Symfony\Component\Process\Process;
use Illuminate\Support\Facades\Log;
use Exception;

class ConnectionDiagnosticsService
{
    /**
     * Run full diagnostics for a server.
     */
    public function runFullDiagnostics(SourceServer|BackupServer $server): array
    {
        $diagnostics = [
            'server_info' => $this->getServerInfo($server),
            'network_connectivity' => $this->testNetworkConnectivity($server),
            'port_accessibility' => $this->testPortAccessibility($server),
            'dns_resolution' => $this->testDnsResolution($server),
            'ssl_certificate' => $this->validateSslCertificate($server),
            'authentication' => $this->testAuthentication($server),
            'performance' => $this->measurePerformance($server),
            'recommendations' => []
        ];

        // Generate recommendations based on results
        $diagnostics['recommendations'] = $this->generateRecommendations($diagnostics);

        Log::info("Full diagnostics completed for server: {$server->name}");
        return $diagnostics;
    }

    /**
     * Get basic server information.
     */
    protected function getServerInfo(SourceServer|BackupServer $server): array
    {
        return [
            'name' => $server->name,
            'type' => $server instanceof SourceServer ? 'source' : 'backup',
            'ip_address' => $server->ip_address,
            'port' => $server->port ?: $this->getDefaultPort($server),
            'protocol' => $this->getProtocol($server),
            'username' => $server->username,
            'auth_method' => $this->getAuthMethod($server),
            'last_connection_test' => $server->last_connection_test?->toISOString()
        ];
    }

    /**
     * Test network connectivity to the server.
     */
    public function testNetworkConnectivity(SourceServer|BackupServer $server): array
    {
        $result = [
            'ping' => $this->pingHost($server->ip_address),
            'traceroute' => $this->traceRoute($server->ip_address),
            'network_latency' => $this->measureLatency($server->ip_address)
        ];

        return $result;
    }

    /**
     * Ping a host to test basic connectivity.
     */
    public function pingHost(string $host, int $count = 3): array
    {
        try {
            $process = new Process(['ping', '-c', (string)$count, $host]);
            $process->setTimeout(30);
            $process->run();

            $output = $process->getOutput();
            $success = $process->isSuccessful();

            // Parse ping statistics
            $stats = $this->parsePingOutput($output);

            return [
                'success' => $success,
                'output' => $output,
                'statistics' => $stats,
                'error' => $success ? null : $process->getErrorOutput()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'output' => '',
                'statistics' => null,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test port accessibility.
     */
    public function testPortAccessibility(SourceServer|BackupServer $server): array
    {
        $port = $server->port ?: $this->getDefaultPort($server);
        return $this->testPort($server->ip_address, $port);
    }

    /**
     * Test if a specific port is accessible.
     */
    public function testPort(string $host, int $port, int $timeout = 10): array
    {
        $startTime = microtime(true);
        
        try {
            $socket = @fsockopen($host, $port, $errno, $errstr, $timeout);
            $duration = round((microtime(true) - $startTime) * 1000);

            if ($socket) {
                fclose($socket);
                return [
                    'success' => true,
                    'port' => $port,
                    'duration_ms' => $duration,
                    'error' => null
                ];
            } else {
                return [
                    'success' => false,
                    'port' => $port,
                    'duration_ms' => $duration,
                    'error' => "Connection failed: {$errstr} (Error {$errno})"
                ];
            }

        } catch (Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000);
            return [
                'success' => false,
                'port' => $port,
                'duration_ms' => $duration,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test DNS resolution.
     */
    public function testDnsResolution(SourceServer|BackupServer $server): array
    {
        $host = $server->ip_address;
        
        // Skip DNS test if it's already an IP address
        if (filter_var($host, FILTER_VALIDATE_IP)) {
            return [
                'success' => true,
                'type' => 'ip_address',
                'resolved_ip' => $host,
                'error' => null
            ];
        }

        try {
            $ip = gethostbyname($host);
            
            if ($ip === $host) {
                // DNS resolution failed
                return [
                    'success' => false,
                    'type' => 'hostname',
                    'hostname' => $host,
                    'resolved_ip' => null,
                    'error' => 'DNS resolution failed'
                ];
            }

            return [
                'success' => true,
                'type' => 'hostname',
                'hostname' => $host,
                'resolved_ip' => $ip,
                'error' => null
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'type' => 'hostname',
                'hostname' => $host,
                'resolved_ip' => null,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate SSL certificate (for FTPS/HTTPS).
     */
    public function validateSslCertificate(SourceServer|BackupServer $server): array
    {
        $protocol = $this->getProtocol($server);
        
        if (!in_array($protocol, ['ftps', 'https'])) {
            return [
                'applicable' => false,
                'reason' => 'SSL not used for this protocol'
            ];
        }

        // This is a simplified SSL validation
        // In a real implementation, you'd use more comprehensive SSL checking
        return [
            'applicable' => true,
            'valid' => true, // Placeholder
            'expiry_date' => null,
            'issuer' => null,
            'error' => null
        ];
    }

    /**
     * Test authentication methods.
     */
    protected function testAuthentication(SourceServer|BackupServer $server): array
    {
        // This would test authentication without actually connecting
        // For now, return basic auth info
        return [
            'method' => $this->getAuthMethod($server),
            'username_provided' => !empty($server->username),
            'password_provided' => !empty($server->password),
            'key_provided' => $server instanceof SourceServer && !empty($server->private_key),
            'recommendations' => $this->getAuthRecommendations($server)
        ];
    }

    /**
     * Measure connection performance.
     */
    protected function measurePerformance(SourceServer|BackupServer $server): array
    {
        $port = $server->port ?: $this->getDefaultPort($server);
        $measurements = [];

        // Take multiple measurements
        for ($i = 0; $i < 5; $i++) {
            $result = $this->testPort($server->ip_address, $port, 5);
            if ($result['success']) {
                $measurements[] = $result['duration_ms'];
            }
            usleep(100000); // 100ms delay between tests
        }

        if (empty($measurements)) {
            return [
                'success' => false,
                'error' => 'No successful connections for performance measurement'
            ];
        }

        return [
            'success' => true,
            'measurements' => $measurements,
            'min_ms' => min($measurements),
            'max_ms' => max($measurements),
            'avg_ms' => round(array_sum($measurements) / count($measurements), 2),
            'jitter_ms' => round(max($measurements) - min($measurements), 2)
        ];
    }

    /**
     * Generate recommendations based on diagnostic results.
     */
    protected function generateRecommendations(array $diagnostics): array
    {
        $recommendations = [];

        // Network connectivity recommendations
        if (!$diagnostics['network_connectivity']['ping']['success']) {
            $recommendations[] = [
                'type' => 'error',
                'category' => 'network',
                'message' => 'Host is not reachable. Check network connectivity and firewall rules.'
            ];
        }

        // Port accessibility recommendations
        if (!$diagnostics['port_accessibility']['success']) {
            $recommendations[] = [
                'type' => 'error',
                'category' => 'port',
                'message' => "Port {$diagnostics['port_accessibility']['port']} is not accessible. Check firewall and service status."
            ];
        }

        // Performance recommendations
        if ($diagnostics['performance']['success']) {
            $avgLatency = $diagnostics['performance']['avg_ms'];
            if ($avgLatency > 1000) {
                $recommendations[] = [
                    'type' => 'warning',
                    'category' => 'performance',
                    'message' => "High latency detected ({$avgLatency}ms). Consider optimizing network or using compression."
                ];
            }
        }

        // Authentication recommendations
        $auth = $diagnostics['authentication'];
        if (!$auth['password_provided'] && !$auth['key_provided']) {
            $recommendations[] = [
                'type' => 'error',
                'category' => 'authentication',
                'message' => 'No authentication credentials provided.'
            ];
        }

        return $recommendations;
    }

    /**
     * Parse ping output to extract statistics.
     */
    protected function parsePingOutput(string $output): ?array
    {
        // Simple ping output parsing
        if (preg_match('/(\d+) packets transmitted, (\d+) received/', $output, $matches)) {
            $transmitted = (int)$matches[1];
            $received = (int)$matches[2];
            $loss = $transmitted > 0 ? (($transmitted - $received) / $transmitted) * 100 : 100;

            return [
                'packets_transmitted' => $transmitted,
                'packets_received' => $received,
                'packet_loss_percent' => round($loss, 1)
            ];
        }

        return null;
    }

    /**
     * Trace route to host (simplified).
     */
    protected function traceRoute(string $host): array
    {
        try {
            $process = new Process(['traceroute', '-m', '10', $host]);
            $process->setTimeout(30);
            $process->run();

            return [
                'success' => $process->isSuccessful(),
                'output' => $process->getOutput(),
                'error' => $process->isSuccessful() ? null : $process->getErrorOutput()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'output' => '',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Measure network latency.
     */
    protected function measureLatency(string $host): ?int
    {
        $pingResult = $this->pingHost($host, 1);
        
        if ($pingResult['success'] && $pingResult['statistics']) {
            // Extract latency from ping statistics (simplified)
            return 50; // Placeholder - would parse actual ping times
        }

        return null;
    }

    /**
     * Get default port for server.
     */
    protected function getDefaultPort(SourceServer|BackupServer $server): int
    {
        if ($server instanceof SourceServer) {
            return 22; // SSH default
        }

        return match($server->protocol) {
            'sftp' => 22,
            'ftp' => 21,
            'ftps' => 21,
            default => 22
        };
    }

    /**
     * Get protocol for server.
     */
    protected function getProtocol(SourceServer|BackupServer $server): string
    {
        if ($server instanceof SourceServer) {
            return 'ssh';
        }

        return $server->protocol;
    }

    /**
     * Get authentication method for server.
     */
    protected function getAuthMethod(SourceServer|BackupServer $server): string
    {
        if ($server instanceof SourceServer) {
            return $server->authentication_method;
        }

        return 'password'; // Backup servers typically use password auth
    }

    /**
     * Get authentication recommendations.
     */
    protected function getAuthRecommendations(SourceServer|BackupServer $server): array
    {
        $recommendations = [];

        if ($server instanceof SourceServer && $server->authentication_method === 'password') {
            $recommendations[] = 'Consider using SSH key authentication for better security';
        }

        if (empty($server->password) && ($server instanceof BackupServer || $server->authentication_method === 'password')) {
            $recommendations[] = 'Password is required for authentication';
        }

        return $recommendations;
    }
}
