<?php

namespace Database\Seeders;

use App\Models\BackupJob;
use App\Models\SourceServer;
use App\Models\BackupServer;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BackupJobSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get servers for relationships
        $sourceServers = SourceServer::all();
        $backupServers = BackupServer::all();

        if ($sourceServers->isEmpty() || $backupServers->isEmpty()) {
            $this->command->warn('No source or backup servers found. Please run SourceServerSeeder and BackupServerSeeder first.');
            return;
        }

        $backupJobs = [
            [
                'name' => 'Daily Web Server Backup',
                'source_server_id' => $sourceServers->where('name', 'Production Web Server')->first()?->id ?? $sourceServers->first()->id,
                'backup_server_id' => $backupServers->where('name', 'Primary Backup Storage')->first()?->id ?? $backupServers->first()->id,
                'source_path' => '/var/www/html',
                'destination_path' => '/web_backups/daily',
                'schedule' => '0 2 * * *', // Daily at 2 AM
                'status' => 'active',
                'last_run' => now()->subHours(22),
                'next_run' => now()->addHours(2),
                'retention_policy_days' => 30,
                'compression_enabled' => true,
                'encryption_enabled' => true,
                'description' => 'Daily backup of production web server files',
                'backup_options' => [
                    'exclude_patterns' => ['*.log', '*.tmp', 'cache/*'],
                    'follow_symlinks' => false,
                ],
            ],
            [
                'name' => 'Database Backup',
                'source_server_id' => $sourceServers->where('name', 'Database Server')->first()?->id ?? $sourceServers->skip(1)->first()?->id ?? $sourceServers->first()->id,
                'backup_server_id' => $backupServers->where('name', 'Primary Backup Storage')->first()?->id ?? $backupServers->first()->id,
                'source_path' => '/var/lib/mysql',
                'destination_path' => '/database_backups',
                'schedule' => '0 3 * * *', // Daily at 3 AM
                'status' => 'active',
                'last_run' => now()->subHours(21),
                'next_run' => now()->addHours(3),
                'retention_policy_days' => 90,
                'compression_enabled' => true,
                'encryption_enabled' => true,
                'description' => 'Daily MySQL database backup with compression',
                'backup_options' => [
                    'mysqldump_options' => '--single-transaction --routines --triggers',
                    'compress_level' => 9,
                ],
            ],
            [
                'name' => 'Weekly File Server Archive',
                'source_server_id' => $sourceServers->where('name', 'File Server')->first()?->id ?? $sourceServers->skip(2)->first()?->id ?? $sourceServers->first()->id,
                'backup_server_id' => $backupServers->where('name', 'Secondary Backup Storage')->first()?->id ?? $backupServers->skip(1)->first()?->id ?? $backupServers->first()->id,
                'source_path' => '/shared/files',
                'destination_path' => '/file_archives/weekly',
                'schedule' => '0 1 * * 0', // Weekly on Sunday at 1 AM
                'status' => 'active',
                'last_run' => now()->subDays(6),
                'next_run' => now()->addDay(),
                'retention_policy_days' => 365,
                'compression_enabled' => true,
                'encryption_enabled' => false,
                'description' => 'Weekly archive of shared file server',
                'backup_options' => [
                    'archive_format' => 'tar.gz',
                    'verify_integrity' => true,
                ],
            ],
            [
                'name' => 'Staging Environment Sync',
                'source_server_id' => $sourceServers->where('name', 'Staging Server')->first()?->id ?? $sourceServers->last()->id,
                'backup_server_id' => $backupServers->where('name', 'Cloud Backup Gateway')->first()?->id ?? $backupServers->skip(2)->first()?->id ?? $backupServers->first()->id,
                'source_path' => '/var/www/staging',
                'destination_path' => '/staging_backups',
                'schedule' => '0 */6 * * *', // Every 6 hours
                'status' => 'paused',
                'last_run' => now()->subDays(2),
                'next_run' => null,
                'retention_policy_days' => 7,
                'compression_enabled' => false,
                'encryption_enabled' => true,
                'description' => 'Staging environment synchronization (currently paused)',
                'backup_options' => [
                    'sync_mode' => true,
                    'delete_extra' => false,
                ],
            ],
            [
                'name' => 'Configuration Backup',
                'source_server_id' => $sourceServers->first()->id,
                'backup_server_id' => $backupServers->first()->id,
                'source_path' => '/etc',
                'destination_path' => '/config_backups',
                'schedule' => '0 4 * * 1', // Weekly on Monday at 4 AM
                'status' => 'active',
                'last_run' => now()->subDays(5),
                'next_run' => now()->addDays(2),
                'retention_policy_days' => 180,
                'compression_enabled' => true,
                'encryption_enabled' => true,
                'description' => 'Weekly backup of system configuration files',
                'backup_options' => [
                    'include_patterns' => ['*.conf', '*.cfg', '*.ini'],
                    'exclude_sensitive' => true,
                ],
            ],
        ];

        foreach ($backupJobs as $jobData) {
            BackupJob::updateOrCreate(
                [
                    'name' => $jobData['name'],
                    'source_server_id' => $jobData['source_server_id'],
                ],
                $jobData
            );
        }
    }
}
