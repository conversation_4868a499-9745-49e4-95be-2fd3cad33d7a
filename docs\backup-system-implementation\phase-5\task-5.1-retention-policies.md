# Task 5.1: Retention Policy Engine

## Overview
Implement comprehensive retention policy management to automatically clean up old backups based on various criteria including age, count, size, and advanced rotation schemes like grandfather-father-son (GFS). This ensures efficient storage utilization while maintaining appropriate backup history.

## Subtasks

### Subtask 5.1.1: Enhance Retention Service

**Description:** Update the RetentionService created in Phase 1 with comprehensive retention policy functionality.

**Implementation Steps:**
1. Update the existing RetentionService:
   ```bash
   # Edit existing app/Services/RetentionService.php
   ```

2. Implement retention strategies:
   - Age-based retention (days, weeks, months)
   - Count-based retention (keep last N backups)
   - Size-based retention (storage limits)
   - Grandfather-Father-<PERSON> (GFS) rotation
   - Custom retention rules

**Manual Testing:**
- Test age-based retention:
  ```php
  php artisan tinker
  $retention = new App\Services\RetentionService();
  
  $job = App\Models\BackupJob::first();
  $job->retention_policy_days = 30;
  
  // Get backups older than retention period
  $oldBackups = $retention->getBackupsToDelete($job);
  echo "Backups to delete: " . $oldBackups->count();
  
  foreach ($oldBackups as $backup) {
      echo "Old backup: " . $backup->created_at . " (Age: " . $backup->created_at->diffInDays() . " days)";
  }
  ```

- Test count-based retention:
  ```php
  $job->backup_options = ['retention_count' => 10];
  $job->save();
  
  $excessBackups = $retention->getExcessBackupsByCount($job);
  echo "Excess backups: " . $excessBackups->count();
  ```

### Subtask 5.1.2: Implement Grandfather-Father-Son Rotation

**Description:** Create advanced GFS rotation scheme for long-term backup retention.

**Implementation Steps:**
1. Create GFS rotation service:
   ```bash
   touch app/Services/GfsRotationService.php
   ```

2. Implement GFS features:
   - Daily backups (sons) - keep for 1 week
   - Weekly backups (fathers) - keep for 1 month
   - Monthly backups (grandfathers) - keep for 1 year
   - Configurable retention periods
   - Automatic promotion of backups

**Manual Testing:**
- Test GFS rotation:
  ```php
  php artisan tinker
  $gfs = new App\Services\GfsRotationService();
  
  $job = App\Models\BackupJob::first();
  
  // Configure GFS policy
  $gfsPolicy = [
      'daily_retention_days' => 7,
      'weekly_retention_weeks' => 4,
      'monthly_retention_months' => 12
  ];
  
  $job->backup_options = array_merge($job->backup_options ?? [], ['gfs_policy' => $gfsPolicy]);
  $job->save();
  
  // Apply GFS rotation
  $result = $gfs->applyGfsRotation($job);
  print_r($result);
  ```

- Test backup promotion:
  ```php
  // Simulate weekly promotion
  $weeklyBackups = $gfs->promoteToWeekly($job);
  echo "Promoted to weekly: " . $weeklyBackups->count();
  
  // Simulate monthly promotion
  $monthlyBackups = $gfs->promoteToMonthly($job);
  echo "Promoted to monthly: " . $monthlyBackups->count();
  ```

### Subtask 5.1.3: Add Size-based Retention

**Description:** Implement storage quota management with size-based cleanup.

**Implementation Steps:**
1. Create size-based retention logic
2. Implement storage quota enforcement
3. Add intelligent cleanup prioritization
4. Create storage usage reporting

**Manual Testing:**
- Test size-based retention:
  ```php
  php artisan tinker
  $retention = new App\Services\RetentionService();
  
  $server = App\Models\BackupServer::first();
  
  // Set storage quota (in bytes)
  $server->connection_options = array_merge(
      $server->connection_options ?? [], 
      ['storage_quota_gb' => 100]
  );
  $server->save();
  
  // Check current usage
  $usage = $retention->calculateStorageUsage($server);
  echo "Current usage: " . $usage['used_gb'] . " GB / " . $usage['quota_gb'] . " GB";
  
  // Get backups to delete for quota compliance
  if ($usage['used_gb'] > $usage['quota_gb']) {
      $toDelete = $retention->getBackupsForQuotaCompliance($server);
      echo "Backups to delete for quota: " . $toDelete->count();
  }
  ```

### Subtask 5.1.4: Create Retention Policy Templates

**Description:** Implement predefined retention policy templates for common scenarios.

**Implementation Steps:**
1. Create retention policy templates
2. Implement template application system
3. Add custom template creation
4. Create template validation

**Manual Testing:**
- Test retention templates:
  ```php
  php artisan tinker
  $retention = new App\Services\RetentionService();
  
  // Get available templates
  $templates = $retention->getRetentionTemplates();
  print_r($templates);
  
  // Apply template to backup job
  $job = App\Models\BackupJob::first();
  $retention->applyRetentionTemplate($job, 'standard_business');
  
  echo "Applied retention policy: " . $job->retention_policy_days . " days";
  print_r($job->backup_options['retention_policy']);
  ```

- Test custom template creation:
  ```php
  $customTemplate = [
      'name' => 'Long Term Archive',
      'description' => 'Extended retention for compliance',
      'retention_days' => 2555, // 7 years
      'gfs_enabled' => true,
      'gfs_policy' => [
          'daily_retention_days' => 30,
          'weekly_retention_weeks' => 12,
          'monthly_retention_months' => 84
      ]
  ];
  
  $retention->createRetentionTemplate($customTemplate);
  ```

### Subtask 5.1.5: Implement Retention Scheduling

**Description:** Create automated retention policy execution with scheduling.

**Implementation Steps:**
1. Create retention scheduler
2. Implement retention job queuing
3. Add retention execution monitoring
4. Create retention reporting

**Manual Testing:**
- Test retention scheduling:
  ```php
  php artisan tinker
  $retention = new App\Services\RetentionService();
  
  // Schedule retention cleanup for all jobs
  $scheduled = $retention->scheduleRetentionCleanup();
  echo "Retention jobs scheduled: " . $scheduled;
  
  // Schedule for specific job
  $job = App\Models\BackupJob::first();
  $retention->scheduleJobRetention($job);
  ```

- Create retention command:
  ```bash
  php artisan make:command RunRetentionCleanup
  php artisan retention:cleanup --dry-run
  php artisan retention:cleanup --job=1
  ```

### Subtask 5.1.6: Add Retention Verification

**Description:** Implement verification and validation of retention policy execution.

**Implementation Steps:**
1. Create retention verification service
2. Implement backup integrity checks before deletion
3. Add retention audit logging
4. Create retention compliance reporting

**Manual Testing:**
- Test retention verification:
  ```php
  php artisan tinker
  $retention = new App\Services\RetentionService();
  
  $job = App\Models\BackupJob::first();
  
  // Verify retention policy compliance
  $compliance = $retention->verifyRetentionCompliance($job);
  print_r($compliance);
  
  // Check backup integrity before deletion
  $backupsToDelete = $retention->getBackupsToDelete($job);
  foreach ($backupsToDelete as $backup) {
      $isValid = $retention->verifyBackupIntegrity($backup);
      echo "Backup {$backup->id} integrity: " . ($isValid ? 'OK' : 'CORRUPTED');
  }
  ```

### Subtask 5.1.7: Create Retention Dashboard

**Description:** Create UI components for managing and monitoring retention policies.

**Implementation Steps:**
1. Create retention policy management interface
2. Add retention status visualization
3. Implement retention history tracking
4. Create storage usage dashboards

**Manual Testing:**
- Test retention dashboard:
  1. Navigate to retention management page
  2. View current retention policies for all jobs
  3. Check storage usage visualization
  4. Review retention execution history
  5. Test policy modification interface

- Test retention alerts:
  1. Set up retention policies with tight limits
  2. Verify alerts appear for policy violations
  3. Check storage quota warnings
  4. Test retention failure notifications

## Database Updates

### Subtask 5.1.8: Create Retention Tracking Tables

**Implementation Steps:**
1. Create migration for retention history:
   ```bash
   php artisan make:migration create_retention_history_table
   ```

2. Create migration for retention templates:
   ```bash
   php artisan make:migration create_retention_templates_table
   ```

**Manual Testing:**
- Verify migrations:
  ```bash
  php artisan migrate
  ```

- Test retention history tracking:
  ```php
  php artisan tinker
  DB::table('retention_history')->insert([
      'backup_job_id' => 1,
      'retention_type' => 'age_based',
      'backups_deleted' => 5,
      'space_freed_bytes' => 1073741824, // 1GB
      'executed_at' => now(),
      'created_at' => now(),
      'updated_at' => now(),
  ]);
  ```

## Verification Checklist

After completing all subtasks, verify:

- [ ] Age-based retention deletes old backups correctly
- [ ] Count-based retention maintains specified backup count
- [ ] Size-based retention enforces storage quotas
- [ ] GFS rotation promotes backups appropriately
- [ ] Retention templates apply policies correctly
- [ ] Retention scheduling executes automatically
- [ ] Retention verification prevents data loss
- [ ] Dashboard provides clear retention status

## Expected Files Created

- `app/Services/GfsRotationService.php`
- `app/Console/Commands/RunRetentionCleanup.php`
- `database/migrations/xxxx_create_retention_history_table.php`
- `database/migrations/xxxx_create_retention_templates_table.php`

## Expected Files Modified

- `app/Services/RetentionService.php` - Enhanced with full functionality
- `app/Models/BackupJob.php` - Retention policy configuration
- `app/Models/BackupServer.php` - Storage quota settings
- UI components for retention management

## Retention Policy Benefits

1. **Storage Efficiency:** Automatic cleanup prevents storage bloat
2. **Compliance:** Configurable retention meets regulatory requirements
3. **Cost Control:** Size-based policies control storage costs
4. **Flexibility:** Multiple retention strategies for different needs
5. **Automation:** Scheduled cleanup reduces manual maintenance

## Common Retention Scenarios

1. **Daily Business Backups:** 30-day retention with weekly archives
2. **Compliance Backups:** 7-year retention with GFS rotation
3. **Development Backups:** 7-day retention, count-based
4. **Critical Systems:** GFS with extended monthly retention
5. **Archive Storage:** Size-based with intelligent prioritization

## Next Steps

After completing this task, proceed to [Task 5.2: Storage Management](./task-5.2-storage-management.md).
