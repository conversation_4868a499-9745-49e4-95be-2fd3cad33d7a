# Task 4.2: Background Job Dispatcher

## Overview
Implement a robust background job dispatcher that automatically checks for due backup jobs and dispatches them for execution. This includes creating console commands, handling job queuing, and implementing proper error handling and retry mechanisms.

## Subtasks

### Subtask 4.2.1: Create Schedule Checker Console Command

**Description:** Create an Artisan command that checks for due backup jobs and dispatches them.

**Implementation Steps:**
1. Create the console command:
   ```bash
   php artisan make:command CheckScheduledBackups
   ```

2. Implement command features:
   - Check all active backup jobs for due schedules
   - Dispatch ExecuteBackupJob for due jobs
   - Update next_run timestamps
   - Handle job conflicts and resource limitations
   - Provide verbose output for monitoring

**Manual Testing:**
- Test the command manually:
  ```bash
  php artisan backup:check-scheduled --verbose
  ```

- Test with specific job:
  ```bash
  php artisan backup:check-scheduled --job=1 --verbose
  ```

- Set a job to be due and test:
  ```php
  php artisan tinker
  $job = App\Models\BackupJob::first();
  $job->update(['next_run' => now()->subMinute()]);
  exit
  ```
  ```bash
  php artisan backup:check-scheduled --verbose
  ```

### Subtask 4.2.2: Implement Job Queue Management

**Description:** Create sophisticated job queue management with priorities, rate limiting, and resource allocation.

**Implementation Steps:**
1. Create job queue manager service:
   ```bash
   touch app/Services/JobQueueManagerService.php
   ```

2. Implement features:
   - Priority-based job queuing
   - Resource-aware job dispatching
   - Rate limiting for server connections
   - Job dependency management
   - Queue monitoring and statistics

**Manual Testing:**
- Test job queue management:
  ```php
  php artisan tinker
  $queueManager = new App\Services\JobQueueManagerService();
  
  // Test priority queuing
  $highPriorityJob = App\Models\BackupJob::find(1);
  $lowPriorityJob = App\Models\BackupJob::find(2);
  
  $queueManager->dispatchJob($highPriorityJob, 'high');
  $queueManager->dispatchJob($lowPriorityJob, 'low');
  
  // Check queue status
  $status = $queueManager->getQueueStatus();
  print_r($status);
  ```

- Test resource allocation:
  ```php
  // Test with multiple jobs using same server
  $jobs = App\Models\BackupJob::where('source_server_id', 1)->take(3)->get();
  foreach ($jobs as $job) {
      $result = $queueManager->dispatchJob($job);
      echo "Job {$job->id}: " . ($result ? 'Dispatched' : 'Queued for later');
  }
  ```

### Subtask 4.2.3: Add Job Conflict Resolution

**Description:** Implement intelligent conflict resolution for overlapping backup jobs.

**Implementation Steps:**
1. Create conflict resolution service:
   ```bash
   touch app/Services/ConflictResolutionService.php
   ```

2. Implement resolution strategies:
   - Resource-based conflict detection
   - Automatic job rescheduling
   - Priority-based conflict resolution
   - User notification for manual resolution

**Manual Testing:**
- Test conflict resolution:
  ```php
  php artisan tinker
  $conflictResolver = new App\Services\ConflictResolutionService();
  
  // Create conflicting jobs
  $job1 = App\Models\BackupJob::find(1);
  $job2 = App\Models\BackupJob::find(2);
  
  // Both jobs use same source server and scheduled at same time
  $job1->update(['next_run' => now()]);
  $job2->update(['next_run' => now(), 'source_server_id' => $job1->source_server_id]);
  
  $conflicts = $conflictResolver->detectConflicts([$job1, $job2]);
  echo "Conflicts detected: " . count($conflicts);
  
  $resolution = $conflictResolver->resolveConflicts($conflicts);
  print_r($resolution);
  ```

### Subtask 4.2.4: Create Job Monitoring and Alerting

**Description:** Implement comprehensive monitoring for dispatched jobs with alerting capabilities.

**Implementation Steps:**
1. Create job monitoring service:
   ```bash
   touch app/Services/JobMonitoringService.php
   ```

2. Implement monitoring features:
   - Real-time job status tracking
   - Performance metrics collection
   - Failure detection and alerting
   - Resource usage monitoring
   - SLA compliance tracking

**Manual Testing:**
- Test job monitoring:
  ```php
  php artisan tinker
  $monitor = new App\Services\JobMonitoringService();
  
  // Start monitoring
  $monitor->startMonitoring();
  
  // Get current job statistics
  $stats = $monitor->getJobStatistics();
  print_r($stats);
  
  // Check for stuck jobs
  $stuckJobs = $monitor->detectStuckJobs();
  echo "Stuck jobs: " . count($stuckJobs);
  
  // Get performance metrics
  $metrics = $monitor->getPerformanceMetrics(24); // Last 24 hours
  print_r($metrics);
  ```

### Subtask 4.2.5: Implement Retry and Failure Handling

**Description:** Create robust retry mechanisms and failure handling for backup jobs.

**Implementation Steps:**
1. Create retry handler service:
   ```bash
   touch app/Services/RetryHandlerService.php
   ```

2. Implement retry features:
   - Exponential backoff retry strategy
   - Maximum retry limits
   - Failure categorization
   - Automatic vs manual retry decisions
   - Failure notification system

**Manual Testing:**
- Test retry mechanisms:
  ```php
  php artisan tinker
  $retryHandler = new App\Services\RetryHandlerService();
  
  // Simulate failed job
  $failedLog = App\Models\BackupLog::where('status', 'failed')->first();
  
  if ($failedLog) {
      $shouldRetry = $retryHandler->shouldRetry($failedLog);
      echo "Should retry: " . ($shouldRetry ? 'Yes' : 'No');
      
      if ($shouldRetry) {
          $retryDelay = $retryHandler->calculateRetryDelay($failedLog);
          echo "Retry in: " . $retryDelay . " seconds";
          
          $retryHandler->scheduleRetry($failedLog);
      }
  }
  ```

### Subtask 4.2.6: Create Laravel Scheduler Integration

**Description:** Integrate the backup scheduler with Laravel's task scheduler for automated execution.

**Implementation Steps:**
1. Update `app/Console/Kernel.php` to include backup scheduling
2. Create multiple schedule frequencies
3. Add schedule monitoring and health checks
4. Implement schedule failure recovery

**Manual Testing:**
- Test Laravel scheduler integration:
  ```bash
  # Add to app/Console/Kernel.php schedule method:
  # $schedule->command('backup:check-scheduled')->everyMinute();
  
  # Test scheduler
  php artisan schedule:run
  php artisan schedule:list
  ```

- Test schedule monitoring:
  ```bash
  php artisan schedule:work --verbose
  ```

### Subtask 4.2.7: Add System Health Checks

**Description:** Implement system health checks to ensure the dispatcher is functioning properly.

**Implementation Steps:**
1. Create health check service:
   ```bash
   touch app/Services/SystemHealthService.php
   ```

2. Implement health checks:
   - Queue worker status
   - Database connectivity
   - Disk space availability
   - Memory usage monitoring
   - Network connectivity tests

**Manual Testing:**
- Test system health checks:
  ```php
  php artisan tinker
  $health = new App\Services\SystemHealthService();
  
  $healthReport = $health->performHealthCheck();
  print_r($healthReport);
  
  // Check specific components
  echo "Queue workers: " . ($health->checkQueueWorkers() ? 'OK' : 'FAIL');
  echo "Database: " . ($health->checkDatabase() ? 'OK' : 'FAIL');
  echo "Disk space: " . ($health->checkDiskSpace() ? 'OK' : 'FAIL');
  echo "Memory: " . ($health->checkMemoryUsage() ? 'OK' : 'FAIL');
  ```

- Create health check command:
  ```bash
  php artisan make:command SystemHealthCheck
  php artisan system:health-check
  ```

### Subtask 4.2.8: Create Dispatcher Dashboard

**Description:** Create a dashboard for monitoring and managing the job dispatcher.

**Implementation Steps:**
1. Create dispatcher dashboard UI components
2. Add real-time job queue visualization
3. Implement dispatcher control panel
4. Add performance metrics display

**Manual Testing:**
- Test dispatcher dashboard:
  1. Navigate to dispatcher dashboard page
  2. Verify real-time job queue status
  3. Check performance metrics display
  4. Test dispatcher control functions (pause/resume)
  5. Monitor job execution in real-time

## System Integration

### Subtask 4.2.9: Create Production Deployment Scripts

**Implementation Steps:**
1. Create supervisor configuration for queue workers
2. Create systemd service files
3. Add monitoring and alerting scripts
4. Create deployment automation

**Manual Testing:**
- Test supervisor configuration:
  ```bash
  # Create supervisor config file
  sudo nano /etc/supervisor/conf.d/backup-queue-worker.conf
  
  # Reload supervisor
  sudo supervisorctl reread
  sudo supervisorctl update
  sudo supervisorctl start backup-queue-worker:*
  ```

- Test systemd service:
  ```bash
  # Create systemd service file
  sudo nano /etc/systemd/system/backup-scheduler.service
  
  # Enable and start service
  sudo systemctl enable backup-scheduler
  sudo systemctl start backup-scheduler
  sudo systemctl status backup-scheduler
  ```

## Verification Checklist

After completing all subtasks, verify:

- [ ] Console command checks schedules correctly
- [ ] Job queue management handles priorities
- [ ] Conflict resolution prevents resource contention
- [ ] Job monitoring tracks performance accurately
- [ ] Retry mechanisms handle failures gracefully
- [ ] Laravel scheduler integration works
- [ ] System health checks detect issues
- [ ] Dashboard provides useful monitoring
- [ ] Production deployment is automated

## Expected Files Created

- `app/Console/Commands/CheckScheduledBackups.php`
- `app/Services/JobQueueManagerService.php`
- `app/Services/ConflictResolutionService.php`
- `app/Services/JobMonitoringService.php`
- `app/Services/RetryHandlerService.php`
- `app/Services/SystemHealthService.php`
- `app/Console/Commands/SystemHealthCheck.php`

## Expected Files Modified

- `app/Console/Kernel.php` - Laravel scheduler integration
- UI components for dispatcher dashboard
- Supervisor and systemd configuration files

## Dispatcher Benefits

1. **Automation:** Fully automated backup execution
2. **Reliability:** Robust error handling and retry mechanisms
3. **Scalability:** Resource-aware job dispatching
4. **Monitoring:** Comprehensive job tracking and alerting
5. **Maintenance:** Health checks ensure system reliability

## Production Considerations

1. **Queue Workers:** Run multiple workers for redundancy
2. **Monitoring:** Set up external monitoring for critical alerts
3. **Logging:** Ensure adequate log retention and rotation
4. **Resources:** Monitor system resources and scale as needed
5. **Backup:** Regular backup of job queue and configuration

## Next Steps

After completing this task, proceed to [Phase 5: Retention and Cleanup](../phase-5/task-5.1-retention-policies.md).
