<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BackupMetrics extends Model
{
    use HasFactory;

    protected $table = 'backup_metrics';

    protected $fillable = [
        'backup_log_id',
        'metric_type',
        'metric_name',
        'metric_value',
        'metric_unit',
        'metric_metadata',
        'measured_at',
    ];

    protected $casts = [
        'metric_value' => 'decimal:4',
        'metric_metadata' => 'array',
        'measured_at' => 'datetime',
    ];

    /**
     * Get the backup log that owns this metrics record.
     */
    public function backupLog(): BelongsTo
    {
        return $this->belongsTo(BackupLog::class);
    }

    /**
     * Get the formatted metric value with unit.
     */
    public function getFormattedValueAttribute(): string
    {
        $value = number_format($this->metric_value, 2);

        if ($this->metric_unit) {
            return $value . ' ' . $this->metric_unit;
        }

        return $value;
    }

    /**
     * Scope to filter by metric type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('metric_type', $type);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('measured_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent metrics.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('measured_at', '>=', now()->subHours($hours));
    }
}
