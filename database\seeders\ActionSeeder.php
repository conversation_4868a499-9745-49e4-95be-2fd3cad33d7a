<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ActionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\Action::insert([
            [
                'name' => 'Deploy App',
                'source_server_id' => 1,
                'command' => 'cd /var/www/app && git pull origin main && composer install --no-interaction && php artisan migrate --force',
                'template' => 'deploy',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Build Frontend',
                'source_server_id' => 1,
                'command' => 'cd /var/www/app && npm install && npm run build',
                'template' => 'build',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Restart Services',
                'source_server_id' => 1,
                'command' => 'sudo systemctl restart nginx && sudo systemctl restart php-fpm',
                'template' => 'restart',
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
