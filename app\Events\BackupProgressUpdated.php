<?php

namespace App\Events;

use App\Models\BackupProgress;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BackupProgressUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public BackupProgress $progress;

    /**
     * Create a new event instance.
     */
    public function __construct(BackupProgress $progress)
    {
        $this->progress = $progress;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('backup-progress.' . $this->progress->backup_log_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'backup_log_id' => $this->progress->backup_log_id,
            'stage' => $this->progress->stage,
            'percentage' => $this->progress->percentage,
            'message' => $this->progress->message,
            'files_processed' => $this->progress->files_processed,
            'total_files' => $this->progress->total_files,
            'bytes_processed' => $this->progress->bytes_processed,
            'total_bytes' => $this->progress->total_bytes,
            'transfer_speed_mbps' => $this->progress->transfer_speed_mbps,
            'updated_at' => $this->progress->updated_at,
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'progress.updated';
    }
}
