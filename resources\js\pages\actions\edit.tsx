import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import ActionForm from './form';

interface SourceServer {
    id: number;
    name: string;
}

interface Template {
    label: string;
    value: string;
    command: string;
}

interface Action {
    id: number;
    name: string;
    source_server_id: number;
    command: string;
    template: string | null;
}

interface EditActionProps {
    action: Action;
    sourceServers: SourceServer[];
    templates: Template[];
}

export default function EditAction({ action, sourceServers = [], templates = [] }: EditActionProps) {
    return (
        <AppLayout>
            <Head title="Edit Action" />
            <div className="max-w-2xl mx-auto py-8">
                <h1 className="text-2xl font-bold mb-4">Edit Action</h1>
                <ActionForm action={action} sourceServers={sourceServers} templates={templates} />
            </div>
        </AppLayout>
    );
} 