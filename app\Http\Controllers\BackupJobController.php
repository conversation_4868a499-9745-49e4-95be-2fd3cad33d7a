<?php

namespace App\Http\Controllers;

use App\Models\BackupJob;
use App\Models\SourceServer;
use App\Models\BackupServer;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class BackupJobController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $query = BackupJob::with(['sourceServer', 'backupServer']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhereHas('sourceServer', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('backupServer', function ($bq) use ($search) {
                      $bq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $query->where('status', $request->get('status'));
        }

        // Filter by source server
        if ($request->filled('source_server') && $request->get('source_server') !== 'all') {
            $query->where('source_server_id', $request->get('source_server'));
        }

        // Filter by backup server
        if ($request->filled('backup_server') && $request->get('backup_server') !== 'all') {
            $query->where('backup_server_id', $request->get('backup_server'));
        }

        $backupJobs = $query->orderBy('name')->paginate(10);

        // Get servers for filters
        $sourceServers = SourceServer::select('id', 'name')->get();
        $backupServers = BackupServer::select('id', 'name')->get();

        return Inertia::render('backup-jobs/index', [
            'backupJobs' => $backupJobs,
            'sourceServers' => $sourceServers,
            'backupServers' => $backupServers,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $sourceServers = SourceServer::where('is_active', true)->orderBy('name')->get();
        $backupServers = BackupServer::where('is_active', true)->orderBy('name')->get();

        return Inertia::render('backup-jobs/create', [
            'sourceServers' => $sourceServers,
            'backupServers' => $backupServers,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:backup_jobs',
            'source_server_id' => 'required|exists:source_servers,id',
            'backup_server_id' => 'required|exists:backup_servers,id',
            'source_path' => 'required|string|max:500',
            'destination_path' => 'required|string|max:500',
            'schedule' => 'required|string|max:100', // CRON expression
            'status' => 'required|in:active,paused,disabled',
            'retention_policy_days' => 'nullable|integer|min:1|max:3650',
            'compression_enabled' => 'boolean',
            'encryption_enabled' => 'boolean',
            'description' => 'nullable|string|max:1000',
            'backup_options' => 'nullable|array',
        ]);

        // Calculate next run time based on schedule
        $validated['next_run'] = $this->calculateNextRun($validated['schedule']);

        $backupJob = BackupJob::create($validated);

        return redirect()->route('backup-jobs.index')
            ->with('success', 'Backup job created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(BackupJob $backupJob): Response
    {
        $backupJob->load(['sourceServer', 'backupServer', 'backupLogs' => function ($query) {
            $query->orderBy('started_at', 'desc')->limit(10);
        }]);

        return Inertia::render('backup-jobs/show', [
            'backupJob' => $backupJob,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BackupJob $backupJob): Response
    {
        $sourceServers = SourceServer::where('is_active', true)->orderBy('name')->get();
        $backupServers = BackupServer::where('is_active', true)->orderBy('name')->get();

        return Inertia::render('backup-jobs/edit', [
            'backupJob' => $backupJob,
            'sourceServers' => $sourceServers,
            'backupServers' => $backupServers,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BackupJob $backupJob): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:backup_jobs,name,' . $backupJob->id,
            'source_server_id' => 'required|exists:source_servers,id',
            'backup_server_id' => 'required|exists:backup_servers,id',
            'source_path' => 'required|string|max:500',
            'destination_path' => 'required|string|max:500',
            'schedule' => 'required|string|max:100',
            'status' => 'required|in:active,paused,disabled',
            'retention_policy_days' => 'nullable|integer|min:1|max:3650',
            'compression_enabled' => 'boolean',
            'encryption_enabled' => 'boolean',
            'description' => 'nullable|string|max:1000',
            'backup_options' => 'nullable|array',
        ]);

        // Recalculate next run time if schedule changed
        if ($backupJob->schedule !== $validated['schedule']) {
            $validated['next_run'] = $this->calculateNextRun($validated['schedule']);
        }

        $backupJob->update($validated);

        return redirect()->route('backup-jobs.index')
            ->with('success', 'Backup job updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BackupJob $backupJob): RedirectResponse
    {
        // Check if job is currently running
        if ($backupJob->backupLogs()->where('status', 'running')->exists()) {
            return redirect()->route('backup-jobs.index')
                ->with('error', 'Cannot delete backup job that is currently running.');
        }

        $backupJob->delete();

        return redirect()->route('backup-jobs.index')
            ->with('success', 'Backup job deleted successfully.');
    }

    /**
     * Manually trigger a backup job.
     */
    public function trigger(BackupJob $backupJob): RedirectResponse
    {
        if (!$backupJob->isActive()) {
            return redirect()->back()
                ->with('error', 'Cannot trigger inactive backup job.');
        }

        // Check if job is already running
        if ($backupJob->backupLogs()->where('status', 'running')->exists()) {
            return redirect()->back()
                ->with('error', 'Backup job is already running.');
        }

        try {
            // Dispatch the actual backup job
            \App\Jobs\ExecuteBackupJob::dispatch($backupJob);

            return redirect()->back()
                ->with('success', 'Backup job has been queued for execution. Check the backup logs for progress.');
        } catch (\Exception $e) {
            \Log::error("Failed to trigger backup job {$backupJob->name}: " . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Failed to trigger backup job: ' . $e->getMessage());
        }
    }

    /**
     * Calculate next run time based on cron schedule.
     */
    private function calculateNextRun(string $schedule): ?\Carbon\Carbon
    {
        // This is a simplified implementation
        // In a real application, you would use a cron expression parser
        try {
            return now()->addHour(); // Simplified: next hour
        } catch (\Exception $e) {
            return null;
        }
    }
}
