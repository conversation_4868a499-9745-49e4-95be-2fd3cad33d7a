<?php

namespace App\Services;

use App\Models\SourceServer;
use App\Models\BackupServer;
use App\Models\BackupLog;
use App\Services\Connections\ConnectionInterface;
use Illuminate\Support\Facades\Log;
use Exception;

class BackupTransferService
{
    protected ConnectionService $connectionService;
    protected int $chunkSize = 8192; // 8KB chunks
    protected int $maxRetries = 3;
    protected int $retryDelay = 1; // seconds

    public function __construct(ConnectionService $connectionService)
    {
        $this->connectionService = $connectionService;
    }

    /**
     * Transfer a single file from source to backup server.
     */
    public function transferFile(
        SourceServer $sourceServer,
        string $sourcePath,
        BackupServer $backupServer,
        string $destinationPath,
        ?BackupLog $log = null
    ): bool {
        $attempt = 0;
        
        while ($attempt < $this->maxRetries) {
            try {
                $sourceConnection = $this->connectionService->connectToSourceServer($sourceServer, 'sftp');
                $backupConnection = $this->connectionService->connectToBackupServer($backupServer);

                // Ensure destination directory exists
                $this->ensureDirectoryExists($backupConnection, dirname($destinationPath));

                // Get file size for progress tracking
                $fileSize = $sourceConnection->size($sourcePath);
                
                if ($log) {
                    $this->updateTransferProgress($log, "Transferring file: {$sourcePath}", 0, $fileSize);
                }

                // Transfer file in chunks
                $success = $this->transferFileInChunks(
                    $sourceConnection,
                    $sourcePath,
                    $backupConnection,
                    $destinationPath,
                    $fileSize,
                    $log
                );

                if ($success) {
                    Log::info("Successfully transferred file: {$sourcePath} -> {$destinationPath}");
                    return true;
                }

            } catch (Exception $e) {
                $attempt++;
                Log::warning("File transfer attempt {$attempt} failed: " . $e->getMessage());
                
                if ($attempt >= $this->maxRetries) {
                    Log::error("File transfer failed after {$this->maxRetries} attempts: {$sourcePath}");
                    throw $e;
                }
                
                sleep($this->retryDelay * $attempt); // Exponential backoff
            }
        }

        return false;
    }

    /**
     * Transfer multiple files from source to backup server.
     */
    public function transferFiles(
        SourceServer $sourceServer,
        array $files,
        BackupServer $backupServer,
        string $baseDestinationPath,
        ?BackupLog $log = null
    ): array {
        $results = [
            'transferred' => 0,
            'failed' => 0,
            'total_size' => 0,
            'errors' => []
        ];

        $totalFiles = count($files);
        $processedFiles = 0;

        foreach ($files as $file) {
            try {
                $relativePath = ltrim(str_replace($sourceServer->name, '', $file['path']), '/');
                $destinationPath = rtrim($baseDestinationPath, '/') . '/' . $relativePath;

                if ($log) {
                    $progress = ($processedFiles / $totalFiles) * 100;
                    $this->updateTransferProgress(
                        $log,
                        "Processing file {$processedFiles}/{$totalFiles}: {$file['path']}",
                        $progress
                    );
                }

                if ($file['type'] === 'file') {
                    $success = $this->transferFile(
                        $sourceServer,
                        $file['path'],
                        $backupServer,
                        $destinationPath,
                        $log
                    );

                    if ($success) {
                        $results['transferred']++;
                        $results['total_size'] += $file['size'];
                    } else {
                        $results['failed']++;
                        $results['errors'][] = "Failed to transfer: {$file['path']}";
                    }
                } elseif ($file['type'] === 'directory') {
                    // Create directory on backup server
                    $backupConnection = $this->connectionService->connectToBackupServer($backupServer);
                    $this->ensureDirectoryExists($backupConnection, $destinationPath);
                }

                $processedFiles++;

            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Error transferring {$file['path']}: " . $e->getMessage();
                Log::error("File transfer error: " . $e->getMessage());
            }
        }

        if ($log) {
            $this->updateTransferProgress($log, "Transfer completed", 100);
        }

        Log::info("Transfer summary: {$results['transferred']} transferred, {$results['failed']} failed");

        return $results;
    }

    /**
     * Transfer a directory recursively.
     */
    public function transferDirectory(
        SourceServer $sourceServer,
        string $sourcePath,
        BackupServer $backupServer,
        string $destinationPath,
        array $options = [],
        ?BackupLog $log = null
    ): array {
        try {
            // Discover files in the source directory
            $fileDiscovery = new FileDiscoveryService($this->connectionService);
            $files = $fileDiscovery->discoverFiles($sourceServer, $sourcePath, $options);

            // Apply filters if specified
            if (!empty($options['exclude_patterns'])) {
                $files = $fileDiscovery->applyExcludePatterns($files, $options['exclude_patterns']);
            }

            if (!empty($options['include_patterns'])) {
                $files = $fileDiscovery->applyIncludePatterns($files, $options['include_patterns']);
            }

            // Transfer all discovered files
            return $this->transferFiles($sourceServer, $files, $backupServer, $destinationPath, $log);

        } catch (Exception $e) {
            Log::error("Directory transfer failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Transfer file in chunks with progress tracking.
     */
    protected function transferFileInChunks(
        ConnectionInterface $sourceConnection,
        string $sourcePath,
        ConnectionInterface $backupConnection,
        string $destinationPath,
        int $fileSize,
        ?BackupLog $log = null
    ): bool {
        try {
            // Open source file for reading
            $sourceHandle = $sourceConnection->fopen($sourcePath, 'rb');
            if (!$sourceHandle) {
                throw new Exception("Failed to open source file: {$sourcePath}");
            }

            // Open destination file for writing
            $destHandle = $backupConnection->fopen($destinationPath, 'wb');
            if (!$destHandle) {
                fclose($sourceHandle);
                throw new Exception("Failed to open destination file: {$destinationPath}");
            }

            $bytesTransferred = 0;
            $lastProgressUpdate = 0;

            while (!feof($sourceHandle)) {
                $chunk = fread($sourceHandle, $this->chunkSize);
                if ($chunk === false) {
                    break;
                }

                $written = fwrite($destHandle, $chunk);
                if ($written === false) {
                    throw new Exception("Failed to write to destination file");
                }

                $bytesTransferred += $written;

                // Update progress every 1MB or 10%
                if ($log && ($bytesTransferred - $lastProgressUpdate) >= 1048576) {
                    $progress = $fileSize > 0 ? ($bytesTransferred / $fileSize) * 100 : 0;
                    $this->updateTransferProgress(
                        $log,
                        "Transferring: " . round($progress, 1) . "%",
                        $progress,
                        $fileSize,
                        $bytesTransferred
                    );
                    $lastProgressUpdate = $bytesTransferred;
                }
            }

            fclose($sourceHandle);
            fclose($destHandle);

            // Verify file size
            $destSize = $backupConnection->size($destinationPath);
            if ($destSize !== $fileSize) {
                throw new Exception("File size mismatch: expected {$fileSize}, got {$destSize}");
            }

            return true;

        } catch (Exception $e) {
            Log::error("Chunked file transfer failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Ensure a directory exists on the backup server.
     */
    protected function ensureDirectoryExists(ConnectionInterface $connection, string $path): bool
    {
        try {
            if ($connection->is_dir($path)) {
                return true;
            }

            // Create parent directories recursively
            $parentDir = dirname($path);
            if ($parentDir !== '/' && $parentDir !== '.') {
                $this->ensureDirectoryExists($connection, $parentDir);
            }

            return $connection->mkdir($path, 0755, true);

        } catch (Exception $e) {
            Log::warning("Failed to create directory {$path}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update transfer progress in backup log.
     */
    protected function updateTransferProgress(
        BackupLog $log,
        string $message,
        float $progress,
        int $totalSize = 0,
        int $transferredSize = 0
    ): void {
        try {
            $metadata = $log->metadata ?? [];
            $metadata['progress_percentage'] = round($progress, 2);
            $metadata['current_operation'] = $message;
            $metadata['total_size'] = $totalSize;
            $metadata['transferred_size'] = $transferredSize;
            $metadata['last_update'] = now()->toISOString();

            $log->update(['metadata' => $metadata]);

        } catch (Exception $e) {
            Log::warning("Failed to update transfer progress: " . $e->getMessage());
        }
    }

    /**
     * Set chunk size for file transfers.
     */
    public function setChunkSize(int $chunkSize): void
    {
        $this->chunkSize = max(1024, $chunkSize); // Minimum 1KB
    }

    /**
     * Set retry configuration.
     */
    public function setRetryConfig(int $maxRetries, int $retryDelay): void
    {
        $this->maxRetries = max(1, $maxRetries);
        $this->retryDelay = max(1, $retryDelay);
    }
}
