import { Head, <PERSON>, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { ArrowLeft, Edit, Trash2, DollarSign, Calendar, FileText, User } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface Expense {
    id: number;
    name: string;
    expense_date: string;
    value: string;
    description: string | null;
    user_id: number;
    created_at: string;
    updated_at: string;
    user: User;
}

interface Props {
    expense: Expense;
}

export default function ShowExpense({ expense }: Props) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Expenses',
            href: '/expenses',
        },
        {
            title: expense.name,
            href: `/expenses/${expense.id}`,
        },
    ];

    const handleDelete = () => {
        if (confirm(`Are you sure you want to delete "${expense.name}"?`)) {
            router.delete(`/expenses/${expense.id}`);
        }
    };

    const formatCurrency = (value: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(parseFloat(value));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatDateTime = (dateString: string) => {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={expense.name} />
            
            <div className="px-4 py-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-2xl font-bold">{expense.name}</h1>
                        <p className="text-muted-foreground">Expense details</p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button variant="outline" asChild>
                            <Link href="/expenses">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Expenses
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={`/expenses/${expense.id}/edit`}>
                                <Edit className="w-4 h-4 mr-2" />
                                Edit
                            </Link>
                        </Button>
                        <Button
                            variant="outline"
                            onClick={handleDelete}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Expense Information */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <DollarSign className="w-5 h-5 mr-2" />
                                    Expense Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Name</label>
                                        <p className="text-lg font-semibold">{expense.name}</p>
                                    </div>
                                    
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Amount</label>
                                        <p className="text-lg font-semibold text-primary">
                                            {formatCurrency(expense.value)}
                                        </p>
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center mb-2">
                                        <Calendar className="w-4 h-4 mr-1" />
                                        Date
                                    </label>
                                    <p className="text-base">{formatDate(expense.expense_date)}</p>
                                </div>

                                {expense.description && (
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground flex items-center mb-2">
                                            <FileText className="w-4 h-4 mr-1" />
                                            Description
                                        </label>
                                        <p className="text-base whitespace-pre-wrap">{expense.description}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar Information */}
                    <div className="space-y-6">
                        {/* User Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center text-base">
                                    <User className="w-4 h-4 mr-2" />
                                    Created By
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                        <User className="w-4 h-4 text-primary" />
                                    </div>
                                    <div>
                                        <p className="font-medium">{expense.user.name}</p>
                                        <p className="text-sm text-muted-foreground">{expense.user.email}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Timestamps */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-base">Timestamps</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Created</label>
                                    <p className="text-sm">{formatDateTime(expense.created_at)}</p>
                                </div>
                                
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                                    <p className="text-sm">{formatDateTime(expense.updated_at)}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Quick Stats */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-base">Quick Stats</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">Expense ID</span>
                                    <Badge variant="secondary">#{expense.id}</Badge>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">Month</span>
                                    <Badge variant="outline">
                                        {new Date(expense.expense_date).toLocaleDateString('en-US', { 
                                            month: 'short', 
                                            year: 'numeric' 
                                        })}
                                    </Badge>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">Day of Week</span>
                                    <Badge variant="outline">
                                        {new Date(expense.expense_date).toLocaleDateString('en-US', { 
                                            weekday: 'short' 
                                        })}
                                    </Badge>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
