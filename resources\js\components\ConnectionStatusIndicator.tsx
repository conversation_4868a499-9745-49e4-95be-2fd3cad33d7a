import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
    Wifi, 
    WifiOff, 
    Loader2, 
    CheckCircle, 
    XCircle, 
    AlertCircle,
    Clock
} from 'lucide-react';
import axios from 'axios';

interface ConnectionStatusIndicatorProps {
    serverType: 'source' | 'backup';
    serverId: number;
    serverName: string;
    lastConnectionTest?: string;
    isActive: boolean;
    showQuickTest?: boolean;
    className?: string;
}

interface ServerHealth {
    status: 'healthy' | 'warning' | 'critical' | 'unknown';
    success_rate: number;
    last_test: any;
    avg_response_time: number;
    total_tests: number;
}

export function ConnectionStatusIndicator({
    serverType,
    serverId,
    serverName,
    lastConnectionTest,
    isActive,
    showQuickTest = false,
    className = ''
}: ConnectionStatusIndicatorProps) {
    const [health, setHealth] = useState<ServerHealth | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isTestingConnection, setIsTestingConnection] = useState(false);

    useEffect(() => {
        if (isActive) {
            fetchServerHealth();
        }
    }, [serverId, serverType, isActive]);

    const fetchServerHealth = async () => {
        try {
            setIsLoading(true);
            const response = await axios.get('/api/connection-test/health', {
                params: {
                    server_type: serverType,
                    server_id: serverId
                }
            });
            
            if (response.data.success) {
                setHealth(response.data.health);
            }
        } catch (error) {
            console.error('Failed to fetch server health:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const quickTestConnection = async () => {
        try {
            setIsTestingConnection(true);
            const endpoint = serverType === 'source'
                ? `/api/connection-test/source-servers/${serverId}`
                : `/api/connection-test/backup-servers/${serverId}`;

            const response = await axios.post(endpoint);

            if (response.data.success) {
                // Show success notification
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';
                notification.innerHTML = `
                    <div class="flex items-start space-x-2">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium">Quick Test Started</h4>
                            <p class="text-sm mt-1">Testing ${serverName} connection...</p>
                        </div>
                    </div>
                `;
                document.body.appendChild(notification);
                setTimeout(() => notification.remove(), 3000);
            }

            // Refresh health after a short delay
            setTimeout(() => {
                fetchServerHealth();
            }, 2000);
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Connection test failed';
            // Show error notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';
            notification.innerHTML = `
                <div class="flex items-start space-x-2">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium">Quick Test Failed</h4>
                        <p class="text-sm mt-1">${errorMessage}</p>
                    </div>
                </div>
            `;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 5000);
        } finally {
            setIsTestingConnection(false);
        }
    };

    const getStatusIcon = () => {
        if (isLoading || isTestingConnection) {
            return <Loader2 className="h-3 w-3 animate-spin" />;
        }

        if (!isActive) {
            return <WifiOff className="h-3 w-3 text-muted-foreground" />;
        }

        if (!health) {
            return <AlertCircle className="h-3 w-3 text-muted-foreground" />;
        }

        switch (health.status) {
            case 'healthy':
                return <CheckCircle className="h-3 w-3 text-green-600" />;
            case 'warning':
                return <AlertCircle className="h-3 w-3 text-yellow-600" />;
            case 'critical':
                return <XCircle className="h-3 w-3 text-red-600" />;
            default:
                return <Clock className="h-3 w-3 text-muted-foreground" />;
        }
    };

    const getStatusText = () => {
        if (!isActive) return 'Inactive';
        if (isLoading) return 'Loading...';
        if (isTestingConnection) return 'Testing...';
        if (!health) return 'Unknown';

        const rate = Math.round(health.success_rate);
        switch (health.status) {
            case 'healthy':
                return `✓ ${rate}%`;
            case 'warning':
                return `⚠ ${rate}%`;
            case 'critical':
                return `✗ ${rate}%`;
            default:
                return 'Unknown';
        }
    };

    const getStatusVariant = () => {
        if (!isActive) return 'secondary';
        if (!health) return 'outline';

        switch (health.status) {
            case 'healthy':
                return 'default';
            case 'warning':
                return 'secondary';
            case 'critical':
                return 'destructive';
            default:
                return 'outline';
        }
    };

    const getTooltipContent = () => {
        if (!isActive) {
            return (
                <div className="space-y-1">
                    <div className="font-medium">{serverName}</div>
                    <div className="text-muted-foreground">Server is inactive</div>
                    <div className="text-xs">Enable server to test connection</div>
                </div>
            );
        }

        if (isTestingConnection) {
            return (
                <div className="space-y-1">
                    <div className="font-medium">{serverName}</div>
                    <div className="text-blue-600">Connection test in progress...</div>
                    <div className="text-xs">Please wait for results</div>
                </div>
            );
        }

        if (!health) {
            return (
                <div className="space-y-1">
                    <div className="font-medium">{serverName}</div>
                    <div className="text-muted-foreground">Connection status unknown</div>
                    <div className="text-xs">Click the test button to check connectivity</div>
                </div>
            );
        }

        const statusEmoji = health.status === 'healthy' ? '✅' :
                           health.status === 'warning' ? '⚠️' :
                           health.status === 'critical' ? '❌' : '❓';

        return (
            <div className="space-y-1">
                <div className="font-medium flex items-center gap-1">
                    <span>{statusEmoji}</span>
                    <span>{serverName}</span>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>Status: <span className="font-medium">{health.status}</span></div>
                    <div>Success: <span className="font-medium">{Math.round(health.success_rate)}%</span></div>
                    <div>Tests: <span className="font-medium">{health.total_tests}</span></div>
                    {health.avg_response_time > 0 && (
                        <div>Response: <span className="font-medium">{health.avg_response_time}ms</span></div>
                    )}
                </div>
                {health.last_test && (
                    <div className="text-xs text-muted-foreground border-t pt-1">
                        Last: {new Date(health.last_test.tested_at).toLocaleString()}
                    </div>
                )}
                <div className="text-xs text-muted-foreground">
                    Click test button for new check
                </div>
            </div>
        );
    };

    return (
        <div className={`flex items-center space-x-2 ${className}`}>
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Badge 
                            variant={getStatusVariant() as any}
                            className="cursor-help"
                        >
                            {getStatusIcon()}
                            <span className="ml-1">{getStatusText()}</span>
                        </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                        {getTooltipContent()}
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>

            {showQuickTest && isActive && (
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={quickTestConnection}
                    disabled={isTestingConnection}
                    className="h-6 px-2"
                >
                    {isTestingConnection ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                        <Wifi className="h-3 w-3" />
                    )}
                </Button>
            )}
        </div>
    );
}
