<?php

namespace Database\Seeders;

use App\Models\BackupServer;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BackupServerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $backupServers = [
            [
                'name' => 'Primary Backup Storage',
                'protocol' => 'sftp',
                'ip_address' => '*********',
                'port' => 22,
                'username' => 'backup_user',
                'password' => 'backup_secure_password_2023',
                'base_directory' => '/backups/primary',
                'description' => 'Main SFTP server for storing production backups',
                'is_active' => true,
                'last_connection_test' => now()->subMinutes(15),
                'connection_options' => [
                    'timeout' => 30,
                    'retry_attempts' => 3,
                ],
            ],
            [
                'name' => 'Secondary Backup Storage',
                'protocol' => 'ftp',
                'ip_address' => '*********',
                'port' => 21,
                'username' => 'ftp_backup',
                'password' => 'ftp_backup_pass_456',
                'base_directory' => '/backup_storage',
                'description' => 'Secondary FTP server for redundant backups',
                'is_active' => true,
                'last_connection_test' => now()->subHours(1),
                'connection_options' => [
                    'passive_mode' => true,
                    'timeout' => 60,
                ],
            ],
            [
                'name' => 'Cloud Backup Gateway',
                'protocol' => 'sftp',
                'ip_address' => '************',
                'port' => 2222,
                'username' => 'cloud_backup',
                'password' => 'cloud_secure_789',
                'base_directory' => '/cloud/backups',
                'description' => 'SFTP gateway to cloud storage provider',
                'is_active' => true,
                'last_connection_test' => now()->subMinutes(45),
                'connection_options' => [
                    'compression' => true,
                    'timeout' => 120,
                    'keep_alive' => true,
                ],
            ],
            [
                'name' => 'Archive Storage',
                'protocol' => 'sftp',
                'ip_address' => '*********',
                'port' => 22,
                'username' => 'archive_user',
                'password' => 'archive_password_321',
                'base_directory' => '/archives/long_term',
                'description' => 'Long-term archive storage for compliance',
                'is_active' => false,
                'last_connection_test' => now()->subDays(7),
                'connection_options' => [
                    'timeout' => 300,
                    'retry_attempts' => 5,
                ],
            ],
        ];

        foreach ($backupServers as $serverData) {
            BackupServer::updateOrCreate(
                ['ip_address' => $serverData['ip_address']],
                $serverData
            );
        }
    }
}
