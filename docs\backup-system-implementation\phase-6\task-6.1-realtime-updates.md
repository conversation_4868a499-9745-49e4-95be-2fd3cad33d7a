# Task 6.1: Real-time Updates

## Overview
Implement real-time updates for the backup system UI using WebSockets or Server-Sent Events to provide live feedback on backup progress, job status changes, and system alerts. This enhances user experience by providing immediate feedback without requiring page refreshes.

## Subtasks

### Subtask 6.1.1: Set Up Laravel Broadcasting

**Description:** Configure Laravel Broadcasting for real-time communication between server and client.

**Implementation Steps:**
1. Install and configure broadcasting dependencies:
   ```bash
   composer require pusher/pusher-php-server
   npm install --save-dev laravel-echo pusher-js
   ```

2. Configure broadcasting in `config/broadcasting.php`
3. Set up broadcasting routes and middleware
4. Configure frontend Echo instance

**Manual Testing:**
- Test broadcasting configuration:
  ```php
  php artisan tinker
  broadcast(new App\Events\TestEvent('Hello World'));
  ```

- Test frontend Echo connection:
  ```javascript
  // In browser console
  Echo.channel('test-channel')
      .listen('TestEvent', (e) => {
          console.log('Received:', e);
      });
  ```

### Subtask 6.1.2: Create Real-time Progress Events

**Description:** Implement events for broadcasting backup progress updates in real-time.

**Implementation Steps:**
1. Create progress update events:
   ```bash
   php artisan make:event BackupProgressUpdated
   php artisan make:event BackupStatusChanged
   php artisan make:event BackupCompleted
   ```

2. Implement event broadcasting logic
3. Integrate events with backup services
4. Add progress data serialization

**Manual Testing:**
- Test progress events:
  ```php
  php artisan tinker
  $job = App\Models\BackupJob::first();
  
  // Simulate progress updates
  event(new App\Events\BackupProgressUpdated($job, 25, 'Discovering files...'));
  event(new App\Events\BackupProgressUpdated($job, 50, 'Transferring files...'));
  event(new App\Events\BackupProgressUpdated($job, 75, 'Compressing archive...'));
  event(new App\Events\BackupCompleted($job, 'success'));
  ```

- Monitor events in browser:
  ```javascript
  Echo.channel(`backup-job.${jobId}`)
      .listen('BackupProgressUpdated', (e) => {
          console.log(`Progress: ${e.percentage}% - ${e.message}`);
          updateProgressBar(e.percentage);
      })
      .listen('BackupCompleted', (e) => {
          console.log(`Backup completed: ${e.status}`);
          showCompletionNotification(e.status);
      });
  ```

### Subtask 6.1.3: Implement Live Job Status Updates

**Description:** Create real-time updates for backup job status changes across the system.

**Implementation Steps:**
1. Create job status events:
   ```bash
   php artisan make:event JobStatusChanged
   php artisan make:event JobScheduleUpdated
   php artisan make:event JobCreated
   php artisan make:event JobDeleted
   ```

2. Implement status change broadcasting
3. Add job list real-time updates
4. Create status change animations

**Manual Testing:**
- Test job status updates:
  ```php
  php artisan tinker
  $job = App\Models\BackupJob::first();
  
  // Simulate status changes
  $job->status = 'paused';
  $job->save();
  event(new App\Events\JobStatusChanged($job));
  
  $job->status = 'active';
  $job->save();
  event(new App\Events\JobStatusChanged($job));
  ```

- Test in UI:
  1. Open backup jobs list page
  2. Change job status in another browser tab
  3. Verify status updates in real-time without refresh
  4. Check status indicator animations

### Subtask 6.1.4: Add Real-time Log Streaming

**Description:** Implement live log streaming for backup operations and system events.

**Implementation Steps:**
1. Create log streaming events:
   ```bash
   php artisan make:event LogEntryCreated
   php artisan make:event ErrorOccurred
   ```

2. Implement log streaming service
3. Add log filtering and search
4. Create scrolling log viewer component

**Manual Testing:**
- Test log streaming:
  ```php
  php artisan tinker
  $logEntry = [
      'level' => 'info',
      'message' => 'Backup operation started',
      'context' => ['job_id' => 1, 'user_id' => 1],
      'timestamp' => now()
  ];
  
  event(new App\Events\LogEntryCreated($logEntry));
  ```

- Test log viewer:
  1. Open backup log viewer page
  2. Start a backup operation
  3. Verify logs appear in real-time
  4. Test log filtering and search functionality

### Subtask 6.1.5: Create System Alert Broadcasting

**Description:** Implement real-time system alerts and notifications.

**Implementation Steps:**
1. Create alert events:
   ```bash
   php artisan make:event SystemAlert
   php artisan make:event StorageWarning
   php artisan make:event ConnectionFailure
   ```

2. Implement alert broadcasting system
3. Add alert prioritization and categorization
4. Create alert notification UI components

**Manual Testing:**
- Test system alerts:
  ```php
  php artisan tinker
  
  // Storage warning alert
  event(new App\Events\StorageWarning([
      'server_id' => 1,
      'usage_percentage' => 85,
      'message' => 'Storage usage is approaching limit'
  ]));
  
  // Connection failure alert
  event(new App\Events\ConnectionFailure([
      'server_id' => 1,
      'error' => 'Connection timeout after 30 seconds'
  ]));
  ```

- Test alert UI:
  1. Verify alerts appear as toast notifications
  2. Check alert persistence in notification center
  3. Test alert acknowledgment functionality
  4. Verify alert sound/visual indicators

### Subtask 6.1.6: Implement Dashboard Real-time Metrics

**Description:** Create real-time dashboard with live metrics and statistics.

**Implementation Steps:**
1. Create metrics events:
   ```bash
   php artisan make:event MetricsUpdated
   php artisan make:event PerformanceUpdate
   ```

2. Implement real-time metrics collection
3. Add dashboard auto-refresh functionality
4. Create animated metric visualizations

**Manual Testing:**
- Test dashboard metrics:
  ```php
  php artisan tinker
  
  $metrics = [
      'active_jobs' => 5,
      'running_backups' => 2,
      'completed_today' => 15,
      'failed_today' => 1,
      'storage_used_gb' => 250,
      'average_backup_time' => 1800 // 30 minutes
  ];
  
  event(new App\Events\MetricsUpdated($metrics));
  ```

- Test dashboard updates:
  1. Open main dashboard
  2. Start/stop backup jobs
  3. Verify metrics update in real-time
  4. Check chart animations and transitions

### Subtask 6.1.7: Add User Presence and Activity

**Description:** Implement user presence indicators and activity tracking.

**Implementation Steps:**
1. Create presence events:
   ```bash
   php artisan make:event UserOnline
   php artisan make:event UserOffline
   php artisan make:event UserActivity
   ```

2. Implement presence tracking
3. Add activity indicators in UI
4. Create user activity logs

**Manual Testing:**
- Test user presence:
  ```php
  php artisan tinker
  $user = App\Models\User::first();
  
  event(new App\Events\UserOnline($user));
  event(new App\Events\UserActivity($user, 'viewed_backup_jobs'));
  ```

- Test presence indicators:
  1. Open application in multiple browser tabs
  2. Verify online user indicators
  3. Test activity status updates
  4. Check user activity history

### Subtask 6.1.8: Optimize Real-time Performance

**Description:** Implement performance optimizations for real-time features.

**Implementation Steps:**
1. Add event throttling and debouncing
2. Implement selective broadcasting (user-specific channels)
3. Add connection management and reconnection logic
4. Create performance monitoring for real-time features

**Manual Testing:**
- Test performance optimizations:
  ```php
  php artisan tinker
  
  // Test throttled events
  for ($i = 0; $i < 100; $i++) {
      event(new App\Events\BackupProgressUpdated($job, $i, "Progress {$i}%"));
      usleep(10000); // 10ms delay
  }
  ```

- Monitor performance:
  1. Check browser network tab for WebSocket traffic
  2. Monitor memory usage during heavy updates
  3. Test connection stability over time
  4. Verify reconnection after network interruption

## Frontend Implementation

### Subtask 6.1.9: Create React Real-time Components

**Implementation Steps:**
1. Create real-time hooks and contexts
2. Implement progress bar components
3. Add notification system components
4. Create live data visualization components

**Manual Testing:**
- Test React components:
  1. Verify progress bars update smoothly
  2. Check notification animations
  3. Test component cleanup on unmount
  4. Verify memory leak prevention

## Verification Checklist

After completing all subtasks, verify:

- [ ] Broadcasting configuration works correctly
- [ ] Progress updates appear in real-time
- [ ] Job status changes broadcast immediately
- [ ] Log streaming works without delays
- [ ] System alerts appear promptly
- [ ] Dashboard metrics update live
- [ ] User presence tracking functions
- [ ] Performance optimizations are effective
- [ ] Frontend components handle updates properly

## Expected Files Created

- `app/Events/BackupProgressUpdated.php`
- `app/Events/BackupStatusChanged.php`
- `app/Events/BackupCompleted.php`
- `app/Events/JobStatusChanged.php`
- `app/Events/LogEntryCreated.php`
- `app/Events/SystemAlert.php`
- `app/Events/MetricsUpdated.php`
- `app/Events/UserOnline.php`
- React components for real-time features

## Expected Files Modified

- `config/broadcasting.php` - Broadcasting configuration
- `resources/js/bootstrap.js` - Echo setup
- Existing UI components - Real-time integration
- Service classes - Event broadcasting integration

## Real-time Benefits

1. **User Experience:** Immediate feedback improves usability
2. **Monitoring:** Real-time visibility into system status
3. **Responsiveness:** No need for manual page refreshes
4. **Collaboration:** Multiple users see updates simultaneously
5. **Alerting:** Immediate notification of critical events

## Performance Considerations

1. **Connection Management:** Efficient WebSocket connection handling
2. **Event Throttling:** Prevent overwhelming clients with updates
3. **Memory Usage:** Proper cleanup of event listeners
4. **Network Efficiency:** Minimize unnecessary data transmission
5. **Scalability:** Consider Redis for multi-server deployments

## Next Steps

After completing this task, proceed to [Task 6.2: Advanced Management Features](./task-6.2-advanced-features.md).
