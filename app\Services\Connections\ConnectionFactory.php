<?php

namespace App\Services\Connections;

use App\Models\SourceServer;
use App\Models\BackupServer;
use Illuminate\Support\Facades\Log;
use Exception;

class ConnectionFactory
{
    /**
     * Cache of active connections to avoid creating multiple connections to the same server.
     */
    protected array $connectionCache = [];

    /**
     * Create a connection handler for the specified server.
     */
    public function createConnection(SourceServer|BackupServer $server, string $preferredType = null): ConnectionInterface
    {
        $connectionType = $this->determineConnectionType($server, $preferredType);
        $cacheKey = $this->getCacheKey($server, $connectionType);

        // Return cached connection if it exists and is still connected
        if (isset($this->connectionCache[$cacheKey])) {
            $connection = $this->connectionCache[$cacheKey];
            if ($connection->isConnected()) {
                Log::debug("Reusing cached connection for server: {$server->name}");
                return $connection;
            } else {
                // Remove stale connection from cache
                unset($this->connectionCache[$cacheKey]);
            }
        }

        // Create new connection
        $connection = $this->instantiateConnection($connectionType);
        
        // Cache the connection for reuse
        $this->connectionCache[$cacheKey] = $connection;

        Log::info("Created new {$connectionType} connection for server: {$server->name}");
        return $connection;
    }

    /**
     * Create and connect to a server in one step.
     */
    public function createAndConnect(SourceServer|BackupServer $server, string $preferredType = null): ConnectionInterface
    {
        $connection = $this->createConnection($server, $preferredType);
        
        if (!$connection->connect($server)) {
            throw new Exception("Failed to connect to server {$server->name}: " . $connection->getLastError());
        }

        return $connection;
    }

    /**
     * Determine the appropriate connection type for a server.
     */
    protected function determineConnectionType(SourceServer|BackupServer $server, string $preferredType = null): string
    {
        // If a preferred type is specified and supported, use it
        if ($preferredType && $this->isConnectionTypeSupported($preferredType, $server)) {
            return $preferredType;
        }

        // For source servers, default to SSH
        if ($server instanceof SourceServer) {
            return 'ssh';
        }

        // For backup servers, use the configured protocol
        if ($server instanceof BackupServer) {
            switch (strtolower($server->protocol)) {
                case 'sftp':
                    return 'sftp';
                case 'ftp':
                case 'ftps':
                    return 'ftp';
                case 'ssh':
                    return 'ssh';
                default:
                    throw new Exception("Unsupported protocol: {$server->protocol}");
            }
        }

        throw new Exception("Unable to determine connection type for server: {$server->name}");
    }

    /**
     * Check if a connection type is supported for a server.
     */
    protected function isConnectionTypeSupported(string $connectionType, SourceServer|BackupServer $server): bool
    {
        $supportedTypes = $this->getSupportedConnectionTypes($server);
        return in_array($connectionType, $supportedTypes);
    }

    /**
     * Get supported connection types for a server.
     */
    protected function getSupportedConnectionTypes(SourceServer|BackupServer $server): array
    {
        if ($server instanceof SourceServer) {
            // Source servers typically support SSH and SFTP
            return ['ssh', 'sftp'];
        }

        if ($server instanceof BackupServer) {
            switch (strtolower($server->protocol)) {
                case 'sftp':
                    return ['sftp'];
                case 'ftp':
                case 'ftps':
                    return ['ftp'];
                case 'ssh':
                    return ['ssh', 'sftp'];
                default:
                    return [];
            }
        }

        return [];
    }

    /**
     * Instantiate a connection handler of the specified type.
     */
    protected function instantiateConnection(string $connectionType): ConnectionInterface
    {
        switch ($connectionType) {
            case 'ssh':
                return new SshConnection();
            case 'sftp':
                return new SftpConnection();
            case 'ftp':
                return new FtpConnection();
            default:
                throw new Exception("Unsupported connection type: {$connectionType}");
        }
    }

    /**
     * Generate a cache key for a server and connection type.
     */
    protected function getCacheKey(SourceServer|BackupServer $server, string $connectionType): string
    {
        $serverType = $server instanceof SourceServer ? 'source' : 'backup';
        return "{$serverType}_{$server->id}_{$connectionType}";
    }

    /**
     * Get all active connections.
     */
    public function getActiveConnections(): array
    {
        return array_filter($this->connectionCache, function ($connection) {
            return $connection->isConnected();
        });
    }

    /**
     * Close all cached connections.
     */
    public function closeAllConnections(): void
    {
        foreach ($this->connectionCache as $connection) {
            try {
                $connection->disconnect();
            } catch (Exception $e) {
                Log::warning("Error closing connection: " . $e->getMessage());
            }
        }

        $this->connectionCache = [];
        Log::info("All cached connections closed");
    }

    /**
     * Close connections for a specific server.
     */
    public function closeServerConnections(SourceServer|BackupServer $server): void
    {
        $serverType = $server instanceof SourceServer ? 'source' : 'backup';
        $prefix = "{$serverType}_{$server->id}_";

        foreach ($this->connectionCache as $key => $connection) {
            if (str_starts_with($key, $prefix)) {
                try {
                    $connection->disconnect();
                } catch (Exception $e) {
                    Log::warning("Error closing connection for server {$server->name}: " . $e->getMessage());
                }
                unset($this->connectionCache[$key]);
            }
        }

        Log::info("Closed all connections for server: {$server->name}");
    }

    /**
     * Get connection statistics.
     */
    public function getConnectionStats(): array
    {
        $stats = [
            'total_cached' => count($this->connectionCache),
            'active_connections' => 0,
            'connection_types' => [],
            'servers_connected' => []
        ];

        foreach ($this->connectionCache as $key => $connection) {
            if ($connection->isConnected()) {
                $stats['active_connections']++;
                
                $info = $connection->getConnectionInfo();
                $type = $info['type'] ?? 'unknown';
                
                if (!isset($stats['connection_types'][$type])) {
                    $stats['connection_types'][$type] = 0;
                }
                $stats['connection_types'][$type]++;
                
                $stats['servers_connected'][] = [
                    'key' => $key,
                    'type' => $type,
                    'host' => $info['host'] ?? 'unknown'
                ];
            }
        }

        return $stats;
    }

    /**
     * Test connection to a server without caching.
     */
    public function testConnection(SourceServer|BackupServer $server, string $preferredType = null): bool
    {
        try {
            $connection = $this->instantiateConnection(
                $this->determineConnectionType($server, $preferredType)
            );

            if (!$connection->connect($server)) {
                return false;
            }

            $result = $connection->testConnection();
            $connection->disconnect();

            return $result;

        } catch (Exception $e) {
            Log::error("Connection test failed for server {$server->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up stale connections from cache.
     */
    public function cleanupStaleConnections(): int
    {
        $removed = 0;
        
        foreach ($this->connectionCache as $key => $connection) {
            if (!$connection->isConnected()) {
                unset($this->connectionCache[$key]);
                $removed++;
            }
        }

        if ($removed > 0) {
            Log::info("Cleaned up {$removed} stale connections from cache");
        }

        return $removed;
    }
}
