import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ConnectionTestButton } from '@/components/ConnectionTestButton';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, MoreHorizontal, Edit, Trash2, Wifi, WifiOff, Server, Key, Lock, Calendar, Clock, Play } from 'lucide-react';

interface BackupJob {
    id: number;
    name: string;
    status: 'active' | 'paused' | 'disabled';
    schedule: string;
    next_run?: string;
    backup_server: {
        name: string;
        protocol: string;
    };
}

interface SourceServer {
    id: number;
    name: string;
    ip_address: string;
    port: number;
    username: string;
    authentication_method: 'password' | 'private_key';
    description?: string;
    is_active: boolean;
    last_connection_test?: string;
    created_at: string;
    updated_at: string;
    backup_jobs: BackupJob[];
}

interface Props {
    sourceServer: SourceServer;
}

export default function SourceServerShow({ sourceServer }: Props) {
    const handleDelete = () => {
        // Count backup jobs for this server
        const backupJobsCount = sourceServer.backup_jobs?.length || 0;
        const activeJobsCount = sourceServer.backup_jobs?.filter((job: any) => job.status === 'active').length || 0;

        let message = `Are you sure you want to delete "${sourceServer.name}"?\n\n`;

        if (backupJobsCount === 0) {
            message += `✅ This server has no backup jobs and can be safely deleted.`;
        } else {
            message += `⚠️  WARNING: This server has ${backupJobsCount} backup job(s):\n`;
            message += `   • ${activeJobsCount} active job(s)\n`;
            message += `   • ${backupJobsCount - activeJobsCount} inactive job(s)\n\n`;

            if (activeJobsCount > 0) {
                message += `❌ Cannot delete server with active backup jobs.\n\n`;
                message += `OPTIONS:\n`;
                message += `1. Cancel and disable/delete backup jobs first\n`;
                message += `2. Click OK to try anyway (will show force delete option)\n`;
                message += `3. Force delete will remove server AND all backup jobs`;
            } else {
                message += `✅ All backup jobs are inactive, deletion should work.`;
            }
        }

        const choice = confirm(message);

        if (choice) {
            router.post(`/source-servers/${sourceServer.id}/delete`, {}, {
                onSuccess: () => {
                    // Redirect will be handled by the controller
                },
                onError: (errors) => {
                    console.error('Delete failed:', errors);
                    // If regular delete fails, offer force delete
                    const forceDelete = confirm(
                        `❌ DELETE FAILED\n\n` +
                        `Server "${sourceServer.name}" has ${activeJobsCount} active backup jobs.\n\n` +
                        `🚨 FORCE DELETE OPTION:\n` +
                        `This will permanently delete:\n` +
                        `• The server "${sourceServer.name}"\n` +
                        `• All ${backupJobsCount} backup jobs\n` +
                        `• All backup history and logs\n\n` +
                        `⚠️  THIS CANNOT BE UNDONE!\n\n` +
                        `Click OK to FORCE DELETE everything, or Cancel to keep the server.`
                    );
                    if (forceDelete) {
                        router.post(`/source-servers/${sourceServer.id}/force-delete`, {}, {
                            onSuccess: () => {
                                // Redirect will be handled by the controller
                            },
                            onError: (errors) => {
                                console.error('Force delete failed:', errors);
                                alert('Force delete failed. Please try again.');
                            }
                        });
                    }
                }
            });
        }
    };



    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'default';
            case 'paused': return 'secondary';
            case 'disabled': return 'outline';
            default: return 'secondary';
        }
    };

    return (
        <AppLayout>
            <Head title={`Source Server: ${sourceServer.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href="/source-servers">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Source Servers
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">{sourceServer.name}</h1>
                            <p className="text-muted-foreground">
                                SSH server details and backup jobs
                            </p>
                        </div>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline">
                                <MoreHorizontal className="mr-2 h-4 w-4" />
                                Actions
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                                <Link href={`/source-servers/${sourceServer.id}/edit`}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Server
                                </Link>
                            </DropdownMenuItem>

                            <DropdownMenuItem 
                                onClick={handleDelete}
                                className="text-destructive"
                            >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Server
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Server Details */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Server className="mr-2 h-5 w-5" />
                                Server Information
                            </CardTitle>
                            <CardDescription>SSH connection details and configuration</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">Status</span>
                                <Badge variant={sourceServer.is_active ? 'default' : 'secondary'}>
                                    {sourceServer.is_active ? (
                                        <>
                                            <Wifi className="mr-1 h-3 w-3" />
                                            Active
                                        </>
                                    ) : (
                                        <>
                                            <WifiOff className="mr-1 h-3 w-3" />
                                            Inactive
                                        </>
                                    )}
                                </Badge>
                            </div>
                            
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">IP Address:</span>
                                    <span className="text-sm font-mono">{sourceServer.ip_address}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Port:</span>
                                    <span className="text-sm font-mono">{sourceServer.port}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Username:</span>
                                    <span className="text-sm font-mono">{sourceServer.username}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Authentication:</span>
                                    <Badge variant="outline" className="text-xs">
                                        {sourceServer.authentication_method === 'password' ? (
                                            <>
                                                <Lock className="mr-1 h-3 w-3" />
                                                Password
                                            </>
                                        ) : (
                                            <>
                                                <Key className="mr-1 h-3 w-3" />
                                                Private Key
                                            </>
                                        )}
                                    </Badge>
                                </div>
                            </div>

                            {sourceServer.description && (
                                <div>
                                    <span className="text-sm font-medium">Description</span>
                                    <p className="text-sm text-muted-foreground mt-1">{sourceServer.description}</p>
                                </div>
                            )}

                            <div className="pt-2 border-t">
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>Created: {new Date(sourceServer.created_at).toLocaleDateString()}</span>
                                    <span>Updated: {new Date(sourceServer.updated_at).toLocaleDateString()}</span>
                                </div>
                                {sourceServer.last_connection_test && (
                                    <div className="text-xs text-muted-foreground mt-1">
                                        Last test: {new Date(sourceServer.last_connection_test).toLocaleString()}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Connection Test */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Wifi className="mr-2 h-5 w-5" />
                                Connection Status
                            </CardTitle>
                            <CardDescription>Test SSH connectivity to this server</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="py-4">
                                {sourceServer.last_connection_test && (
                                    <div className="mb-4 text-center">
                                        <div className="text-sm text-muted-foreground mb-2">Last Connection Test</div>
                                        <div className="text-lg font-semibold">
                                            {new Date(sourceServer.last_connection_test).toLocaleString()}
                                        </div>
                                    </div>
                                )}

                                <ConnectionTestButton
                                    serverType="source"
                                    serverId={sourceServer.id}
                                    serverName={sourceServer.name}
                                    showProgress={true}
                                    onTestComplete={(success, result) => {
                                        if (success) {
                                            // Optionally refresh the page or update the last_connection_test
                                            console.log('Connection test successful:', result);
                                        }
                                    }}
                                />

                                <div className="mt-4 p-3 bg-muted rounded-lg text-sm">
                                    <div className="font-medium mb-1">Connection String:</div>
                                    <div className="font-mono text-xs break-all">
                                        ssh://{sourceServer.username}@{sourceServer.ip_address}:{sourceServer.port}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Backup Jobs */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    Backup Jobs ({sourceServer.backup_jobs.length})
                                </CardTitle>
                                <CardDescription>Scheduled backup tasks using this source server</CardDescription>
                            </div>
                            <Link href="/backup-jobs/create">
                                <Button size="sm">
                                    Create Backup Job
                                </Button>
                            </Link>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {sourceServer.backup_jobs.length > 0 ? (
                            <div className="space-y-4">
                                {sourceServer.backup_jobs.map((job) => (
                                    <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center space-x-4">
                                            <div>
                                                <h4 className="font-medium">{job.name}</h4>
                                                <p className="text-sm text-muted-foreground">
                                                    Destination: {job.backup_server.name} ({job.backup_server.protocol.toUpperCase()})
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    Schedule: {job.schedule}
                                                    {job.next_run && ` • Next run: ${new Date(job.next_run).toLocaleString()}`}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Badge variant={getStatusColor(job.status)}>
                                                {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                                            </Badge>
                                            <Link href={`/backup-jobs/${job.id}`}>
                                                <Button variant="outline" size="sm">
                                                    View
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No backup jobs configured</h3>
                                <p className="text-muted-foreground mb-4">
                                    Create a backup job to start backing up data from this server.
                                </p>
                                <Link href="/backup-jobs/create">
                                    <Button>
                                        Create First Backup Job
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
