<?php

namespace App\Services;

use App\Models\BackupLog;
use App\Models\BackupMetrics;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class MetricsCollectionService
{
    protected array $activeCollections = [];

    /**
     * Start metrics collection for a backup job.
     */
    public function startCollection(int $backupLogId): void
    {
        try {
            $this->activeCollections[$backupLogId] = [
                'started_at' => now(),
                'transfer_start_times' => [],
                'compression_start_time' => null,
                'encryption_start_time' => null,
            ];

            // Record initial system metrics
            $this->recordSystemMetrics($backupLogId);

            Log::debug("Started metrics collection for backup log: {$backupLogId}");

        } catch (Exception $e) {
            Log::error("Failed to start metrics collection: " . $e->getMessage());
        }
    }

    /**
     * Record transfer start for a file.
     */
    public function recordTransferStart(int $backupLogId, string $fileName, int $fileSize): void
    {
        try {
            if (!isset($this->activeCollections[$backupLogId])) {
                $this->startCollection($backupLogId);
            }

            $this->activeCollections[$backupLogId]['transfer_start_times'][$fileName] = [
                'start_time' => microtime(true),
                'file_size' => $fileSize,
            ];

            // Record file transfer start metric
            $this->recordMetric($backupLogId, 'file_transfer', 'transfer_start', 1, 'count', [
                'file_name' => $fileName,
                'file_size' => $fileSize,
            ]);

        } catch (Exception $e) {
            Log::error("Failed to record transfer start: " . $e->getMessage());
        }
    }

    /**
     * Record transfer completion for a file.
     */
    public function recordTransferComplete(int $backupLogId, string $fileName): void
    {
        try {
            if (!isset($this->activeCollections[$backupLogId]['transfer_start_times'][$fileName])) {
                Log::warning("No transfer start time found for file: {$fileName}");
                return;
            }

            $startData = $this->activeCollections[$backupLogId]['transfer_start_times'][$fileName];
            $duration = microtime(true) - $startData['start_time'];
            $fileSize = $startData['file_size'];
            
            // Calculate transfer speed in MB/s
            $transferSpeedMbps = $fileSize > 0 ? ($fileSize / (1024 * 1024)) / $duration : 0;

            // Record metrics
            $this->recordMetric($backupLogId, 'file_transfer', 'transfer_duration', $duration * 1000, 'ms', [
                'file_name' => $fileName,
                'file_size' => $fileSize,
            ]);

            $this->recordMetric($backupLogId, 'file_transfer', 'transfer_speed', $transferSpeedMbps, 'MB/s', [
                'file_name' => $fileName,
                'file_size' => $fileSize,
            ]);

            // Clean up
            unset($this->activeCollections[$backupLogId]['transfer_start_times'][$fileName]);

        } catch (Exception $e) {
            Log::error("Failed to record transfer complete: " . $e->getMessage());
        }
    }

    /**
     * Record compression start.
     */
    public function recordCompressionStart(int $backupLogId, int $originalSize): void
    {
        try {
            if (!isset($this->activeCollections[$backupLogId])) {
                $this->startCollection($backupLogId);
            }

            $this->activeCollections[$backupLogId]['compression_start_time'] = microtime(true);
            $this->activeCollections[$backupLogId]['compression_original_size'] = $originalSize;

            $this->recordMetric($backupLogId, 'compression', 'compression_start', 1, 'count', [
                'original_size' => $originalSize,
            ]);

        } catch (Exception $e) {
            Log::error("Failed to record compression start: " . $e->getMessage());
        }
    }

    /**
     * Record compression completion.
     */
    public function recordCompressionComplete(int $backupLogId, int $compressedSize): void
    {
        try {
            if (!isset($this->activeCollections[$backupLogId]['compression_start_time'])) {
                Log::warning("No compression start time found for backup log: {$backupLogId}");
                return;
            }

            $startTime = $this->activeCollections[$backupLogId]['compression_start_time'];
            $originalSize = $this->activeCollections[$backupLogId]['compression_original_size'];
            $duration = microtime(true) - $startTime;
            
            // Calculate compression ratio
            $compressionRatio = $originalSize > 0 ? (1 - $compressedSize / $originalSize) : 0;
            $compressionPercentage = $compressionRatio * 100;

            // Record metrics
            $this->recordMetric($backupLogId, 'compression', 'compression_duration', $duration * 1000, 'ms', [
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
            ]);

            $this->recordMetric($backupLogId, 'compression', 'compression_ratio', $compressionRatio, 'ratio', [
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
            ]);

            $this->recordMetric($backupLogId, 'compression', 'compression_percentage', $compressionPercentage, '%', [
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
            ]);

            // Clean up
            unset($this->activeCollections[$backupLogId]['compression_start_time']);
            unset($this->activeCollections[$backupLogId]['compression_original_size']);

        } catch (Exception $e) {
            Log::error("Failed to record compression complete: " . $e->getMessage());
        }
    }

    /**
     * Record encryption start.
     */
    public function recordEncryptionStart(int $backupLogId, int $fileSize): void
    {
        try {
            if (!isset($this->activeCollections[$backupLogId])) {
                $this->startCollection($backupLogId);
            }

            $this->activeCollections[$backupLogId]['encryption_start_time'] = microtime(true);
            $this->activeCollections[$backupLogId]['encryption_file_size'] = $fileSize;

            $this->recordMetric($backupLogId, 'encryption', 'encryption_start', 1, 'count', [
                'file_size' => $fileSize,
            ]);

        } catch (Exception $e) {
            Log::error("Failed to record encryption start: " . $e->getMessage());
        }
    }

    /**
     * Record encryption completion.
     */
    public function recordEncryptionComplete(int $backupLogId): void
    {
        try {
            if (!isset($this->activeCollections[$backupLogId]['encryption_start_time'])) {
                Log::warning("No encryption start time found for backup log: {$backupLogId}");
                return;
            }

            $startTime = $this->activeCollections[$backupLogId]['encryption_start_time'];
            $fileSize = $this->activeCollections[$backupLogId]['encryption_file_size'];
            $duration = microtime(true) - $startTime;

            // Record metrics
            $this->recordMetric($backupLogId, 'encryption', 'encryption_duration', $duration * 1000, 'ms', [
                'file_size' => $fileSize,
            ]);

            // Clean up
            unset($this->activeCollections[$backupLogId]['encryption_start_time']);
            unset($this->activeCollections[$backupLogId]['encryption_file_size']);

        } catch (Exception $e) {
            Log::error("Failed to record encryption complete: " . $e->getMessage());
        }
    }

    /**
     * Record system metrics.
     */
    public function recordSystemMetrics(int $backupLogId): void
    {
        try {
            // Memory usage
            $memoryUsage = memory_get_usage(true);
            $peakMemory = memory_get_peak_usage(true);
            
            $this->recordMetric($backupLogId, 'system', 'memory_usage', $memoryUsage, 'bytes');
            $this->recordMetric($backupLogId, 'system', 'peak_memory', $peakMemory, 'bytes');

            // CPU load (if available on Linux)
            if (function_exists('sys_getloadavg')) {
                $load = sys_getloadavg();
                if ($load !== false) {
                    $this->recordMetric($backupLogId, 'system', 'cpu_load_1min', $load[0], 'load');
                    $this->recordMetric($backupLogId, 'system', 'cpu_load_5min', $load[1], 'load');
                    $this->recordMetric($backupLogId, 'system', 'cpu_load_15min', $load[2], 'load');
                }
            }

            // Disk space (for backup destination)
            $backupLog = BackupLog::find($backupLogId);
            if ($backupLog && $backupLog->backup_path) {
                $diskFree = disk_free_space(dirname($backupLog->backup_path));
                $diskTotal = disk_total_space(dirname($backupLog->backup_path));
                
                if ($diskFree !== false && $diskTotal !== false) {
                    $this->recordMetric($backupLogId, 'system', 'disk_free', $diskFree, 'bytes');
                    $this->recordMetric($backupLogId, 'system', 'disk_total', $diskTotal, 'bytes');
                    $this->recordMetric($backupLogId, 'system', 'disk_usage_percentage', 
                        (($diskTotal - $diskFree) / $diskTotal) * 100, '%');
                }
            }

        } catch (Exception $e) {
            Log::error("Failed to record system metrics: " . $e->getMessage());
        }
    }

    /**
     * Get performance report for a backup job.
     */
    public function getPerformanceReport(int $backupLogId): array
    {
        try {
            $metrics = BackupMetrics::where('backup_log_id', $backupLogId)->get();
            
            $report = [
                'backup_log_id' => $backupLogId,
                'total_metrics' => $metrics->count(),
                'metrics_by_type' => [],
                'summary' => [],
            ];

            // Group metrics by type
            foreach ($metrics as $metric) {
                $type = $metric->metric_type;
                if (!isset($report['metrics_by_type'][$type])) {
                    $report['metrics_by_type'][$type] = [];
                }
                $report['metrics_by_type'][$type][] = [
                    'name' => $metric->metric_name,
                    'value' => $metric->metric_value,
                    'unit' => $metric->metric_unit,
                    'measured_at' => $metric->measured_at,
                    'metadata' => $metric->metric_metadata,
                ];
            }

            // Generate summary statistics
            $report['summary'] = $this->generateSummaryStats($metrics);

            return $report;

        } catch (Exception $e) {
            Log::error("Failed to get performance report: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Record a metric.
     */
    protected function recordMetric(int $backupLogId, string $type, string $name, float $value, 
                                   string $unit = null, array $metadata = []): void
    {
        try {
            BackupMetrics::create([
                'backup_log_id' => $backupLogId,
                'metric_type' => $type,
                'metric_name' => $name,
                'metric_value' => $value,
                'metric_unit' => $unit,
                'metric_metadata' => $metadata,
                'measured_at' => now(),
            ]);

        } catch (Exception $e) {
            Log::error("Failed to record metric: " . $e->getMessage());
        }
    }

    /**
     * Generate summary statistics from metrics.
     */
    protected function generateSummaryStats($metrics): array
    {
        $summary = [];

        // Calculate averages for transfer speeds
        $transferSpeeds = $metrics->where('metric_name', 'transfer_speed')->pluck('metric_value');
        if ($transferSpeeds->count() > 0) {
            $summary['avg_transfer_speed'] = [
                'value' => $transferSpeeds->avg(),
                'unit' => 'MB/s',
                'count' => $transferSpeeds->count(),
            ];
        }

        // Get compression ratio
        $compressionRatio = $metrics->where('metric_name', 'compression_ratio')->first();
        if ($compressionRatio) {
            $summary['compression_ratio'] = [
                'value' => $compressionRatio->metric_value,
                'unit' => 'ratio',
            ];
        }

        // Get total durations
        $totalDuration = $metrics->where('metric_name', 'compression_duration')
            ->sum('metric_value') + $metrics->where('metric_name', 'encryption_duration')
            ->sum('metric_value');
        
        if ($totalDuration > 0) {
            $summary['total_processing_time'] = [
                'value' => $totalDuration,
                'unit' => 'ms',
            ];
        }

        return $summary;
    }
}
