import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, HardDrive } from 'lucide-react';

interface BackupServer {
    id: number;
    name: string;
    protocol: 'ftp' | 'sftp';
    ip_address: string;
    port: number;
    username: string;
    password?: string;
    base_directory: string;
    description?: string;
    is_active: boolean;
    connection_options?: any;
}

interface Props {
    backupServer: BackupServer;
}

export default function BackupServerEdit({ backupServer }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: backupServer.name || '',
        protocol: backupServer.protocol || 'sftp' as 'ftp' | 'sftp',
        ip_address: backupServer.ip_address || '',
        port: backupServer.port?.toString() || '',
        username: backupServer.username || '',
        password: '',
        base_directory: backupServer.base_directory || '/',
        description: backupServer.description || '',
        is_active: backupServer.is_active ?? true,
        connection_options: backupServer.connection_options || {},
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/backup-servers/${backupServer.id}`);
    };

    const handleProtocolChange = (protocol: 'ftp' | 'sftp') => {
        setData('protocol', protocol);
        // Set default port based on protocol if current port is empty or default
        if (!data.port || data.port === '21' || data.port === '22') {
            setData('port', protocol === 'ftp' ? '21' : '22');
        }
    };

    return (
        <AppLayout>
            <Head title={`Edit Backup Server: ${backupServer.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href="/backup-servers">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Backup Servers
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Edit Backup Server</h1>
                            <p className="text-muted-foreground">
                                Update FTP/SFTP server configuration and settings
                            </p>
                        </div>
                    </div>
                </div>

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <HardDrive className="mr-2 h-5 w-5" />
                            Server Configuration
                        </CardTitle>
                        <CardDescription>
                            Update the FTP/SFTP server details and connection settings
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Basic Information */}
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Server Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Production Backup Server"
                                            className={errors.name ? 'border-destructive' : ''}
                                        />
                                        {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="protocol">Protocol *</Label>
                                        <Select
                                            value={data.protocol}
                                            onValueChange={handleProtocolChange}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select protocol" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="ftp">FTP</SelectItem>
                                                <SelectItem value="sftp">SFTP (Secure)</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.protocol && <p className="text-sm text-destructive">{errors.protocol}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="ip_address">Server Address *</Label>
                                        <Input
                                            id="ip_address"
                                            value={data.ip_address}
                                            onChange={(e) => setData('ip_address', e.target.value)}
                                            placeholder="e.g., ************* or ftp.example.com"
                                            className={errors.ip_address ? 'border-destructive' : ''}
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            Enter an IP address (*************) or domain name (ftp.example.com)
                                        </p>
                                        {errors.ip_address && <p className="text-sm text-destructive">{errors.ip_address}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="port">Port</Label>
                                        <Input
                                            id="port"
                                            type="number"
                                            value={data.port}
                                            onChange={(e) => setData('port', e.target.value)}
                                            placeholder={data.protocol === 'ftp' ? '21' : '22'}
                                            min="1"
                                            max="65535"
                                            className={errors.port ? 'border-destructive' : ''}
                                        />
                                        {errors.port && <p className="text-sm text-destructive">{errors.port}</p>}
                                        <p className="text-xs text-muted-foreground">
                                            Leave empty to use default port ({data.protocol === 'ftp' ? '21' : '22'})
                                        </p>
                                    </div>
                                </div>

                                {/* Authentication & Paths */}
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="username">Username *</Label>
                                        <Input
                                            id="username"
                                            value={data.username}
                                            onChange={(e) => setData('username', e.target.value)}
                                            placeholder="backup_user"
                                            className={errors.username ? 'border-destructive' : ''}
                                        />
                                        {errors.username && <p className="text-sm text-destructive">{errors.username}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password">New Password (leave empty to keep current)</Label>
                                        <Input
                                            id="password"
                                            type="password"
                                            value={data.password}
                                            onChange={(e) => setData('password', e.target.value)}
                                            placeholder="Enter new password"
                                            className={errors.password ? 'border-destructive' : ''}
                                        />
                                        {errors.password && <p className="text-sm text-destructive">{errors.password}</p>}
                                        <p className="text-xs text-muted-foreground">
                                            Leave empty to keep the current password
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="base_directory">Base Directory *</Label>
                                        <Input
                                            id="base_directory"
                                            value={data.base_directory}
                                            onChange={(e) => setData('base_directory', e.target.value)}
                                            placeholder="/backups"
                                            className={errors.base_directory ? 'border-destructive' : ''}
                                        />
                                        {errors.base_directory && <p className="text-sm text-destructive">{errors.base_directory}</p>}
                                        <p className="text-xs text-muted-foreground">
                                            Directory where backups will be stored on the server
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="description">Description</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            placeholder="Optional description for this backup server"
                                            rows={3}
                                            className={errors.description ? 'border-destructive' : ''}
                                        />
                                        {errors.description && <p className="text-sm text-destructive">{errors.description}</p>}
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="is_active"
                                            checked={data.is_active}
                                            onCheckedChange={(checked) => setData('is_active', checked)}
                                        />
                                        <Label htmlFor="is_active">Server is active</Label>
                                    </div>
                                </div>
                            </div>

                            {/* Protocol Information */}
                            <div className="p-4 bg-muted rounded-lg">
                                <h4 className="font-medium mb-2">Protocol Information</h4>
                                {data.protocol === 'ftp' ? (
                                    <div className="text-sm text-muted-foreground">
                                        <p>• FTP (File Transfer Protocol) - Standard file transfer</p>
                                        <p>• Default port: 21</p>
                                        <p>• ⚠️ Data transmitted in plain text (less secure)</p>
                                    </div>
                                ) : (
                                    <div className="text-sm text-muted-foreground">
                                        <p>• SFTP (SSH File Transfer Protocol) - Secure file transfer</p>
                                        <p>• Default port: 22</p>
                                        <p>• ✅ Encrypted data transmission (recommended)</p>
                                    </div>
                                )}
                            </div>

                            {/* Submit Button */}
                            <div className="flex items-center justify-end space-x-4 pt-6 border-t">
                                <Link href="/backup-servers">
                                    <Button variant="outline" type="button">
                                        Cancel
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    <Save className="mr-2 h-4 w-4" />
                                    {processing ? 'Updating...' : 'Update Server'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
