<?php

namespace App\Services\Connections;

use App\Models\SourceServer;
use App\Models\BackupServer;
use Illuminate\Support\Facades\Log;
use Exception;

class FtpConnection implements ConnectionInterface
{
    protected $ftp = null;
    protected string $lastError = '';
    protected int $timeout = 30;
    protected SourceServer|BackupServer|null $server = null;
    protected bool $useSSL = false;
    protected bool $passiveMode = true;

    /**
     * Connect to the specified server via FTP.
     */
    public function connect(SourceServer|BackupServer $server): bool
    {
        try {
            $this->server = $server;
            $this->lastError = '';

            // Determine if we should use SSL/TLS
            $this->useSSL = $this->shouldUseSSL($server);

            // Create FTP connection
            if ($this->useSSL) {
                $this->ftp = ftp_ssl_connect($server->ip_address, $server->port ?: 21, $this->timeout);
            } else {
                $this->ftp = ftp_connect($server->ip_address, $server->port ?: 21, $this->timeout);
            }

            if (!$this->ftp) {
                $this->lastError = "Failed to establish FTP connection to {$server->name}";
                Log::warning($this->lastError);
                return false;
            }

            // Set passive mode
            ftp_pasv($this->ftp, $this->passiveMode);

            // Authenticate
            if (!ftp_login($this->ftp, $server->username, $server->password)) {
                $this->lastError = "FTP authentication failed for server {$server->name}";
                Log::warning($this->lastError);
                ftp_close($this->ftp);
                $this->ftp = null;
                return false;
            }

            Log::info("Successfully connected to server via FTP: {$server->name}");
            return true;

        } catch (Exception $e) {
            $this->lastError = "FTP connection failed: " . $e->getMessage();
            Log::error("Failed to connect to server {$server->name} via FTP: " . $e->getMessage());
            
            if ($this->ftp) {
                ftp_close($this->ftp);
                $this->ftp = null;
            }
            
            return false;
        }
    }

    /**
     * Determine if SSL should be used based on server configuration.
     */
    protected function shouldUseSSL(SourceServer|BackupServer $server): bool
    {
        if ($server instanceof BackupServer) {
            return $server->protocol === 'ftps' || 
                   (isset($server->connection_options['use_ssl']) && $server->connection_options['use_ssl']);
        }
        
        // For source servers, check connection options
        return isset($server->connection_options['use_ssl']) && $server->connection_options['use_ssl'];
    }

    /**
     * Upload a file to the remote server.
     */
    public function uploadFile(string $localPath, string $remotePath, callable $progressCallback = null): bool
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            // Create remote directory if it doesn't exist
            $remoteDir = dirname($remotePath);
            if ($remoteDir !== '.' && $remoteDir !== '/') {
                $this->createDirectory($remoteDir, 0755, true);
            }

            $result = ftp_put($this->ftp, $remotePath, $localPath, FTP_BINARY);
            
            if (!$result) {
                $this->lastError = "FTP upload failed for file: $localPath";
                Log::error($this->lastError);
            }
            
            return $result;

        } catch (Exception $e) {
            $this->lastError = "File upload failed: " . $e->getMessage();
            Log::error("FTP upload failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Download a file from the remote server.
     */
    public function downloadFile(string $remotePath, string $localPath, callable $progressCallback = null): bool
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            // Create local directory if it doesn't exist
            $localDir = dirname($localPath);
            if (!is_dir($localDir)) {
                mkdir($localDir, 0755, true);
            }

            $result = ftp_get($this->ftp, $localPath, $remotePath, FTP_BINARY);
            
            if (!$result) {
                $this->lastError = "FTP download failed for file: $remotePath";
                Log::error($this->lastError);
            }
            
            return $result;

        } catch (Exception $e) {
            $this->lastError = "File download failed: " . $e->getMessage();
            Log::error("FTP download failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * List directory contents.
     */
    public function listDirectory(string $path = '.'): array
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            $listing = ftp_nlist($this->ftp, $path);
            return is_array($listing) ? $listing : [];
        } catch (Exception $e) {
            $this->lastError = "Directory listing failed: " . $e->getMessage();
            Log::error("FTP directory listing failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get detailed directory listing.
     */
    public function listDirectoryDetailed(string $path = '.'): array
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            $listing = ftp_rawlist($this->ftp, $path);
            if (!is_array($listing)) {
                return [];
            }

            $files = [];
            foreach ($listing as $line) {
                $files[] = $this->parseRawListLine($line);
            }
            
            return array_filter($files);
        } catch (Exception $e) {
            $this->lastError = "Detailed directory listing failed: " . $e->getMessage();
            Log::error("FTP detailed directory listing failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Parse a raw list line into file information.
     */
    protected function parseRawListLine(string $line): ?array
    {
        // Basic parsing of Unix-style ls -l output
        if (preg_match('/^([drwx-]+)\s+\d+\s+\S+\s+\S+\s+(\d+)\s+(\S+\s+\S+\s+\S+)\s+(.+)$/', $line, $matches)) {
            return [
                'permissions' => $matches[1],
                'size' => (int)$matches[2],
                'date' => $matches[3],
                'name' => $matches[4],
                'type' => $matches[1][0] === 'd' ? 'directory' : 'file'
            ];
        }
        
        return null;
    }

    /**
     * Create a directory.
     */
    public function createDirectory(string $path, int $mode = 0755, bool $recursive = false): bool
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            if (!$recursive) {
                return ftp_mkdir($this->ftp, $path) !== false;
            }

            // Create directories recursively
            $parts = explode('/', trim($path, '/'));
            $currentPath = '';
            
            foreach ($parts as $part) {
                if (empty($part)) continue;
                
                $currentPath .= '/' . $part;
                
                // Check if directory exists
                if (!$this->directoryExists($currentPath)) {
                    if (ftp_mkdir($this->ftp, $currentPath) === false) {
                        $this->lastError = "Failed to create directory: $currentPath";
                        return false;
                    }
                }
            }
            
            return true;

        } catch (Exception $e) {
            $this->lastError = "Directory creation failed: " . $e->getMessage();
            Log::error("FTP directory creation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if a directory exists.
     */
    protected function directoryExists(string $path): bool
    {
        $currentDir = ftp_pwd($this->ftp);
        $result = @ftp_chdir($this->ftp, $path);
        @ftp_chdir($this->ftp, $currentDir);
        return $result;
    }

    /**
     * Delete a file.
     */
    public function deleteFile(string $path): bool
    {
        if (!$this->isConnected()) {
            throw new Exception("Not connected to server");
        }

        try {
            $result = ftp_delete($this->ftp, $path);
            
            if (!$result) {
                $this->lastError = "Failed to delete file: $path";
                Log::error($this->lastError);
            }
            
            return $result;
        } catch (Exception $e) {
            $this->lastError = "File deletion failed: " . $e->getMessage();
            Log::error("FTP file deletion failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if a file exists.
     */
    public function exists(string $path): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            $size = ftp_size($this->ftp, $path);
            return $size !== -1;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get file size.
     */
    public function getFileSize(string $path): int
    {
        if (!$this->isConnected()) {
            return 0;
        }

        try {
            $size = ftp_size($this->ftp, $path);
            return $size > 0 ? $size : 0;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Set passive mode.
     */
    public function setPassiveMode(bool $passive): void
    {
        $this->passiveMode = $passive;
        if ($this->ftp) {
            ftp_pasv($this->ftp, $passive);
        }
    }

    /**
     * Disconnect from the server.
     */
    public function disconnect(): void
    {
        if ($this->ftp) {
            ftp_close($this->ftp);
            $this->ftp = null;
        }
        $this->server = null;
        Log::debug("FTP connection disconnected");
    }

    /**
     * Check if currently connected to the server.
     */
    public function isConnected(): bool
    {
        return $this->ftp !== null && $this->ftp !== false;
    }

    /**
     * Test the connection to verify it's working.
     */
    public function testConnection(): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            // Test by getting current directory
            $pwd = ftp_pwd($this->ftp);
            return $pwd !== false;
        } catch (Exception $e) {
            $this->lastError = "Connection test failed: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Get the last error message if any.
     */
    public function getLastError(): string
    {
        return $this->lastError;
    }

    /**
     * Get connection timeout in seconds.
     */
    public function getTimeout(): int
    {
        return $this->timeout;
    }

    /**
     * Set connection timeout in seconds.
     */
    public function setTimeout(int $timeout): void
    {
        $this->timeout = $timeout;
    }

    /**
     * Get connection information for debugging.
     */
    public function getConnectionInfo(): array
    {
        if (!$this->server) {
            return [];
        }

        return [
            'type' => 'ftp',
            'host' => $this->server->ip_address,
            'port' => $this->server->port ?: 21,
            'username' => $this->server->username,
            'connected' => $this->isConnected(),
            'timeout' => $this->timeout,
            'use_ssl' => $this->useSSL,
            'passive_mode' => $this->passiveMode
        ];
    }
}
