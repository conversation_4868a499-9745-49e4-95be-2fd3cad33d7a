#!/usr/bin/env php
<?php

/**
 * DevOps Backup System - Connection Test Script
 * 
 * This script tests the connection testing system to ensure it's working properly.
 */

require __DIR__ . '/../vendor/autoload.php';

use App\Models\SourceServer;
use App\Models\BackupServer;
use App\Services\ConnectionStatusService;
use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 DevOps Connection Test System Verification\n";
echo "============================================\n\n";

// Test 1: Check if servers exist
echo "1. Checking for test servers...\n";
$sourceServers = SourceServer::take(2)->get();
$backupServers = BackupServer::take(2)->get();

if ($sourceServers->isEmpty()) {
    echo "   ❌ No source servers found. Please create some test servers first.\n";
    exit(1);
}

if ($backupServers->isEmpty()) {
    echo "   ❌ No backup servers found. Please create some test servers first.\n";
    exit(1);
}

echo "   ✅ Found {$sourceServers->count()} source servers and {$backupServers->count()} backup servers\n\n";

// Test 2: Test ConnectionStatusService
echo "2. Testing ConnectionStatusService...\n";
$statusService = app(ConnectionStatusService::class);

try {
    $testServer = $sourceServers->first();
    echo "   Testing with source server: {$testServer->name}\n";
    
    $testId = $statusService->startConnectionTest($testServer);
    echo "   ✅ Connection test started with ID: {$testId}\n";
    
    // Wait a moment and check status
    sleep(1);
    $status = $statusService->getTestStatus($testId);
    echo "   📊 Test status: " . json_encode($status, JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "   ❌ Error testing ConnectionStatusService: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Check queue status
echo "3. Checking queue status...\n";
$queueCount = \DB::table('jobs')->count();
echo "   📊 Jobs in queue: {$queueCount}\n";

if ($queueCount > 0) {
    $recentJobs = \DB::table('jobs')
        ->orderBy('created_at', 'desc')
        ->limit(3)
        ->get(['queue', 'payload']);
    
    echo "   Recent jobs:\n";
    foreach ($recentJobs as $job) {
        $payload = json_decode($job->payload, true);
        $jobClass = $payload['displayName'] ?? 'Unknown';
        echo "     - [{$job->queue}] {$jobClass}\n";
    }
}

echo "\n";

// Test 4: Check failed jobs
echo "4. Checking failed jobs...\n";
$failedCount = \DB::table('failed_jobs')->count();
if ($failedCount > 0) {
    echo "   ⚠️  Failed jobs: {$failedCount}\n";
    echo "   Run 'php artisan queue:failed' to see details\n";
} else {
    echo "   ✅ No failed jobs\n";
}

echo "\n";

// Test 5: API endpoint test
echo "5. Testing API endpoints...\n";
try {
    $testServer = $backupServers->first();
    echo "   Testing backup server API endpoint for: {$testServer->name}\n";
    
    // Simulate API call
    $url = "http://localhost:8000/api/connection-test/backup-servers/{$testServer->id}";
    echo "   API URL: {$url}\n";
    echo "   ℹ️  Use browser dev tools or curl to test this endpoint\n";
    
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Summary
echo "🎯 Test Summary:\n";
echo "================\n";
echo "✅ Connection test system components are loaded\n";
echo "✅ Database connections are working\n";
echo "✅ Queue system is accessible\n";
echo "\n";
echo "📝 Next steps:\n";
echo "1. Ensure queue worker is running: php artisan queue:work --queue=testing,backup-execution,maintenance,default\n";
echo "2. Test connection via web UI at: http://localhost:8000\n";
echo "3. Monitor queue: php artisan queue:monitor-devops\n";
echo "\n";
echo "🚀 Connection test system verification complete!\n";
