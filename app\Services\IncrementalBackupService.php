<?php

namespace App\Services;

use App\Models\BackupJob;
use App\Models\BackupLog;
use App\Models\SourceServer;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use Exception;

class IncrementalBackupService
{
    protected FileDiscoveryService $fileDiscovery;
    protected IntegrityVerificationService $integrityService;

    public function __construct(
        FileDiscoveryService $fileDiscovery,
        IntegrityVerificationService $integrityService
    ) {
        $this->fileDiscovery = $fileDiscovery;
        $this->integrityService = $integrityService;
    }

    /**
     * Get files that have changed since the last backup.
     */
    public function getChangedFiles(BackupJob $job): array
    {
        try {
            $lastBackup = $this->getLastSuccessfulBackup($job);
            $lastBackupTime = $lastBackup ? $lastBackup->completed_at : null;

            Log::info("Checking for changes since: " . ($lastBackupTime ? $lastBackupTime->toISOString() : 'never'));

            // Discover all files in the source path
            $allFiles = $this->fileDiscovery->discoverFiles($job->sourceServer, $job->source_path);

            // Apply include/exclude patterns
            $filteredFiles = $this->applyJobFilters($job, $allFiles);

            if (!$lastBackupTime) {
                Log::info("No previous backup found, all files are considered changed");
                return $filteredFiles;
            }

            // Get the manifest from the last backup
            $lastManifest = $this->getBackupManifest($lastBackup);

            // Compare current files with last backup manifest
            $changedFiles = $this->compareWithManifest($filteredFiles, $lastManifest, $lastBackupTime);

            Log::info("Found " . count($changedFiles) . " changed files out of " . count($filteredFiles) . " total files");

            return $changedFiles;

        } catch (Exception $e) {
            Log::error("Failed to get changed files: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create a backup manifest for tracking file states.
     */
    public function createBackupManifest(BackupJob $job, array $files): array
    {
        $manifest = [
            'backup_job_id' => $job->id,
            'created_at' => Carbon::now()->toISOString(),
            'source_path' => $job->source_path,
            'total_files' => count($files),
            'total_size' => $this->fileDiscovery->calculateTotalSize($files),
            'files' => []
        ];

        foreach ($files as $file) {
            $manifest['files'][$file['path']] = [
                'size' => $file['size'],
                'mtime' => $file['mtime'],
                'permissions' => $file['permissions'],
                'type' => $file['type'],
                'checksum' => $this->calculateFileChecksum($job->sourceServer, $file['path'])
            ];
        }

        return $manifest;
    }

    /**
     * Save backup manifest to storage.
     */
    public function saveBackupManifest(BackupLog $log, array $manifest): bool
    {
        try {
            $manifestPath = "manifests/backup_{$log->id}_manifest.json";
            $manifestJson = json_encode($manifest, JSON_PRETTY_PRINT);

            Storage::disk('local')->put($manifestPath, $manifestJson);

            // Update backup log with manifest path
            $metadata = $log->metadata ?? [];
            $metadata['manifest_path'] = $manifestPath;
            $log->update(['metadata' => $metadata]);

            Log::info("Backup manifest saved: {$manifestPath}");
            return true;

        } catch (Exception $e) {
            Log::error("Failed to save backup manifest: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Load backup manifest from storage.
     */
    public function getBackupManifest(BackupLog $log): ?array
    {
        try {
            $metadata = $log->metadata ?? [];
            $manifestPath = $metadata['manifest_path'] ?? null;

            if (!$manifestPath || !Storage::disk('local')->exists($manifestPath)) {
                Log::warning("Backup manifest not found for log ID: {$log->id}");
                return null;
            }

            $manifestJson = Storage::disk('local')->get($manifestPath);
            return json_decode($manifestJson, true);

        } catch (Exception $e) {
            Log::error("Failed to load backup manifest: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Calculate delta between current files and last backup.
     */
    public function calculateDelta(array $currentFiles, array $lastManifest): array
    {
        $delta = [
            'added' => [],
            'modified' => [],
            'deleted' => [],
            'unchanged' => []
        ];

        $lastFiles = $lastManifest['files'] ?? [];

        // Check for added and modified files
        foreach ($currentFiles as $file) {
            $path = $file['path'];
            
            if (!isset($lastFiles[$path])) {
                // File is new
                $delta['added'][] = $file;
            } else {
                $lastFile = $lastFiles[$path];
                
                // Check if file has changed
                if ($this->hasFileChanged($file, $lastFile)) {
                    $delta['modified'][] = $file;
                } else {
                    $delta['unchanged'][] = $file;
                }
            }
        }

        // Check for deleted files
        $currentPaths = array_column($currentFiles, 'path');
        foreach ($lastFiles as $path => $lastFile) {
            if (!in_array($path, $currentPaths)) {
                $delta['deleted'][] = [
                    'path' => $path,
                    'size' => $lastFile['size'],
                    'type' => $lastFile['type']
                ];
            }
        }

        Log::info("Delta calculation: " . 
            count($delta['added']) . " added, " .
            count($delta['modified']) . " modified, " .
            count($delta['deleted']) . " deleted, " .
            count($delta['unchanged']) . " unchanged"
        );

        return $delta;
    }

    /**
     * Get the last successful backup for a job.
     */
    protected function getLastSuccessfulBackup(BackupJob $job): ?BackupLog
    {
        return $job->backupLogs()
            ->where('status', 'completed')
            ->orderBy('completed_at', 'desc')
            ->first();
    }

    /**
     * Apply job-specific include/exclude filters.
     */
    protected function applyJobFilters(BackupJob $job, array $files): array
    {
        // Apply exclude patterns
        if ($job->exclude_patterns) {
            $excludePatterns = is_string($job->exclude_patterns) 
                ? json_decode($job->exclude_patterns, true) 
                : $job->exclude_patterns;
            
            if ($excludePatterns) {
                $files = $this->fileDiscovery->applyExcludePatterns($files, $excludePatterns);
            }
        }

        // Apply include patterns
        if ($job->include_patterns) {
            $includePatterns = is_string($job->include_patterns) 
                ? json_decode($job->include_patterns, true) 
                : $job->include_patterns;
            
            if ($includePatterns) {
                $files = $this->fileDiscovery->applyIncludePatterns($files, $includePatterns);
            }
        }

        return $files;
    }

    /**
     * Compare current files with backup manifest.
     */
    protected function compareWithManifest(array $currentFiles, ?array $lastManifest, Carbon $lastBackupTime): array
    {
        if (!$lastManifest) {
            return $currentFiles;
        }

        $changedFiles = [];
        $lastFiles = $lastManifest['files'] ?? [];

        foreach ($currentFiles as $file) {
            $path = $file['path'];
            
            // File is new
            if (!isset($lastFiles[$path])) {
                $changedFiles[] = $file;
                continue;
            }

            $lastFile = $lastFiles[$path];

            // Check if file has changed
            if ($this->hasFileChanged($file, $lastFile)) {
                $changedFiles[] = $file;
            }
        }

        return $changedFiles;
    }

    /**
     * Check if a file has changed compared to its last backup state.
     */
    protected function hasFileChanged(array $currentFile, array $lastFile): bool
    {
        // Check modification time
        if ($currentFile['mtime'] > $lastFile['mtime']) {
            return true;
        }

        // Check file size
        if ($currentFile['size'] !== $lastFile['size']) {
            return true;
        }

        // For critical files, also check checksum
        if ($currentFile['size'] > 0 && $currentFile['size'] < 10485760) { // Files under 10MB
            $currentChecksum = $this->calculateFileChecksum(null, $currentFile['path']);
            if ($currentChecksum && $currentChecksum !== ($lastFile['checksum'] ?? '')) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate checksum for a file.
     */
    protected function calculateFileChecksum(?SourceServer $server, string $filePath): ?string
    {
        try {
            if ($server) {
                return $this->integrityService->calculateRemoteChecksum($server, $filePath);
            }
            return null;
        } catch (Exception $e) {
            Log::warning("Failed to calculate checksum for {$filePath}: " . $e->getMessage());
            return null;
        }
    }
}
