<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BackupServer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'protocol',
        'ip_address',
        'port',
        'username',
        'password',
        'base_directory',
        'description',
        'is_active',
        'last_connection_test',
        'connection_options',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_connection_test' => 'datetime',
        'password' => 'encrypted',
        'connection_options' => 'array',
    ];

    protected $hidden = [
        'password',
    ];

    /**
     * Get the backup jobs for this backup server.
     */
    public function backupJobs(): HasMany
    {
        return $this->hasMany(BackupJob::class);
    }

    /**
     * Get the default port based on protocol.
     */
    public function getDefaultPort(): int
    {
        return match ($this->protocol) {
            'ftp' => 21,
            'sftp' => 22,
            default => 22,
        };
    }

    /**
     * Check if the server uses FTP protocol.
     */
    public function usesFtp(): bool
    {
        return $this->protocol === 'ftp';
    }

    /**
     * Check if the server uses SFTP protocol.
     */
    public function usesSftp(): bool
    {
        return $this->protocol === 'sftp';
    }

    /**
     * Get the connection string for display.
     */
    public function getConnectionStringAttribute(): string
    {
        $port = $this->port ?: $this->getDefaultPort();
        return "{$this->protocol}://{$this->username}@{$this->ip_address}:{$port}{$this->base_directory}";
    }

    /**
     * Get the full backup path.
     */
    public function getFullBackupPath(string $relativePath = ''): string
    {
        $basePath = rtrim($this->base_directory, '/');
        $relativePath = ltrim($relativePath, '/');

        return $relativePath ? "{$basePath}/{$relativePath}" : $basePath;
    }
}
