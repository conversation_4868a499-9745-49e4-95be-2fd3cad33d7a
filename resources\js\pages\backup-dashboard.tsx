import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useDashboardData } from '@/hooks/useDashboardData';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import {
    Server,
    HardDrive,
    Calendar,
    FileText,
    CheckCircle,
    XCircle,
    Clock,
    TrendingUp,
    Activity,
    Wifi,
    AlertTriangle,
    RefreshCw
} from 'lucide-react';

export default function BackupDashboard() {
    const { stats, connectionSummary, isLoading, error, refresh } = useDashboardData();

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return CheckCircle;
            case 'failed':
                return XCircle;
            case 'running':
                return Clock;
            default:
                return Clock;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'default';
            case 'failed':
                return 'destructive';
            case 'running':
                return 'secondary';
            default:
                return 'outline';
        }
    };

    return (
        <AppLayout>
            <Head title="Backup Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Backup Dashboard</h1>
                        <p className="text-muted-foreground">
                            Overview of your backup infrastructure and recent activity
                        </p>
                    </div>
                    <div className="flex space-x-2">
                        <Button variant="outline" onClick={refresh} disabled={isLoading}>
                            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                        <Link href="/backup-jobs/create">
                            <Button>
                                <Calendar className="mr-2 h-4 w-4" />
                                New Backup Job
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Error State */}
                {error && (
                    <Card className="border-red-200 bg-red-50">
                        <CardContent className="pt-6">
                            <div className="flex items-center space-x-2 text-red-600">
                                <AlertTriangle className="h-4 w-4" />
                                <span className="text-sm font-medium">Error loading dashboard data: {error}</span>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Stats Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Source Servers</CardTitle>
                            <Server className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_source_servers}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.active_source_servers} active
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Backup Servers</CardTitle>
                            <HardDrive className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_backup_servers}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.active_backup_servers} active
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Backup Jobs</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_backup_jobs}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.active_backup_jobs} active
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Backups</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_backups}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.successful_backups} successful
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Backup Status Overview */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Successful</CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.successful_backups}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.total_backups > 0 ? Math.round((stats.successful_backups / stats.total_backups) * 100) : 0}% success rate
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Failed</CardTitle>
                            <XCircle className="h-4 w-4 text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{stats.failed_backups}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.total_backups > 0 ? Math.round((stats.failed_backups / stats.total_backups) * 100) : 0}% failure rate
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Running</CardTitle>
                            <Clock className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600">{stats.running_backups}</div>
                            <p className="text-xs text-muted-foreground">
                                Currently in progress
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Health Score</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats.total_backups > 0 ? Math.round((stats.successful_backups / stats.total_backups) * 100) : 100}%
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Overall system health
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Connection Health */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Wifi className="h-5 w-5" />
                            <span>Connection Health</span>
                        </CardTitle>
                        <CardDescription>Server connectivity status overview</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                            <div className="text-center">
                                <div className="text-2xl font-bold">{connectionSummary.total_servers}</div>
                                <p className="text-xs text-muted-foreground">Total Servers</p>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">{connectionSummary.healthy_servers}</div>
                                <p className="text-xs text-muted-foreground">Healthy</p>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-yellow-600">{connectionSummary.warning_servers}</div>
                                <p className="text-xs text-muted-foreground">Warning</p>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-red-600">{connectionSummary.critical_servers}</div>
                                <p className="text-xs text-muted-foreground">Critical</p>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-muted-foreground">{connectionSummary.unknown_servers}</div>
                                <p className="text-xs text-muted-foreground">Unknown</p>
                            </div>
                        </div>

                        {connectionSummary.active_tests > 0 && (
                            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                                <div className="flex items-center space-x-2 text-blue-600">
                                    <Clock className="h-4 w-4 animate-pulse" />
                                    <span className="text-sm font-medium">
                                        {connectionSummary.active_tests} connection test{connectionSummary.active_tests > 1 ? 's' : ''} in progress
                                    </span>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Recent Activity */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Activity className="h-5 w-5" />
                            <span>Recent Backup Activity</span>
                        </CardTitle>
                        <CardDescription>Latest backup job executions</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {stats.recent_jobs && stats.recent_jobs.length > 0 ? (
                            <div className="space-y-3">
                                {stats.recent_jobs.map((job) => {
                                    const StatusIcon = getStatusIcon(job.status);
                                    return (
                                        <div key={job.id} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div className="flex items-center space-x-3">
                                                <StatusIcon className="h-4 w-4" />
                                                <div>
                                                    <p className="font-medium">{job.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {job.source_server.name} • {new Date(job.started_at).toLocaleString()}
                                                    </p>
                                                </div>
                                            </div>
                                            <Badge variant={getStatusColor(job.status)}>
                                                {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                                            </Badge>
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No recent activity</h3>
                                <p className="text-muted-foreground mb-4">
                                    No backup jobs have been executed yet.
                                </p>
                                <Link href="/backup-jobs/create">
                                    <Button>
                                        <Calendar className="mr-2 h-4 w-4" />
                                        Create Your First Backup Job
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>Common backup management tasks</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <Link href="/source-servers">
                                <Button variant="outline" className="w-full justify-start">
                                    <Server className="mr-2 h-4 w-4" />
                                    Manage Source Servers
                                </Button>
                            </Link>
                            <Link href="/backup-servers">
                                <Button variant="outline" className="w-full justify-start">
                                    <HardDrive className="mr-2 h-4 w-4" />
                                    Manage Backup Servers
                                </Button>
                            </Link>
                            <Link href="/backup-jobs">
                                <Button variant="outline" className="w-full justify-start">
                                    <Calendar className="mr-2 h-4 w-4" />
                                    View Backup Jobs
                                </Button>
                            </Link>
                            <Link href="/backup-logs">
                                <Button variant="outline" className="w-full justify-start">
                                    <FileText className="mr-2 h-4 w-4" />
                                    View Backup Logs
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
