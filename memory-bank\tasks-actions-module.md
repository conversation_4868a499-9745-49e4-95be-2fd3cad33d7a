# Actions Module: Task List & Planning

## Overview
Implement a new module to allow users to define, run, and log custom actions (commands) on source servers via SSH, with live output and templates.

---

## Task List

### 1. Database & Models
- [ ] Create Action and ActionLog models and migrations
- [ ] Add relationships to User and SourceServer
- [ ] Seed with example templates (deploy, build, etc.)

### 2. Backend Logic
- [ ] ActionController: CRUD for actions
- [ ] ActionRunController: trigger action, stream output, save logs
- [ ] ActionLogController: view logs
- [ ] ActionRunnerJob: run command via SSH, stream output, update log
- [ ] SSHService: handle SSH connection and command execution
- [ ] Permissions: restrict access to authorized users

### 3. Frontend
- [ ] Actions List page (view, run, edit, delete)
- [ ] Action Create/Edit page (select server, command, template)
- [ ] Action Logs page (per action, per run)
- [ ] TerminalWindow component (live output)
- [ ] ActionForm component
- [ ] ActionLogList component
- [ ] useActionRunner hook (live output streaming)

### 4. Live Output Streaming
- [ ] Implement WebSocket or polling for live output

### 5. Templates
- [ ] Store and manage command templates
- [ ] Allow users to select templates when creating actions

### 6. Security & Testing
- [ ] Validate/sanitize commands
- [ ] Restrict output/logs to authorized users
- [ ] Unit/feature tests for backend
- [ ] UI tests for frontend

---

## Clarifying Questions (for future reference)
1. Should templates be editable by users?
2. Is true real-time (WebSocket) streaming required?
3. Should there be restrictions/warnings for dangerous commands?
4. Should actions be private or shared?
5. Preferred UI for terminal output (modal/page/embedded)?
6. Should users be notified on completion/failure?

---

## Next Step
Begin with **Task 1: Database & Models**
- Create Action and ActionLog models and migrations
- Add relationships to User and SourceServer
- Seed with example templates 