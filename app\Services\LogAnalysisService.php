<?php

namespace App\Services;

use App\Models\BackupLog;
use App\Models\BackupMetrics;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class LogAnalysisService
{
    /**
     * Get recent backup logs.
     */
    public function getRecentLogs(int $hours = 24): array
    {
        try {
            $cutoffTime = now()->subHours($hours);
            
            $logs = BackupLog::where('started_at', '>=', $cutoffTime)
                ->with(['backupJob', 'progressRecords', 'metrics'])
                ->orderBy('started_at', 'desc')
                ->get();

            return $logs->map(function ($log) {
                return [
                    'id' => $log->id,
                    'job_name' => $log->backupJob->name ?? 'Unknown',
                    'status' => $log->status,
                    'started_at' => $log->started_at,
                    'completed_at' => $log->completed_at,
                    'duration_seconds' => $log->duration_seconds,
                    'backup_size_bytes' => $log->backup_size_bytes,
                    'files_count' => $log->files_count,
                    'error_message' => $log->error_message,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error("Failed to get recent logs: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Calculate success rate for a given period.
     */
    public function calculateSuccessRate(int $days = 7): float
    {
        try {
            $cutoffTime = now()->subDays($days);
            
            $totalJobs = BackupLog::where('started_at', '>=', $cutoffTime)->count();
            $successfulJobs = BackupLog::where('started_at', '>=', $cutoffTime)
                ->where('status', 'completed')
                ->count();

            if ($totalJobs === 0) {
                return 0.0;
            }

            return round(($successfulJobs / $totalJobs) * 100, 2);

        } catch (Exception $e) {
            Log::error("Failed to calculate success rate: " . $e->getMessage());
            return 0.0;
        }
    }

    /**
     * Detect error patterns in backup logs.
     */
    public function detectErrorPatterns(int $days = 30): array
    {
        try {
            $cutoffTime = now()->subDays($days);
            
            $errorLogs = BackupLog::where('started_at', '>=', $cutoffTime)
                ->where('status', 'failed')
                ->whereNotNull('error_message')
                ->get();

            $patterns = [];
            $errorCounts = [];

            foreach ($errorLogs as $log) {
                $errorMessage = $log->error_message;
                
                // Extract common error patterns
                $pattern = $this->extractErrorPattern($errorMessage);
                
                if (!isset($errorCounts[$pattern])) {
                    $errorCounts[$pattern] = [
                        'pattern' => $pattern,
                        'count' => 0,
                        'examples' => [],
                        'affected_jobs' => [],
                    ];
                }
                
                $errorCounts[$pattern]['count']++;
                
                if (count($errorCounts[$pattern]['examples']) < 3) {
                    $errorCounts[$pattern]['examples'][] = $errorMessage;
                }
                
                if (!in_array($log->backupJob->name ?? 'Unknown', $errorCounts[$pattern]['affected_jobs'])) {
                    $errorCounts[$pattern]['affected_jobs'][] = $log->backupJob->name ?? 'Unknown';
                }
            }

            // Sort by frequency
            uasort($errorCounts, function ($a, $b) {
                return $b['count'] <=> $a['count'];
            });

            return array_values($errorCounts);

        } catch (Exception $e) {
            Log::error("Failed to detect error patterns: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get performance trends over time.
     */
    public function getPerformanceTrends(int $days = 30): array
    {
        try {
            $cutoffTime = now()->subDays($days);
            
            // Get daily performance metrics
            $dailyMetrics = BackupLog::where('started_at', '>=', $cutoffTime)
                ->where('status', 'completed')
                ->select([
                    DB::raw('DATE(started_at) as date'),
                    DB::raw('AVG(duration_seconds) as avg_duration'),
                    DB::raw('AVG(backup_size_bytes) as avg_size'),
                    DB::raw('COUNT(*) as backup_count'),
                    DB::raw('SUM(backup_size_bytes) as total_size'),
                ])
                ->groupBy(DB::raw('DATE(started_at)'))
                ->orderBy('date')
                ->get();

            // Get transfer speed trends from metrics
            $speedTrends = BackupMetrics::whereHas('backupLog', function ($query) use ($cutoffTime) {
                    $query->where('started_at', '>=', $cutoffTime);
                })
                ->where('metric_name', 'transfer_speed')
                ->select([
                    DB::raw('DATE(measured_at) as date'),
                    DB::raw('AVG(metric_value) as avg_speed'),
                    DB::raw('MAX(metric_value) as max_speed'),
                    DB::raw('MIN(metric_value) as min_speed'),
                ])
                ->groupBy(DB::raw('DATE(measured_at)'))
                ->orderBy('date')
                ->get();

            return [
                'daily_performance' => $dailyMetrics->toArray(),
                'transfer_speeds' => $speedTrends->toArray(),
                'summary' => $this->calculateTrendSummary($dailyMetrics, $speedTrends),
            ];

        } catch (Exception $e) {
            Log::error("Failed to get performance trends: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get backup job health status.
     */
    public function getJobHealthStatus(): array
    {
        try {
            $jobs = DB::table('backup_jobs')
                ->leftJoin('backup_logs', function ($join) {
                    $join->on('backup_jobs.id', '=', 'backup_logs.backup_job_id')
                        ->where('backup_logs.started_at', '>=', now()->subDays(7));
                })
                ->select([
                    'backup_jobs.id',
                    'backup_jobs.name',
                    'backup_jobs.status as job_status',
                    'backup_jobs.last_run',
                    DB::raw('COUNT(backup_logs.id) as recent_runs'),
                    DB::raw('SUM(CASE WHEN backup_logs.status = "completed" THEN 1 ELSE 0 END) as successful_runs'),
                    DB::raw('SUM(CASE WHEN backup_logs.status = "failed" THEN 1 ELSE 0 END) as failed_runs'),
                    DB::raw('MAX(backup_logs.started_at) as last_backup_attempt'),
                ])
                ->groupBy('backup_jobs.id', 'backup_jobs.name', 'backup_jobs.status', 'backup_jobs.last_run')
                ->get();

            return $jobs->map(function ($job) {
                $successRate = $job->recent_runs > 0 ? 
                    round(($job->successful_runs / $job->recent_runs) * 100, 2) : 0;

                $health = 'healthy';
                if ($job->recent_runs === 0) {
                    $health = 'inactive';
                } elseif ($successRate < 50) {
                    $health = 'critical';
                } elseif ($successRate < 80) {
                    $health = 'warning';
                }

                return [
                    'job_id' => $job->id,
                    'job_name' => $job->name,
                    'job_status' => $job->job_status,
                    'health_status' => $health,
                    'success_rate' => $successRate,
                    'recent_runs' => $job->recent_runs,
                    'successful_runs' => $job->successful_runs,
                    'failed_runs' => $job->failed_runs,
                    'last_run' => $job->last_run,
                    'last_backup_attempt' => $job->last_backup_attempt,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error("Failed to get job health status: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get storage usage analysis.
     */
    public function getStorageAnalysis(int $days = 30): array
    {
        try {
            $cutoffTime = now()->subDays($days);
            
            $storageData = BackupLog::where('started_at', '>=', $cutoffTime)
                ->where('status', 'completed')
                ->whereNotNull('backup_size_bytes')
                ->with('backupJob.backupServer')
                ->get();

            $analysis = [
                'total_backups' => $storageData->count(),
                'total_size_bytes' => $storageData->sum('backup_size_bytes'),
                'average_size_bytes' => $storageData->avg('backup_size_bytes'),
                'by_server' => [],
                'by_job' => [],
                'growth_trend' => [],
            ];

            // Group by backup server
            $byServer = $storageData->groupBy(function ($log) {
                return $log->backupJob->backupServer->name ?? 'Unknown';
            });

            foreach ($byServer as $serverName => $logs) {
                $analysis['by_server'][$serverName] = [
                    'backup_count' => $logs->count(),
                    'total_size_bytes' => $logs->sum('backup_size_bytes'),
                    'average_size_bytes' => $logs->avg('backup_size_bytes'),
                ];
            }

            // Group by job
            $byJob = $storageData->groupBy(function ($log) {
                return $log->backupJob->name ?? 'Unknown';
            });

            foreach ($byJob as $jobName => $logs) {
                $analysis['by_job'][$jobName] = [
                    'backup_count' => $logs->count(),
                    'total_size_bytes' => $logs->sum('backup_size_bytes'),
                    'average_size_bytes' => $logs->avg('backup_size_bytes'),
                ];
            }

            // Calculate daily growth trend
            $dailyGrowth = $storageData->groupBy(function ($log) {
                return $log->started_at->format('Y-m-d');
            });

            foreach ($dailyGrowth as $date => $logs) {
                $analysis['growth_trend'][] = [
                    'date' => $date,
                    'backup_count' => $logs->count(),
                    'total_size_bytes' => $logs->sum('backup_size_bytes'),
                ];
            }

            return $analysis;

        } catch (Exception $e) {
            Log::error("Failed to get storage analysis: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Extract error pattern from error message.
     */
    protected function extractErrorPattern(string $errorMessage): string
    {
        // Common error patterns
        $patterns = [
            '/connection.*timeout/i' => 'Connection Timeout',
            '/connection.*refused/i' => 'Connection Refused',
            '/authentication.*failed/i' => 'Authentication Failed',
            '/permission.*denied/i' => 'Permission Denied',
            '/no.*space.*left/i' => 'Disk Space Full',
            '/file.*not.*found/i' => 'File Not Found',
            '/network.*unreachable/i' => 'Network Unreachable',
            '/ssl.*error/i' => 'SSL Error',
            '/compression.*failed/i' => 'Compression Failed',
            '/encryption.*failed/i' => 'Encryption Failed',
        ];

        foreach ($patterns as $regex => $pattern) {
            if (preg_match($regex, $errorMessage)) {
                return $pattern;
            }
        }

        // If no pattern matches, return first 50 characters
        return substr($errorMessage, 0, 50) . (strlen($errorMessage) > 50 ? '...' : '');
    }

    /**
     * Calculate trend summary statistics.
     */
    protected function calculateTrendSummary($dailyMetrics, $speedTrends): array
    {
        $summary = [];

        if ($dailyMetrics->count() > 1) {
            $firstDay = $dailyMetrics->first();
            $lastDay = $dailyMetrics->last();
            
            $summary['duration_trend'] = $lastDay->avg_duration - $firstDay->avg_duration;
            $summary['size_trend'] = $lastDay->avg_size - $firstDay->avg_size;
        }

        if ($speedTrends->count() > 1) {
            $firstSpeed = $speedTrends->first();
            $lastSpeed = $speedTrends->last();
            
            $summary['speed_trend'] = $lastSpeed->avg_speed - $firstSpeed->avg_speed;
        }

        return $summary;
    }
}
