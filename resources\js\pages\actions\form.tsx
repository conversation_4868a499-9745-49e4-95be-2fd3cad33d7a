import { useF<PERSON>, <PERSON> } from '@inertiajs/react';
import { useEffect, FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardContent, CardFooter, CardTitle, CardDescription } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Terminal } from 'lucide-react';

interface SourceServer {
    id: number;
    name: string;
}

interface Template {
    label: string;
    value: string;
    command: string;
}

interface ActionFormProps {
    action?: any;
    sourceServers: SourceServer[];
    templates: Template[];
}

export default function ActionForm({ action, sourceServers, templates }: ActionFormProps) {
    const isEdit = !!action;
    const { data, setData, post, put, processing, errors, reset } = useForm({
        name: action?.name || '',
        source_server_id: action?.source_server_id?.toString() || '',
        command: action?.command || '',
        template: action?.template || 'custom',
    });

    // Auto-fill command when template changes
    useEffect(() => {
        if (data.template && data.template !== 'custom') {
            const selected = templates.find(t => t.value === data.template);
            if (selected) setData('command', selected.command);
        }
    }, [data.template]);

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        if (isEdit) {
            put(`/actions/${action.id}`);
        } else {
            post('/actions');
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center space-x-4">
                <Link href="/actions">
                    <Button variant="outline" size="sm">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Actions
                    </Button>
                </Link>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">{isEdit ? 'Edit Action' : 'Create Action'}</h1>
                    <p className="text-muted-foreground">
                        {isEdit ? 'Update an existing action for your source servers' : 'Define a new action to run on your source servers'}
                    </p>
                </div>
            </div>
            {/* Form */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center">
                        <Terminal className="mr-2 h-5 w-5" />
                        Action Configuration
                    </CardTitle>
                    <CardDescription>
                        Configure the action name, server, template, and command to run
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Action Name *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={e => setData('name', e.target.value)}
                                        placeholder="Deploy App"
                                        className={errors.name ? 'border-destructive' : ''}
                                    />
                                    {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="source_server_id">Source Server *</Label>
                                    <Select
                                        value={data.source_server_id}
                                        onValueChange={value => setData('source_server_id', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select source server" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {sourceServers.map(server => (
                                                <SelectItem key={server.id} value={server.id.toString()}>
                                                    {server.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.source_server_id && <p className="text-sm text-destructive">{errors.source_server_id}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="template">Template</Label>
                                    <Select
                                        value={data.template}
                                        onValueChange={value => setData('template', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Custom" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="custom">Custom</SelectItem>
                                            {templates.map(t => (
                                                <SelectItem key={t.value} value={t.value}>{t.label}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="command">Command *</Label>
                                    <Textarea
                                        id="command"
                                        value={data.command}
                                        onChange={e => setData('command', e.target.value)}
                                        placeholder="cd /var/www/app && git pull ..."
                                        className={errors.command ? 'border-destructive font-mono' : 'font-mono'}
                                        rows={7}
                                    />
                                    {errors.command && <p className="text-sm text-destructive">{errors.command}</p>}
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-end gap-2 pt-4">
                            <Button type="submit" disabled={processing}>
                                <Save className="w-4 h-4 mr-2" />
                                {processing ? (isEdit ? 'Updating...' : 'Creating...') : (isEdit ? 'Update Action' : 'Create Action')}
                            </Button>
                            <Button type="button" variant="outline" asChild>
                                <Link href="/actions">Cancel</Link>
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    );
} 