import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import ActionForm from './form';

interface SourceServer {
    id: number;
    name: string;
}

interface Template {
    label: string;
    value: string;
    command: string;
}

interface CreateActionProps {
    sourceServers: SourceServer[];
    templates: Template[];
}

export default function CreateAction({ sourceServers = [], templates = [] }: CreateActionProps) {
    return (
        <AppLayout>
            <Head title="Create Action" />
            <div className="max-w-2xl mx-auto py-8">
                <h1 className="text-2xl font-bold mb-4">Create Action</h1>
                <ActionForm sourceServers={sourceServers} templates={templates} />
            </div>
        </AppLayout>
    );
} 