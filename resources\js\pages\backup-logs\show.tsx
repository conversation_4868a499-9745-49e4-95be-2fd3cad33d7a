import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { 
    ArrowLeft, 
    MoreHorizontal, 
    Download, 
    RotateCcw, 
    Trash2, 
    FileText, 
    Clock, 
    Server, 
    HardDrive, 
    CheckCircle,
    XCircle,
    AlertCircle,
    Calendar,
    Archive,
    Shield
} from 'lucide-react';

interface BackupLog {
    id: number;
    status: 'running' | 'completed' | 'failed' | 'cancelled';
    started_at: string;
    completed_at?: string;
    duration_seconds?: number;
    backup_size_bytes?: number;
    backup_path?: string;
    error_message?: string;
    backup_job: {
        id: number;
        name: string;
        source_server: {
            id: number;
            name: string;
            ip_address: string;
        };
        backup_server: {
            id: number;
            name: string;
            protocol: string;
            ip_address: string;
        };
        source_path: string;
        destination_path: string;
        compression_enabled: boolean;
        encryption_enabled: boolean;
    };
}

interface Props {
    backupLog: BackupLog;
}

export default function BackupLogShow({ backupLog }: Props) {
    const handleDownload = () => {
        if (confirm('Download backup file?')) {
            router.post(route('backup-logs.download', backupLog.id));
        }
    };

    const handleRetry = () => {
        if (confirm('Are you sure you want to retry this backup?')) {
            router.post(route('backup-logs.retry', backupLog.id));
        }
    };

    const handleDelete = () => {
        if (confirm('Are you sure you want to delete this backup log?')) {
            router.delete(route('backup-logs.destroy', backupLog.id));
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return CheckCircle;
            case 'failed':
                return XCircle;
            case 'running':
                return Clock;
            case 'cancelled':
                return AlertCircle;
            default:
                return AlertCircle;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'failed':
                return 'bg-red-100 text-red-800';
            case 'running':
                return 'bg-blue-100 text-blue-800';
            case 'cancelled':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatBytes = (bytes?: number) => {
        if (!bytes) return 'N/A';
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const formatDuration = (seconds?: number) => {
        if (!seconds) return 'N/A';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours}h ${minutes}m ${secs}s`;
    };

    const StatusIcon = getStatusIcon(backupLog.status);

    return (
        <AppLayout>
            <Head title={`Backup Log - ${backupLog.backup_job.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('backup-logs.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Backup Logs
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold">Backup Log Details</h1>
                            <p className="text-muted-foreground">{backupLog.backup_job.name}</p>
                        </div>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline">
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            {backupLog.status === 'completed' && backupLog.backup_path && (
                                <DropdownMenuItem onClick={handleDownload}>
                                    <Download className="mr-2 h-4 w-4" />
                                    Download
                                </DropdownMenuItem>
                            )}
                            {backupLog.status === 'failed' && (
                                <DropdownMenuItem onClick={handleRetry}>
                                    <RotateCcw className="mr-2 h-4 w-4" />
                                    Retry
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                {/* Status Overview */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <StatusIcon className="h-5 w-5" />
                                <span>Status</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Badge className={getStatusColor(backupLog.status)}>
                                {backupLog.status.charAt(0).toUpperCase() + backupLog.status.slice(1)}
                            </Badge>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <Archive className="h-5 w-5" />
                                <span>Size</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-lg font-semibold">{formatBytes(backupLog.backup_size_bytes)}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <Clock className="h-5 w-5" />
                                <span>Duration</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-lg font-semibold">{formatDuration(backupLog.duration_seconds)}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center space-x-2">
                                <Calendar className="h-5 w-5" />
                                <span>Started</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm">{new Date(backupLog.started_at).toLocaleString()}</p>
                            {backupLog.completed_at && (
                                <p className="text-xs text-muted-foreground mt-1">
                                    Completed: {new Date(backupLog.completed_at).toLocaleString()}
                                </p>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Error Message */}
                {backupLog.error_message && (
                    <Card className="border-red-200">
                        <CardHeader>
                            <CardTitle className="text-red-600 flex items-center space-x-2">
                                <XCircle className="h-5 w-5" />
                                <span>Error Details</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="bg-red-50 p-4 rounded-lg">
                                <p className="text-red-800 font-mono text-sm">{backupLog.error_message}</p>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Backup Job Details */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <FileText className="h-5 w-5" />
                            <span>Backup Job Information</span>
                        </CardTitle>
                        <CardDescription>
                            <Link 
                                href={route('backup-jobs.show', backupLog.backup_job.id)}
                                className="text-blue-600 hover:underline"
                            >
                                View full backup job details →
                            </Link>
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-6 md:grid-cols-2">
                            <div>
                                <h4 className="font-semibold mb-3 flex items-center space-x-2">
                                    <Server className="h-4 w-4" />
                                    <span>Source Server</span>
                                </h4>
                                <div className="space-y-2">
                                    <div>
                                        <p className="font-medium">{backupLog.backup_job.source_server.name}</p>
                                        <p className="text-sm text-muted-foreground">
                                            {backupLog.backup_job.source_server.ip_address}
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-sm font-medium">Source Path:</p>
                                        <p className="text-sm font-mono bg-muted p-2 rounded">
                                            {backupLog.backup_job.source_path}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 className="font-semibold mb-3 flex items-center space-x-2">
                                    <HardDrive className="h-4 w-4" />
                                    <span>Backup Server</span>
                                </h4>
                                <div className="space-y-2">
                                    <div>
                                        <p className="font-medium">{backupLog.backup_job.backup_server.name}</p>
                                        <p className="text-sm text-muted-foreground">
                                            {backupLog.backup_job.backup_server.protocol} - {backupLog.backup_job.backup_server.ip_address}
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-sm font-medium">Destination Path:</p>
                                        <p className="text-sm font-mono bg-muted p-2 rounded">
                                            {backupLog.backup_job.destination_path}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Backup Configuration */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Shield className="h-5 w-5" />
                            <span>Backup Configuration</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div className="flex items-center space-x-2">
                                <Archive className="h-4 w-4" />
                                <span className="text-sm">Compression:</span>
                                <Badge variant={backupLog.backup_job.compression_enabled ? 'default' : 'secondary'}>
                                    {backupLog.backup_job.compression_enabled ? 'Enabled' : 'Disabled'}
                                </Badge>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Shield className="h-4 w-4" />
                                <span className="text-sm">Encryption:</span>
                                <Badge variant={backupLog.backup_job.encryption_enabled ? 'default' : 'secondary'}>
                                    {backupLog.backup_job.encryption_enabled ? 'Enabled' : 'Disabled'}
                                </Badge>
                            </div>
                            {backupLog.backup_path && (
                                <div className="md:col-span-3">
                                    <p className="text-sm font-medium mb-2">Backup File Path:</p>
                                    <p className="text-sm font-mono bg-muted p-2 rounded break-all">
                                        {backupLog.backup_path}
                                    </p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Actions</CardTitle>
                        <CardDescription>Available actions for this backup log</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex space-x-4">
                            <Link href={route('backup-jobs.show', backupLog.backup_job.id)}>
                                <Button variant="outline">
                                    <FileText className="mr-2 h-4 w-4" />
                                    View Backup Job
                                </Button>
                            </Link>
                            
                            {backupLog.status === 'completed' && backupLog.backup_path && (
                                <Button onClick={handleDownload}>
                                    <Download className="mr-2 h-4 w-4" />
                                    Download Backup
                                </Button>
                            )}
                            
                            {backupLog.status === 'failed' && (
                                <Button onClick={handleRetry} variant="outline">
                                    <RotateCcw className="mr-2 h-4 w-4" />
                                    Retry Backup
                                </Button>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
