import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, TrendingUp, Calendar } from 'lucide-react';

interface ExpenseStatsProps {
    currentMonthTotal: string;
    totalExpenses: number;
    month: number;
    year: number;
}

const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
];

export default function ExpenseStats({ 
    currentMonthTotal, 
    totalExpenses, 
    month, 
    year 
}: ExpenseStatsProps) {
    const formatCurrency = (value: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(parseFloat(value));
    };

    const calculateAverage = () => {
        if (totalExpenses === 0) return '0';
        return (parseFloat(currentMonthTotal) / totalExpenses).toString();
    };

    return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Current Month Total</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(currentMonthTotal)}</div>
                    <p className="text-xs text-muted-foreground">
                        {monthNames[month - 1]} {year}
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">{totalExpenses}</div>
                    <p className="text-xs text-muted-foreground">
                        {monthNames[month - 1]} {year}
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Average per Expense</CardTitle>
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">
                        {formatCurrency(calculateAverage())}
                    </div>
                    <p className="text-xs text-muted-foreground">
                        This month
                    </p>
                </CardContent>
            </Card>
        </div>
    );
}
